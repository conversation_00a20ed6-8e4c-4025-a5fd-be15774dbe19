# MyNextTrip Backend Deployment Guide

## Quick Start

### 1. Setup
\`\`\`bash
# Clone and setup
git clone <repository-url>
cd mynexttrip-backend
npm run setup
\`\`\`

### 2. Environment Configuration
\`\`\`bash
# Copy and configure environment variables
cp .env.example .env
# Edit .env with your actual values
\`\`\`

### 3. Development
\`\`\`bash
# Start development environment
npm run dev
\`\`\`

### 4. Testing
\`\`\`bash
# Run all tests
npm run test:all
\`\`\`

## Production Deployment

### Docker Deployment
\`\`\`bash
# Build and run with Docker
docker build -t mynexttrip-backend .
docker run -p 3000:3000 mynexttrip-backend
\`\`\`

### Vercel Deployment
\`\`\`bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
\`\`\`

## API Endpoints

- **Health Check**: `GET /api/v1/health`
- **API Documentation**: `GET /api/docs`
- **Authentication**: `POST /api/v1/auth/login`
- **Search Hotels**: `GET /api/v1/search/hotels`
- **Create Booking**: `POST /api/v1/bookings`

## Database

- **Migrations**: `npm run db:migrate`
- **Seed Data**: `npm run db:seed`
- **Studio**: `npm run db:studio`

## Monitoring

- Health checks available at `/api/v1/health`
- Swagger documentation at `/api/docs`
- Prisma Studio for database management

## Support

For issues and support, contact the development team or check the API documentation.
