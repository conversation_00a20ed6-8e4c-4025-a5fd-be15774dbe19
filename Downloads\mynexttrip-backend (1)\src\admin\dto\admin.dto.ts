import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsEnum, IsBoolean, IsNumber, <PERSON>Int, <PERSON>, <PERSON>, Length } from "class-validator"
import { Type } from "class-transformer"
import { UserStatus, UserRole } from "@prisma/client"

export class AdminQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20

  @ApiPropertyOptional({ description: "Search query" })
  @IsOptional()
  @IsString()
  search?: string

  @ApiPropertyOptional({ enum: UserStatus, description: "Filter by status" })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus

  @ApiPropertyOptional({ enum: UserRole, description: "Filter by role" })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole

  @ApiPropertyOptional({ description: "Start date filter" })
  @IsOptional()
  @IsString()
  startDate?: string

  @ApiPropertyOptional({ description: "End date filter" })
  @IsOptional()
  @IsString()
  endDate?: string
}

export class UserManagementDto {
  @ApiPropertyOptional({ description: "First name" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  firstName?: string

  @ApiPropertyOptional({ description: "Last name" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  lastName?: string

  @ApiPropertyOptional({ description: "Phone number" })
  @IsOptional()
  @IsString()
  phone?: string

  @ApiPropertyOptional({ enum: UserRole, description: "User role" })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole

  @ApiPropertyOptional({ enum: UserStatus, description: "User status" })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus

  @ApiPropertyOptional({ description: "Email verified" })
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean

  @ApiPropertyOptional({ description: "Phone verified" })
  @IsOptional()
  @IsBoolean()
  phoneVerified?: boolean
}

export class PlatformConfigDto {
  @ApiPropertyOptional({ description: "Platform commission percentage" })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(50)
  platformCommission?: number

  @ApiPropertyOptional({ description: "Tax rate percentage" })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(50)
  taxRate?: number

  @ApiPropertyOptional({ description: "Payout delay in days" })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(30)
  payoutDelay?: number

  @ApiPropertyOptional({ description: "Maximum refund days" })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(365)
  maxRefundDays?: number

  @ApiPropertyOptional({ description: "Maintenance mode" })
  @IsOptional()
  @IsBoolean()
  maintenanceMode?: boolean

  @ApiPropertyOptional({ description: "Allow new registrations" })
  @IsOptional()
  @IsBoolean()
  allowNewRegistrations?: boolean

  @ApiPropertyOptional({ description: "Require vendor approval" })
  @IsOptional()
  @IsBoolean()
  requireVendorApproval?: boolean

  @ApiPropertyOptional({ description: "Require listing approval" })
  @IsOptional()
  @IsBoolean()
  requireListingApproval?: boolean
}
