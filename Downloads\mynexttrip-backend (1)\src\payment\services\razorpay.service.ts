import { Injectable } from "@nestjs/common"
import <PERSON><PERSON><PERSON><PERSON> from "razorpay"
import crypto from "crypto"

@Injectable()
export class RazorpayService {
  private razorpay: Razorpay

  constructor() {
    this.razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    })
  }

  async createOrder(params: {
    amount: number
    currency: string
    receipt: string
    notes?: Record<string, string>
  }) {
    return this.razorpay.orders.create(params)
  }

  async getOrder(orderId: string) {
    return this.razorpay.orders.fetch(orderId)
  }

  async createRefund(params: { paymentId: string; amount: number; notes?: Record<string, string> }) {
    return this.razorpay.payments.refund(params.paymentId, {
      amount: params.amount,
      notes: params.notes,
    })
  }

  async verifyWebhookSignature(payload: string, signature: string): boolean {
    const expectedSignature = crypto
      .createHmac("sha256", process.env.RAZORPAY_WEBHOOK_SECRET)
      .update(payload)
      .digest("hex")

    return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))
  }

  async getPayment(paymentId: string) {
    return this.razorpay.payments.fetch(paymentId)
  }
}
