"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeService = void 0;
const common_1 = require("@nestjs/common");
const stripe_1 = require("stripe");
let StripeService = class StripeService {
    constructor() {
        this.stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
            apiVersion: "2023-10-16",
        });
    }
    async createPaymentIntent(params) {
        return this.stripe.paymentIntents.create(params);
    }
    async confirmPaymentIntent(paymentIntentId, paymentMethodId) {
        const params = {};
        if (paymentMethodId) {
            params.payment_method = paymentMethodId;
        }
        return this.stripe.paymentIntents.confirm(paymentIntentId, params);
    }
    async createCustomer(params) {
        return this.stripe.customers.create(params);
    }
    async getCustomerPaymentMethods(customerId) {
        const paymentMethods = await this.stripe.paymentMethods.list({
            customer: customerId,
            type: "card",
        });
        return paymentMethods.data;
    }
    async attachPaymentMethodToCustomer(paymentMethodId, customerId) {
        return this.stripe.paymentMethods.attach(paymentMethodId, {
            customer: customerId,
        });
    }
    async setDefaultPaymentMethod(customerId, paymentMethodId) {
        return this.stripe.customers.update(customerId, {
            invoice_settings: {
                default_payment_method: paymentMethodId,
            },
        });
    }
    async createRefund(params) {
        return this.stripe.refunds.create(params);
    }
    async constructWebhookEvent(payload, signature) {
        return this.stripe.webhooks.constructEvent(payload, signature, process.env.STRIPE_WEBHOOK_SECRET);
    }
    async retrievePaymentIntent(paymentIntentId) {
        return this.stripe.paymentIntents.retrieve(paymentIntentId);
    }
};
exports.StripeService = StripeService;
exports.StripeService = StripeService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], StripeService);
//# sourceMappingURL=stripe.service.js.map