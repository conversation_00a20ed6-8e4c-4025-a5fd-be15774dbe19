import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import type { PrismaService } from "../../common/services/prisma.service"
import type { TypesenseService } from "../../common/services/typesense.service"
import type { CreateHotelDto, UpdateHotelDto, HotelQueryDto } from "./dto/hotel.dto"
import { HotelStatus } from "@prisma/client"
import type { Express } from "express"

@Injectable()
export class HotelService {
  constructor(
    private prisma: PrismaService,
    private typesense: TypesenseService,
  ) {}

  async createHotel(userId: string, createHotelDto: CreateHotelDto) {
    // Get vendor
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    // Create hotel
    const hotel = await this.prisma.hotel.create({
      data: {
        vendorId: vendor.id,
        name: createHotelDto.name,
        description: createHotelDto.description,
        address: createHotelDto.address,
        city: createHotelDto.city,
        state: createHotelDto.state,
        pincode: createHotelDto.pincode,
        latitude: createHotelDto.latitude,
        longitude: createHotelDto.longitude,
        starRating: createHotelDto.starRating,
        amenities: createHotelDto.amenities,
        checkInTime: createHotelDto.checkInTime,
        checkOutTime: createHotelDto.checkOutTime,
        cancellationPolicy: createHotelDto.cancellationPolicy,
        status: HotelStatus.DRAFT,
        rooms: {
          create:
            createHotelDto.rooms?.map((room) => ({
              type: room.type,
              description: room.description,
              maxOccupancy: room.maxOccupancy,
              basePrice: room.basePrice,
              amenities: room.amenities,
              totalRooms: room.totalRooms,
              availableRooms: room.totalRooms,
            })) || [],
        },
      },
      include: {
        rooms: true,
        images: true,
        vendor: {
          select: {
            businessName: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
      },
    })

    // Index in Typesense for search
    await this.indexHotelInTypesense(hotel)

    return hotel
  }

  async getVendorHotels(userId: string, query: HotelQueryDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const { page = 1, limit = 10, status, city, search } = query
    const skip = (page - 1) * limit

    const where: any = {
      vendorId: vendor.id,
    }

    if (status) {
      where.status = status
    }

    if (city) {
      where.city = {
        contains: city,
        mode: "insensitive",
      }
    }

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          description: {
            contains: search,
            mode: "insensitive",
          },
        },
      ]
    }

    const [hotels, total] = await Promise.all([
      this.prisma.hotel.findMany({
        where,
        include: {
          images: {
            take: 1,
            orderBy: {
              isPrimary: "desc",
            },
          },
          rooms: {
            select: {
              id: true,
              type: true,
              basePrice: true,
              maxOccupancy: true,
            },
          },
          _count: {
            select: {
              bookings: true,
              reviews: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      this.prisma.hotel.count({ where }),
    ])

    return {
      hotels,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getHotelById(userId: string, hotelId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const hotel = await this.prisma.hotel.findFirst({
      where: {
        id: hotelId,
        vendorId: vendor.id,
      },
      include: {
        images: {
          orderBy: {
            isPrimary: "desc",
          },
        },
        rooms: true,
        reviews: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                profilePicture: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
        _count: {
          select: {
            bookings: true,
            reviews: true,
          },
        },
      },
    })

    if (!hotel) {
      throw new NotFoundException("Hotel not found")
    }

    return hotel
  }

  async updateHotel(userId: string, hotelId: string, updateHotelDto: UpdateHotelDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const hotel = await this.prisma.hotel.findFirst({
      where: {
        id: hotelId,
        vendorId: vendor.id,
      },
    })

    if (!hotel) {
      throw new NotFoundException("Hotel not found")
    }

    const updatedHotel = await this.prisma.hotel.update({
      where: { id: hotelId },
      data: updateHotelDto,
      include: {
        rooms: true,
        images: true,
        vendor: {
          select: {
            businessName: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
      },
    })

    // Update in Typesense
    await this.indexHotelInTypesense(updatedHotel)

    return updatedHotel
  }

  async deleteHotel(userId: string, hotelId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const hotel = await this.prisma.hotel.findFirst({
      where: {
        id: hotelId,
        vendorId: vendor.id,
      },
    })

    if (!hotel) {
      throw new NotFoundException("Hotel not found")
    }

    // Check for active bookings
    const activeBookings = await this.prisma.booking.count({
      where: {
        hotelId,
        status: {
          in: ["PENDING", "CONFIRMED"],
        },
      },
    })

    if (activeBookings > 0) {
      throw new BadRequestException("Cannot delete hotel with active bookings")
    }

    // Delete from database
    await this.prisma.hotel.delete({
      where: { id: hotelId },
    })

    // Remove from Typesense
    try {
      await this.typesense.client.collections("hotels").documents(hotelId).delete()
    } catch (error) {
      console.error("Error removing hotel from Typesense:", error)
    }
  }

  async uploadHotelImages(userId: string, hotelId: string, files: Express.Multer.File[]) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const hotel = await this.prisma.hotel.findFirst({
      where: {
        id: hotelId,
        vendorId: vendor.id,
      },
    })

    if (!hotel) {
      throw new NotFoundException("Hotel not found")
    }

    // In a real implementation, you would upload files to cloud storage
    // For now, we'll simulate the upload process
    const images = await Promise.all(
      files.map(async (file, index) => {
        return this.prisma.hotelImage.create({
          data: {
            hotelId,
            url: `/uploads/hotels/${hotelId}/${file.filename}`, // Simulated URL
            altText: `${hotel.name} - Image ${index + 1}`,
            isPrimary: index === 0, // First image is primary
          },
        })
      }),
    )

    return images
  }

  async deleteHotelImage(userId: string, hotelId: string, imageId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const hotel = await this.prisma.hotel.findFirst({
      where: {
        id: hotelId,
        vendorId: vendor.id,
      },
    })

    if (!hotel) {
      throw new NotFoundException("Hotel not found")
    }

    const image = await this.prisma.hotelImage.findFirst({
      where: {
        id: imageId,
        hotelId,
      },
    })

    if (!image) {
      throw new NotFoundException("Image not found")
    }

    await this.prisma.hotelImage.delete({
      where: { id: imageId },
    })

    // In a real implementation, you would also delete the file from cloud storage
  }

  async updateHotelStatus(userId: string, hotelId: string, status: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const hotel = await this.prisma.hotel.findFirst({
      where: {
        id: hotelId,
        vendorId: vendor.id,
      },
    })

    if (!hotel) {
      throw new NotFoundException("Hotel not found")
    }

    const updatedHotel = await this.prisma.hotel.update({
      where: { id: hotelId },
      data: { status: status as HotelStatus },
      include: {
        rooms: true,
        images: true,
      },
    })

    // Update in Typesense
    await this.indexHotelInTypesense(updatedHotel)

    return updatedHotel
  }

  private async indexHotelInTypesense(hotel: any) {
    try {
      const document = {
        id: hotel.id,
        name: hotel.name,
        description: hotel.description,
        city: hotel.city,
        state: hotel.state,
        starRating: hotel.starRating,
        amenities: hotel.amenities,
        averageRating: hotel.averageRating || 0,
        totalReviews: hotel.totalReviews || 0,
        minPrice: hotel.rooms?.length > 0 ? Math.min(...hotel.rooms.map((r) => r.basePrice)) : 0,
        maxPrice: hotel.rooms?.length > 0 ? Math.max(...hotel.rooms.map((r) => r.basePrice)) : 0,
        latitude: hotel.latitude,
        longitude: hotel.longitude,
        status: hotel.status,
        type: "hotel",
      }

      await this.typesense.client.collections("listings").documents().upsert(document)
    } catch (error) {
      console.error("Error indexing hotel in Typesense:", error)
    }
  }
}
