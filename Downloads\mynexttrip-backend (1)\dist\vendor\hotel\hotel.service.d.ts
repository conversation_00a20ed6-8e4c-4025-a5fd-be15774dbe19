import type { PrismaService } from "../../common/services/prisma.service";
import type { TypesenseService } from "../../common/services/typesense.service";
import type { CreateHotelDto, UpdateHotelDto, HotelQueryDto } from "./dto/hotel.dto";
import type { Express } from "express";
export declare class HotelService {
    private prisma;
    private typesense;
    constructor(prisma: PrismaService, typesense: TypesenseService);
    createHotel(userId: string, createHotelDto: CreateHotelDto): Promise<any>;
    getVendorHotels(userId: string, query: HotelQueryDto): Promise<{
        hotels: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getHotelById(userId: string, hotelId: string): Promise<any>;
    updateHotel(userId: string, hotelId: string, updateHotelDto: UpdateHotelDto): Promise<any>;
    deleteHotel(userId: string, hotelId: string): Promise<void>;
    uploadHotelImages(userId: string, hotelId: string, files: Express.Multer.File[]): Promise<any>;
    deleteHotelImage(userId: string, hotelId: string, imageId: string): Promise<void>;
    updateHotelStatus(userId: string, hotelId: string, status: string): Promise<any>;
    private indexHotelInTypesense;
}
