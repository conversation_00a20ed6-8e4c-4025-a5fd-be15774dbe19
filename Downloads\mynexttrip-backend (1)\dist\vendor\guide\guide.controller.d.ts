import type { GuideService } from "./guide.service";
import type { CreateGuideDto, UpdateGuideDto, GuideQueryDto } from "./dto/guide.dto";
import type { Express } from "express";
export declare class GuideController {
    private readonly guideService;
    constructor(guideService: GuideService);
    createGuide(req: any, createGuideDto: CreateGuideDto): Promise<any>;
    getGuides(req: any, query: GuideQueryDto): Promise<any>;
    getGuide(req: any, id: string): Promise<any>;
    updateGuide(req: any, id: string, updateGuideDto: UpdateGuideDto): Promise<any>;
    deleteGuide(req: any, id: string): Promise<any>;
    uploadImages(req: any, id: string, files: Express.Multer.File[]): Promise<any>;
    updateStatus(req: any, id: string, body: {
        status: string;
    }): Promise<any>;
    getAvailability(req: any, id: string, query: {
        startDate: string;
        endDate: string;
    }): Promise<any>;
    updateAvailability(req: any, id: string, body: {
        dates: string[];
        available: boolean;
    }): Promise<any>;
}
