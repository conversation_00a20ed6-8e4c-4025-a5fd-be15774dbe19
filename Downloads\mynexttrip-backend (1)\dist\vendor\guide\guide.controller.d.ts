import type { GuideService } from "./guide.service";
import type { CreateGuideDto, UpdateGuideDto, GuideQueryDto } from "./dto/guide.dto";
import { ApiResponseDto } from "../../common/dto/api-response.dto";
import type { Express } from "express";
export declare class GuideController {
    private readonly guideService;
    constructor(guideService: GuideService);
    createGuide(req: any, createGuideDto: CreateGuideDto): Promise<ApiResponseDto<any>>;
    getGuides(req: any, query: GuideQueryDto): Promise<ApiResponseDto<{
        guides: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>>;
    getGuide(req: any, id: string): Promise<ApiResponseDto<any>>;
    updateGuide(req: any, id: string, updateGuideDto: UpdateGuideDto): Promise<ApiResponseDto<any>>;
    deleteGuide(req: any, id: string): Promise<ApiResponseDto<any>>;
    uploadImages(req: any, id: string, files: Express.Multer.File[]): Promise<ApiResponseDto<any>>;
    updateStatus(req: any, id: string, body: {
        status: string;
    }): Promise<ApiResponseDto<any>>;
    getAvailability(req: any, id: string, query: {
        startDate: string;
        endDate: string;
    }): Promise<ApiResponseDto<any>>;
    updateAvailability(req: any, id: string, body: {
        dates: string[];
        available: boolean;
    }): Promise<ApiResponseDto<any>>;
}
