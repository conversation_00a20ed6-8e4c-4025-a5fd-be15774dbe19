import { Controller, Get, Post, Put, Body, Query, UseGuards, HttpStatus } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger"
import { JwtAuthGuard } from "../common/guards/jwt-auth.guard"
import { RolesGuard } from "../common/guards/roles.guard"
import { Roles } from "../common/decorators/roles.decorator"
import type { VendorService } from "./vendor.service"
import type { CreateVendorDto, UpdateVendorDto, VendorQueryDto } from "./dto/vendor.dto"
import { ApiResponseDto } from "../common/dto/api-response.dto"

@ApiTags("Vendor Management")
@Controller("vendor")
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class VendorController {
  constructor(private readonly vendorService: VendorService) {}

  @Post("register")
  @ApiOperation({ summary: "Register as vendor" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Vendor registered successfully" })
  async registerVendor(@Body() createVendorDto: CreateVendorDto, request) {
    const vendor = await this.vendorService.registerVendor(request.user.id, createVendorDto)
    return new ApiResponseDto(true, "Vendor registered successfully", vendor)
  }

  @Get("profile")
  @Roles("vendor")
  @ApiOperation({ summary: "Get vendor profile" })
  async getProfile(request) {
    const vendor = await this.vendorService.getVendorProfile(request.user.id)
    return new ApiResponseDto(true, "Vendor profile retrieved", vendor)
  }

  @Put("profile")
  @Roles("vendor")
  @ApiOperation({ summary: "Update vendor profile" })
  async updateProfile(request, @Body() updateVendorDto: UpdateVendorDto) {
    const vendor = await this.vendorService.updateVendorProfile(request.user.id, updateVendorDto)
    return new ApiResponseDto(true, "Vendor profile updated", vendor)
  }

  @Get("dashboard/stats")
  @Roles("vendor")
  @ApiOperation({ summary: "Get vendor dashboard statistics" })
  async getDashboardStats(request) {
    const stats = await this.vendorService.getDashboardStats(request.user.id)
    return new ApiResponseDto(true, "Dashboard stats retrieved", stats)
  }

  @Get("bookings")
  @Roles("vendor")
  @ApiOperation({ summary: "Get vendor bookings" })
  async getBookings(request, @Query() query: VendorQueryDto) {
    const bookings = await this.vendorService.getVendorBookings(request.user.id, query)
    return new ApiResponseDto(true, "Bookings retrieved", bookings)
  }

  @Get("earnings")
  @Roles("vendor")
  @ApiOperation({ summary: "Get vendor earnings" })
  async getEarnings(request, @Query() query: VendorQueryDto) {
    const earnings = await this.vendorService.getVendorEarnings(request.user.id, query)
    return new ApiResponseDto(true, "Earnings retrieved", earnings)
  }
}
