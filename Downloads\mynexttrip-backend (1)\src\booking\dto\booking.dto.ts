import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsEnum, IsDateString, IsUUID, <PERSON>Int, Min, <PERSON>, Length } from "class-validator"
import { Type } from "class-transformer"
import { BookingStatus } from "@prisma/client"

export class AvailabilityCheckDto {
  @ApiProperty({ description: "Listing type", enum: ["hotel", "guide", "package"] })
  @IsEnum(["hotel", "guide", "package"])
  listingType: string

  @ApiProperty({ description: "Listing ID" })
  @IsUUID()
  listingId: string

  @ApiProperty({ description: "Check-in date (YYYY-MM-DD)" })
  @IsDateString()
  checkInDate: string

  @ApiProperty({ description: "Check-out date (YYYY-MM-DD)" })
  @IsDateString()
  checkOutDate: string

  @ApiPropertyOptional({ description: "Number of guests" })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(20)
  guests?: number

  @ApiPropertyOptional({ description: "Room ID (for hotels)" })
  @IsOptional()
  @IsUUID()
  roomId?: string
}

export class CreateBookingDto extends AvailabilityCheckDto {
  @ApiPropertyOptional({ description: "Special requests" })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  specialRequests?: string
}

export class UpdateBookingDto {
  @ApiPropertyOptional({ description: "Check-in date (YYYY-MM-DD)" })
  @IsOptional()
  @IsDateString()
  checkInDate?: string

  @ApiPropertyOptional({ description: "Check-out date (YYYY-MM-DD)" })
  @IsOptional()
  @IsDateString()
  checkOutDate?: string

  @ApiPropertyOptional({ description: "Number of guests" })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(20)
  guests?: number

  @ApiPropertyOptional({ description: "Special requests" })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  specialRequests?: string
}

export class BookingQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10

  @ApiPropertyOptional({ enum: BookingStatus, description: "Filter by status" })
  @IsOptional()
  @IsEnum(BookingStatus)
  status?: BookingStatus

  @ApiPropertyOptional({ description: "Start date filter" })
  @IsOptional()
  @IsDateString()
  startDate?: string

  @ApiPropertyOptional({ description: "End date filter" })
  @IsOptional()
  @IsDateString()
  endDate?: string
}
