"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPaginatedResult = createPaginatedResult;
function createPaginatedResult(data, total, page, limit) {
    const totalPages = Math.ceil(total / limit);
    return {
        data,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
}
//# sourceMappingURL=pagination.util.js.map