import { Controller, Get, Post, Put, UseGuards, HttpStatus } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger"
import { JwtAuthGuard } from "../common/guards/jwt-auth.guard"
import { RolesGuard } from "../common/guards/roles.guard"
import { Roles } from "../common/decorators/roles.decorator"
import type { PaymentService } from "./payment.service"
import type { CreatePaymentDto, RefundPaymentDto, PaymentQueryDto } from "./dto/payment.dto"
import { ApiResponseDto } from "../common/dto/api-response.dto"

@ApiTags("Payments")
@Controller("payments")
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post("create-intent")
  @Roles("user")
  @ApiOperation({ summary: "Create payment intent" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Payment intent created successfully" })
  async createPaymentIntent(req, createPaymentDto: CreatePaymentDto) {
    const paymentIntent = await this.paymentService.createPaymentIntent(req.user.id, createPaymentDto)
    return new ApiResponseDto(true, "Payment intent created successfully", paymentIntent)
  }

  @Post("process")
  @Roles("user")
  @ApiOperation({ summary: "Process payment" })
  @ApiResponse({ status: HttpStatus.OK, description: "Payment processed successfully" })
  async processPayment(req, body: { paymentIntentId: string; paymentMethodId?: string }) {
    const payment = await this.paymentService.processPayment(req.user.id, body.paymentIntentId, body.paymentMethodId)
    return new ApiResponseDto(true, "Payment processed successfully", payment)
  }

  @Get()
  @Roles("user", "vendor")
  @ApiOperation({ summary: "Get user payments" })
  async getPayments(req, query: PaymentQueryDto) {
    const payments = await this.paymentService.getUserPayments(req.user.id, req.user.role, query)
    return new ApiResponseDto(true, "Payments retrieved successfully", payments)
  }

  @Get(":id")
  @Roles("user", "vendor", "admin")
  @ApiOperation({ summary: "Get payment by ID" })
  async getPayment(req, id: string) {
    const payment = await this.paymentService.getPaymentById(req.user.id, id, req.user.role)
    return new ApiResponseDto(true, "Payment retrieved successfully", payment)
  }

  @Post(":id/refund")
  @Roles("admin", "vendor")
  @ApiOperation({ summary: "Process refund" })
  async processRefund(req, id: string, refundDto: RefundPaymentDto) {
    const refund = await this.paymentService.processRefund(req.user.id, id, refundDto, req.user.role)
    return new ApiResponseDto(true, "Refund processed successfully", refund)
  }

  @Get("methods/saved")
  @Roles("user")
  @ApiOperation({ summary: "Get saved payment methods" })
  async getSavedPaymentMethods(req) {
    const methods = await this.paymentService.getSavedPaymentMethods(req.user.id)
    return new ApiResponseDto(true, "Payment methods retrieved successfully", methods)
  }

  @Post("methods/save")
  @Roles("user")
  @ApiOperation({ summary: "Save payment method" })
  async savePaymentMethod(req, body: { paymentMethodId: string; isDefault?: boolean }) {
    const method = await this.paymentService.savePaymentMethod(req.user.id, body.paymentMethodId, body.isDefault)
    return new ApiResponseDto(true, "Payment method saved successfully", method)
  }

  @Put("methods/:methodId/default")
  @Roles("user")
  @ApiOperation({ summary: "Set default payment method" })
  async setDefaultPaymentMethod(req, methodId: string) {
    await this.paymentService.setDefaultPaymentMethod(req.user.id, methodId)
    return new ApiResponseDto(true, "Default payment method updated successfully")
  }

  @Get("analytics/revenue")
  @Roles("vendor")
  @ApiOperation({ summary: "Get vendor revenue analytics" })
  async getRevenueAnalytics(req, query: { startDate?: string; endDate?: string }) {
    const analytics = await this.paymentService.getVendorRevenueAnalytics(req.user.id, query.startDate, query.endDate)
    return new ApiResponseDto(true, "Revenue analytics retrieved successfully", analytics)
  }
}
