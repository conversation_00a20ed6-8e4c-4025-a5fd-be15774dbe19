import { NestFactory } from "@nestjs/core"
import { <PERSON><PERSON><PERSON>P<PERSON><PERSON>, Logger } from "@nestjs/common"
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger"
import { ConfigService } from "@nestjs/config"
import * as helmet from "helmet"
import * as compression from "compression"
import { AppModule } from "./app.module"
import { PrismaService } from "./common/services/prisma.service"

async function bootstrap() {
  const app = await NestFactory.create(AppModule)
  const configService = app.get(ConfigService)
  const logger = new Logger("Bootstrap")

  // Security middleware
  app.use(helmet())
  app.use(compression())

  // CORS configuration
  app.enableCors({
    origin: configService.get("CORS_ORIGIN") || "http://localhost:3000",
    credentials: true,
  })

  // API prefix
  const apiPrefix = configService.get("API_PREFIX") || "api/v1"
  app.setGlobalPrefix(apiPrefix)

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle("MyNextTrip API")
    .setDescription(
      "Production-grade backend for MyNextTrip travel booking platform covering Northern India with hotels, guides, and travel packages",
    )
    .setVersion("1.0")
    .addBearerAuth()
    .addTag("auth", "Authentication endpoints")
    .addTag("users", "User management")
    .addTag("vendors", "Vendor management and onboarding")
    .addTag("hotels", "Hotel listing management")
    .addTag("guides", "Tour guide management")
    .addTag("packages", "Travel package management")
    .addTag("search", "Search and filtering")
    .addTag("bookings", "Booking management")
    .addTag("payments", "Payment processing (Stripe & Razorpay)")
    .addTag("reviews", "Review and rating system")
    .addTag("admin", "Admin operations and analytics")
    .addServer("http://localhost:3000", "Development server")
    .addServer("https://api.mynexttrip.com", "Production server")
    .build()

  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup("api/docs", app, document, {
    customSiteTitle: "MyNextTrip API Documentation",
    customfavIcon: "/favicon.ico",
    customJs: [
      "https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-standalone-preset.min.js",
    ],
    customCssUrl: ["https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css"],
  })

  // Global validation pipe - NestJS method, not React hook
  const validationPipe = new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
  })
  app.useGlobalPipes(validationPipe)

  // Prisma connection
  const prismaService = app.get(PrismaService)
  await prismaService.enableShutdownHooks(app)

  const port = configService.get("PORT") || 5000
  await app.listen(port)

  logger.log(`🚀 MyNextTrip Backend is running on: http://localhost:${port}`)
  logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`)
  logger.log(`🗄️ Database Studio: Run 'npm run db:studio' to access Prisma Studio`)
  logger.log(`🔍 Health Check: http://localhost:${port}/api/v1/health`)

  const nodeEnv = configService.get("NODE_ENV") || "development"
  logger.log(`🌍 Environment: ${nodeEnv}`)

  if (nodeEnv === "production") {
    logger.log(`🔒 Production mode: Enhanced security and performance optimizations enabled`)
  } else {
    logger.log(`🛠️ Development mode: Debug features and hot reload enabled`)
  }
}

bootstrap().catch((error) => {
  console.error("❌ MyNextTrip Backend failed to start:", error)
  process.exit(1)
})
