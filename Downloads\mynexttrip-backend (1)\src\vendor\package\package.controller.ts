import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
  UseInterceptors,
} from "@nestjs/common"
import { FilesInterceptor } from "@nestjs/platform-express"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from "@nestjs/swagger"
import { JwtAuthGuard } from "../../common/guards/jwt-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles } from "../../common/decorators/roles.decorator"
import type { PackageService } from "./package.service"
import { ApiResponseDto } from "../../common/dto/api-response.dto"
import type { Express } from "express"

@ApiTags("Package Management")
@Controller("vendor/packages")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles("vendor")
@ApiBearerAuth()
export class PackageController {
  constructor(private readonly packageService: PackageService) {}

  @Post()
  @ApiOperation({ summary: "Create new package listing" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Package created successfully" })
  async createPackage(req, createPackageDto) {
    const packageData = await this.packageService.createPackage(req.user.id, createPackageDto)
    return new ApiResponseDto(true, "Package created successfully", packageData)
  }

  @Get()
  @ApiOperation({ summary: "Get vendor packages" })
  async getPackages(req, query) {
    const packages = await this.packageService.getVendorPackages(req.user.id, query)
    return new ApiResponseDto(true, "Packages retrieved successfully", packages)
  }

  @Get(":id")
  @ApiOperation({ summary: "Get package by ID" })
  async getPackage(req, @Param('id', ParseUUIDPipe) id: string) {
    const packageData = await this.packageService.getPackageById(req.user.id, id)
    return new ApiResponseDto(true, "Package retrieved successfully", packageData)
  }

  @Put(":id")
  @ApiOperation({ summary: "Update package" })
  async updatePackage(req, @Param('id', ParseUUIDPipe) id: string, updatePackageDto) {
    const packageData = await this.packageService.updatePackage(req.user.id, id, updatePackageDto)
    return new ApiResponseDto(true, "Package updated successfully", packageData)
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete package" })
  async deletePackage(req, @Param('id', ParseUUIDPipe) id: string) {
    await this.packageService.deletePackage(req.user.id, id)
    return new ApiResponseDto(true, "Package deleted successfully")
  }

  @Post(":id/images")
  @UseInterceptors(FilesInterceptor("images", 10))
  @ApiConsumes("multipart/form-data")
  @ApiOperation({ summary: "Upload package images" })
  async uploadImages(req, @Param('id', ParseUUIDPipe) id: string, files: Express.Multer.File[]) {
    const images = await this.packageService.uploadPackageImages(req.user.id, id, files)
    return new ApiResponseDto(true, "Images uploaded successfully", images)
  }

  @Put(":id/status")
  @ApiOperation({ summary: "Update package status" })
  async updateStatus(req, @Param('id', ParseUUIDPipe) id: string, body) {
    const packageData = await this.packageService.updatePackageStatus(req.user.id, id, body.status)
    return new ApiResponseDto(true, "Package status updated successfully", packageData)
  }
}
