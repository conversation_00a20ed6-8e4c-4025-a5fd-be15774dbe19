version: '3.8'

services:
  postgres-test:
    image: postgres:15
    container_name: mynexttrip-postgres-test
    environment:
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test
      POSTGRES_DB: mynexttrip_test
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data

  redis-test:
    image: redis:7-alpine
    container_name: mynexttrip-redis-test
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_test_data:/data

volumes:
  postgres_test_data:
  redis_test_data:
