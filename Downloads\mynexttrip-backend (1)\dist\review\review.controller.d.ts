import type { ReviewService } from "./review.service";
import type { CreateReviewDto, UpdateReviewDto, ReviewQueryDto, ReviewModerationDto } from "./dto/review.dto";
export declare class ReviewController {
    private readonly reviewService;
    constructor(reviewService: ReviewService);
    createReview(req: any, createReviewDto: CreateReviewDto): Promise<any>;
    getListingReviews(listingId: string, query: ReviewQueryDto): Promise<any>;
    getUserReviews(req: any, userId: string, query: ReviewQueryDto): Promise<any>;
    getReview(id: string): Promise<any>;
    updateReview(req: any, id: string, updateReviewDto: UpdateReviewDto): Promise<any>;
    deleteReview(req: any, id: string): Promise<any>;
    markHelpful(req: any, id: string): Promise<any>;
    reportReview(req: any, id: string, body: {
        reason: string;
    }): Promise<any>;
    getPendingReviews(query: ReviewQueryDto): Promise<any>;
    moderateReview(id: string, moderationDto: ReviewModerationDto): Promise<any>;
    getReportedReviews(query: ReviewQueryDto): Promise<any>;
}
