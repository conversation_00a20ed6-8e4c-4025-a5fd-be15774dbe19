import type { ReviewService } from "./review.service";
import type { CreateReviewDto, UpdateReviewDto, ReviewQueryDto, ReviewModerationDto } from "./dto/review.dto";
import { ApiResponseDto } from "../common/dto/api-response.dto";
export declare class ReviewController {
    private readonly reviewService;
    constructor(reviewService: ReviewService);
    createReview(req: any, createReviewDto: CreateReviewDto): Promise<ApiResponseDto<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>>;
    getListingReviews(listingId: string, query: ReviewQueryDto): Promise<ApiResponseDto<{
        reviews: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
        summary: {
            averageRating: any;
            totalReviews: any;
            ratingDistribution: any;
        };
    }>>;
    getUserReviews(req: any, userId: string, query: ReviewQueryDto): Promise<ApiResponseDto<{
        reviews: {
            title: string | null;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            images: string[];
            rating: number;
            bookingId: string;
            comment: string;
            isApproved: boolean;
            moderatedAt: Date | null;
            moderationNotes: string | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    getReview(id: string): Promise<ApiResponseDto<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>>;
    updateReview(req: any, id: string, updateReviewDto: UpdateReviewDto): Promise<ApiResponseDto<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>>;
    deleteReview(req: any, id: string): Promise<ApiResponseDto<any>>;
    markHelpful(req: any, id: string): Promise<ApiResponseDto<{
        helpful: boolean;
    }>>;
    reportReview(req: any, id: string, body: {
        reason: string;
    }): Promise<ApiResponseDto<any>>;
    getPendingReviews(query: ReviewQueryDto): Promise<ApiResponseDto<{
        reviews: {
            title: string | null;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            images: string[];
            rating: number;
            bookingId: string;
            comment: string;
            isApproved: boolean;
            moderatedAt: Date | null;
            moderationNotes: string | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    moderateReview(id: string, moderationDto: ReviewModerationDto): Promise<ApiResponseDto<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>>;
    getReportedReviews(query: ReviewQueryDto): Promise<ApiResponseDto<{
        reports: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>>;
}
