import { BookingStatus } from "@prisma/client";
export declare class AvailabilityCheckDto {
    listingType: string;
    listingId: string;
    checkInDate: string;
    checkOutDate: string;
    guests?: number;
    roomId?: string;
}
export declare class CreateBookingDto extends AvailabilityCheckDto {
    specialRequests?: string;
}
export declare class UpdateBookingDto {
    checkInDate?: string;
    checkOutDate?: string;
    guests?: number;
    specialRequests?: string;
}
export declare class BookingQueryDto {
    page?: number;
    limit?: number;
    status?: BookingStatus;
    startDate?: string;
    endDate?: string;
}
