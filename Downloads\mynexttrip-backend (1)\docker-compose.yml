version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mynexttrip-postgres
    environment:
      POSTGRES_DB: mynexttrip
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - mynexttrip-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: mynexttrip-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mynexttrip-network

  # Typesense Search Engine
  typesense:
    image: typesense/typesense:0.25.2
    container_name: mynexttrip-typesense
    environment:
      TYPESENSE_DATA_DIR: /data
      TYPESENSE_API_KEY: xyz
    ports:
      - "8108:8108"
    volumes:
      - typesense_data:/data
    networks:
      - mynexttrip-network

  # Backend API (Development)
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: mynexttrip-api
    environment:
      DATABASE_URL: ********************************************/mynexttrip?schema=public
      REDIS_URL: redis://redis:6379
      TYPESENSE_HOST: typesense
      TYPESENSE_PORT: 8108
      TYPESENSE_PROTOCOL: http
      TYPESENSE_API_KEY: xyz
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - typesense
    networks:
      - mynexttrip-network
    command: npm run start:dev

volumes:
  postgres_data:
  redis_data:
  typesense_data:

networks:
  mynexttrip-network:
    driver: bridge
