"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypesenseService = void 0;
const common_1 = require("@nestjs/common");
const typesense_1 = require("typesense");
let TypesenseService = class TypesenseService {
    constructor(configService) {
        this.configService = configService;
        this.client = new typesense_1.Client({
            nodes: [
                {
                    host: this.configService.get("TYPESENSE_HOST") || "localhost",
                    port: Number.parseInt(this.configService.get("TYPESENSE_PORT")) || 8108,
                    protocol: this.configService.get("TYPESENSE_PROTOCOL") || "http",
                },
            ],
            apiKey: this.configService.get("TYPESENSE_API_KEY") || "xyz",
            connectionTimeoutSeconds: 2,
        });
    }
    getClient() {
        return this.client;
    }
    async createCollection(schema) {
        try {
            await this.client.collections().create(schema);
        }
        catch (error) {
            if (error.httpStatus !== 409) {
                throw error;
            }
        }
    }
    async indexDocument(collectionName, document) {
        return this.client.collections(collectionName).documents().create(document);
    }
    async updateDocument(collectionName, documentId, document) {
        return this.client.collections(collectionName).documents(documentId).update(document);
    }
    async deleteDocument(collectionName, documentId) {
        return this.client.collections(collectionName).documents(documentId).delete();
    }
    async search(collectionName, searchParameters) {
        return this.client.collections(collectionName).documents().search(searchParameters);
    }
};
exports.TypesenseService = TypesenseService;
exports.TypesenseService = TypesenseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function])
], TypesenseService);
//# sourceMappingURL=typesense.service.js.map