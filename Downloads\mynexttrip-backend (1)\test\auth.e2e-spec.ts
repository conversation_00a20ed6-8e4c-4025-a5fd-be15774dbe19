import { Test, type TestingModule } from "@nestjs/testing"
import type { INestApplication } from "@nestjs/common"
import * as request from "supertest"
import { AppModule } from "../src/app.module"
import { PrismaService } from "../src/common/services/prisma.service"
import { describe, beforeEach, afterEach, it, expect } from "@jest/globals"

describe("AuthController (e2e)", () => {
  let app: INestApplication
  let prismaService: PrismaService

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile()

    app = moduleFixture.createNestApplication()
    prismaService = moduleFixture.get<PrismaService>(PrismaService)

    await app.init()
  })

  afterEach(async () => {
    // Clean up test data
    await prismaService.user.deleteMany({})
    await app.close()
  })

  describe("/auth/register (POST)", () => {
    it("should register a new user", () => {
      return request(app.getHttpServer())
        .post("/auth/register")
        .send({
          email: "<EMAIL>",
          password: "password123",
          firstName: "John",
          lastName: "Doe",
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty("user")
          expect(res.body.user.email).toBe("<EMAIL>")
        })
    })

    it("should return 409 for duplicate email", async () => {
      // First registration
      await request(app.getHttpServer()).post("/auth/register").send({
        email: "<EMAIL>",
        password: "password123",
        firstName: "John",
        lastName: "Doe",
      })

      // Duplicate registration
      return request(app.getHttpServer())
        .post("/auth/register")
        .send({
          email: "<EMAIL>",
          password: "password123",
          firstName: "Jane",
          lastName: "Doe",
        })
        .expect(409)
    })
  })

  describe("/auth/login (POST)", () => {
    beforeEach(async () => {
      // Create a test user
      await request(app.getHttpServer()).post("/auth/register").send({
        email: "<EMAIL>",
        password: "password123",
        firstName: "John",
        lastName: "Doe",
      })
    })

    it("should login successfully", () => {
      return request(app.getHttpServer())
        .post("/auth/login")
        .send({
          email: "<EMAIL>",
          password: "password123",
        })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty("accessToken")
          expect(res.body).toHaveProperty("user")
        })
    })

    it("should return 401 for invalid credentials", () => {
      return request(app.getHttpServer())
        .post("/auth/login")
        .send({
          email: "<EMAIL>",
          password: "wrongpassword",
        })
        .expect(401)
    })
  })
})
