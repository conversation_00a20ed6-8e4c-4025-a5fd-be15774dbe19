import { Controller, Get } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger"
import { Public } from "../decorators/public.decorator"
import type { PrismaService } from "../services/prisma.service"
import type { RedisService } from "../services/redis.service"

@ApiTags("health")
@Controller("health")
export class HealthController {
  constructor(
    private prismaService: PrismaService,
    private redisService: RedisService,
  ) {}

  @Get()
  @Public()
  @ApiOperation({ summary: "Health check endpoint" })
  @ApiResponse({ status: 200, description: "Service is healthy" })
  async healthCheck() {
    const checks = {
      database: false,
      redis: false,
      timestamp: new Date().toISOString(),
    }

    try {
      await this.prismaService.$queryRaw`SELECT 1`
      checks.database = true
    } catch (error) {
      console.error("Database health check failed:", error)
    }

    try {
      await this.redisService.set("health-check", "ok", 10)
      checks.redis = true
    } catch (error) {
      console.error("Redis health check failed:", error)
    }

    const isHealthy = checks.database && checks.redis

    return {
      status: isHealthy ? "healthy" : "unhealthy",
      checks,
    }
  }
}
