import type { VendorService } from "./vendor.service";
import type { CreateVendorDto, UpdateVendorDto, VendorQueryDto } from "./dto/vendor.dto";
export declare class VendorController {
    private readonly vendorService;
    constructor(vendorService: VendorService);
    registerVendor(createVendorDto: CreateVendorDto, request: any): Promise<any>;
    getProfile(request: any): Promise<any>;
    updateProfile(request: any, updateVendorDto: UpdateVendorDto): Promise<any>;
    getDashboardStats(request: any): Promise<any>;
    getBookings(request: any, query: VendorQueryDto): Promise<any>;
    getEarnings(request: any, query: VendorQueryDto): Promise<any>;
}
