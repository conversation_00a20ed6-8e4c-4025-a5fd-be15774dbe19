import type { VendorService } from "./vendor.service";
import type { CreateVendorDto, UpdateVendorDto, VendorQueryDto } from "./dto/vendor.dto";
import { ApiResponseDto } from "../common/dto/api-response.dto";
export declare class VendorController {
    private readonly vendorService;
    constructor(vendorService: VendorService);
    registerVendor(createVendorDto: CreateVendorDto, request: any): Promise<ApiResponseDto<{
        description: string | null;
        businessName: string;
        businessType: import(".prisma/client").$Enums.VendorType;
        gstNumber: string | null;
        panNumber: string | null;
        status: import(".prisma/client").$Enums.VendorStatus;
        id: string;
        userId: string;
        logo: string | null;
        businessAddress: import("@prisma/client/runtime/library").JsonValue;
        bankDetails: import("@prisma/client/runtime/library").JsonValue;
        documentsUploaded: boolean;
        verificationNotes: string | null;
        verifiedAt: Date | null;
        commissionRate: number;
        createdAt: Date;
        updatedAt: Date;
    }>>;
    getProfile(request: any): Promise<ApiResponseDto<{
        description: string | null;
        businessName: string;
        businessType: import(".prisma/client").$Enums.VendorType;
        gstNumber: string | null;
        panNumber: string | null;
        status: import(".prisma/client").$Enums.VendorStatus;
        id: string;
        userId: string;
        logo: string | null;
        businessAddress: import("@prisma/client/runtime/library").JsonValue;
        bankDetails: import("@prisma/client/runtime/library").JsonValue;
        documentsUploaded: boolean;
        verificationNotes: string | null;
        verifiedAt: Date | null;
        commissionRate: number;
        createdAt: Date;
        updatedAt: Date;
    }>>;
    updateProfile(request: any, updateVendorDto: UpdateVendorDto): Promise<ApiResponseDto<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
    } & {
        description: string | null;
        businessName: string;
        businessType: import(".prisma/client").$Enums.VendorType;
        gstNumber: string | null;
        panNumber: string | null;
        status: import(".prisma/client").$Enums.VendorStatus;
        id: string;
        userId: string;
        logo: string | null;
        businessAddress: import("@prisma/client/runtime/library").JsonValue;
        bankDetails: import("@prisma/client/runtime/library").JsonValue;
        documentsUploaded: boolean;
        verificationNotes: string | null;
        verifiedAt: Date | null;
        commissionRate: number;
        createdAt: Date;
        updatedAt: Date;
    }>>;
    getDashboardStats(request: any): Promise<ApiResponseDto<{
        totalListings: any;
        totalBookings: any;
        totalEarnings: any;
        pendingBookings: any;
        recentBookings: any;
    }>>;
    getBookings(request: any, query: VendorQueryDto): Promise<ApiResponseDto<{
        bookings: {
            status: import(".prisma/client").$Enums.BookingStatus;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            availabilitySlotId: string | null;
            checkIn: Date;
            checkOut: Date | null;
            guests: number;
            totalAmount: number;
            currency: string;
            guestDetails: import("@prisma/client/runtime/library").JsonValue;
            specialRequests: string | null;
            paymentIntentId: string | null;
            paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
            confirmationCode: string;
            confirmedAt: Date | null;
            cancelledAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    getEarnings(request: any, query: VendorQueryDto): Promise<ApiResponseDto<{
        totalEarnings: number;
        totalBookings: number;
        monthlyEarnings: unknown;
    }>>;
}
