#!/bin/bash

# MyNextTrip Backend Deployment Script

echo "🚀 Deploying MyNextTrip Backend..."

# Check environment
if [ -z "$NODE_ENV" ]; then
    echo "❌ NODE_ENV not set. Please set NODE_ENV=production"
    exit 1
fi

# Build the application
echo "🔨 Building application..."
npm run build

# Run database migrations
echo "🗄️ Running database migrations..."
npm run db:migrate:deploy

# Start the application
echo "🎯 Starting production server..."
npm run start:prod
