"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PasswordUtil = void 0;
const bcrypt = require("bcryptjs");
class PasswordUtil {
    static async hash(password) {
        const saltRounds = 12;
        return bcrypt.hash(password, saltRounds);
    }
    static async compare(password, hashedPassword) {
        return bcrypt.compare(password, hashedPassword);
    }
    static generate(length = 12) {
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
        let password = "";
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return password;
    }
}
exports.PasswordUtil = PasswordUtil;
//# sourceMappingURL=password.util.js.map