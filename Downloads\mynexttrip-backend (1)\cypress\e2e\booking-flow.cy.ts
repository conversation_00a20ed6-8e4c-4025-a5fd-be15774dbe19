import { describe, beforeEach, it } from "cypress"
import { cy } from "cypress"

describe("Booking Flow", () => {
  beforeEach(() => {
    // Mock API responses
    cy.mockApiResponse("GET", "/api/hotels*", {
      data: [
        {
          id: "1",
          name: "Luxury Hotel Delhi",
          location: "New Delhi",
          price: 5000,
          rating: 4.5,
          images: ["/hotel-1.jpg"],
        },
      ],
      total: 1,
    })

    cy.mockApiResponse("GET", "/api/hotels/1", {
      id: "1",
      name: "Luxury Hotel Delhi",
      location: "New Delhi",
      price: 5000,
      rating: 4.5,
      description: "A luxury hotel in the heart of Delhi",
      amenities: ["WiFi", "Pool", "Spa"],
      images: ["/hotel-1.jpg"],
    })
  })

  it("completes full booking flow", () => {
    // Start from homepage
    cy.visit("/")
    cy.waitForPageLoad()

    // Search for hotels
    cy.searchHotels("Delhi", "2024-03-15", "2024-03-17", 2)
    cy.wait("@apiCall")

    // Select a hotel
    cy.get('[data-testid="hotel-card"]').first().click()
    cy.url().should("include", "/hotels/1")

    // Book the hotel
    cy.get('[data-testid="book-now-button"]').click()
    cy.url().should("include", "/booking/1")

    // Fill booking details
    cy.get('[data-testid="guest-name-input"]').type("John Doe")
    cy.get('[data-testid="guest-email-input"]').type("<EMAIL>")
    cy.get('[data-testid="guest-phone-input"]').type("+91 9876543210")

    // Select payment method
    cy.get('[data-testid="payment-method-stripe"]').click()

    // Proceed to payment (mock success)
    cy.mockApiResponse("POST", "/api/bookings", {
      id: "booking-123",
      status: "confirmed",
      paymentStatus: "paid",
    })

    cy.get('[data-testid="proceed-payment-button"]').click()
    cy.wait("@apiCall")

    // Verify confirmation page
    cy.url().should("include", "/booking/booking-123/confirmation")
    cy.get('[data-testid="booking-confirmation"]').should("contain", "Booking Confirmed")
  })

  it("handles payment failure gracefully", () => {
    cy.visit("/booking/1")

    // Mock payment failure
    cy.mockApiResponse(
      "POST",
      "/api/bookings",
      {
        error: "Payment failed",
      },
      400,
    )

    cy.get('[data-testid="guest-name-input"]').type("John Doe")
    cy.get('[data-testid="guest-email-input"]').type("<EMAIL>")
    cy.get('[data-testid="payment-method-stripe"]').click()
    cy.get('[data-testid="proceed-payment-button"]').click()

    cy.wait("@apiCall")
    cy.get('[data-testid="error-message"]').should("contain", "Payment failed")
  })
})
