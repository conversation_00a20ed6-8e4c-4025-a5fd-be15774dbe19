"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let BookingService = class BookingService {
    constructor(prisma, redis) {
        this.prisma = prisma;
        this.redis = redis;
    }
    async checkAvailability(availabilityDto) {
        const { listingType, listingId, checkInDate, checkOutDate, guests, roomId } = availabilityDto;
        const checkIn = new Date(checkInDate);
        const checkOut = new Date(checkOutDate);
        if (checkIn >= checkOut) {
            throw new common_1.BadRequestException("Check-out date must be after check-in date");
        }
        if (checkIn < new Date()) {
            throw new common_1.BadRequestException("Check-in date cannot be in the past");
        }
        switch (listingType) {
            case "hotel":
                return this.checkHotelAvailability(listingId, checkIn, checkOut, guests, roomId);
            case "guide":
                return this.checkGuideAvailability(listingId, checkIn, checkOut);
            case "package":
                return this.checkPackageAvailability(listingId, checkIn, guests);
            default:
                throw new common_1.BadRequestException("Invalid listing type");
        }
    }
    async createBooking(userId, createBookingDto) {
        const { listingType, listingId, checkInDate, checkOutDate, guests, roomId, specialRequests } = createBookingDto;
        const availability = await this.checkAvailability({
            listingType,
            listingId,
            checkInDate,
            checkOutDate,
            guests,
            roomId,
        });
        if (!availability.isAvailable) {
            throw new common_1.BadRequestException("Selected dates are not available");
        }
        const totalAmount = await this.calculateBookingAmount(createBookingDto);
        const booking = await this.prisma.booking.create({
            data: {
                userId,
                ...(listingType === "hotel" && { hotelId: listingId, roomId }),
                ...(listingType === "guide" && { guideId: listingId }),
                ...(listingType === "package" && { packageId: listingId }),
                checkInDate: new Date(checkInDate),
                checkOutDate: new Date(checkOutDate),
                guests,
                totalAmount,
                specialRequests,
                status: client_1.BookingStatus.PENDING,
                bookingReference: this.generateBookingReference(),
            },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                    },
                },
                hotel: {
                    include: {
                        vendor: {
                            select: {
                                businessName: true,
                                contactPhone: true,
                                contactEmail: true,
                            },
                        },
                    },
                },
                guide: {
                    include: {
                        vendor: {
                            select: {
                                businessName: true,
                                contactPhone: true,
                                contactEmail: true,
                            },
                        },
                    },
                },
                package: {
                    include: {
                        vendor: {
                            select: {
                                businessName: true,
                                contactPhone: true,
                                contactEmail: true,
                            },
                        },
                    },
                },
                room: true,
            },
        });
        await this.updateInventoryAfterBooking(booking);
        return booking;
    }
    async getUserBookings(userId, query) {
        const { page = 1, limit = 10, status, startDate, endDate } = query;
        const skip = (page - 1) * limit;
        const where = {
            userId,
        };
        if (status) {
            where.status = status;
        }
        if (startDate || endDate) {
            where.checkInDate = {};
            if (startDate)
                where.checkInDate.gte = new Date(startDate);
            if (endDate)
                where.checkInDate.lte = new Date(endDate);
        }
        const [bookings, total] = await Promise.all([
            this.prisma.booking.findMany({
                where,
                include: {
                    hotel: {
                        select: {
                            name: true,
                            city: true,
                            state: true,
                            images: {
                                take: 1,
                                orderBy: { isPrimary: "desc" },
                            },
                        },
                    },
                    guide: {
                        select: {
                            name: true,
                            city: true,
                            state: true,
                            images: {
                                take: 1,
                                orderBy: { isPrimary: "desc" },
                            },
                        },
                    },
                    package: {
                        select: {
                            name: true,
                            city: true,
                            state: true,
                            duration: true,
                            images: {
                                take: 1,
                                orderBy: { isPrimary: "desc" },
                            },
                        },
                    },
                    room: {
                        select: {
                            type: true,
                            maxOccupancy: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
                skip,
                take: limit,
            }),
            this.prisma.booking.count({ where }),
        ]);
        return {
            bookings,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getBookingById(userId, bookingId, userRole) {
        const booking = await this.prisma.booking.findUnique({
            where: { id: bookingId },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                    },
                },
                hotel: {
                    include: {
                        vendor: {
                            select: {
                                businessName: true,
                                contactPhone: true,
                                contactEmail: true,
                                userId: true,
                            },
                        },
                        images: true,
                    },
                },
                guide: {
                    include: {
                        vendor: {
                            select: {
                                businessName: true,
                                contactPhone: true,
                                contactEmail: true,
                                userId: true,
                            },
                        },
                        images: true,
                    },
                },
                package: {
                    include: {
                        vendor: {
                            select: {
                                businessName: true,
                                contactPhone: true,
                                contactEmail: true,
                                userId: true,
                            },
                        },
                        images: true,
                    },
                },
                room: true,
                payments: true,
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException("Booking not found");
        }
        const isOwner = booking.userId === userId;
        const isVendor = userRole === "vendor" &&
            (booking.hotel?.vendor?.userId === userId ||
                booking.guide?.vendor?.userId === userId ||
                booking.package?.vendor?.userId === userId);
        if (!isOwner && !isVendor) {
            throw new common_1.ForbiddenException("Access denied");
        }
        return booking;
    }
    async updateBooking(userId, bookingId, updateBookingDto) {
        const booking = await this.prisma.booking.findFirst({
            where: {
                id: bookingId,
                userId,
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException("Booking not found");
        }
        if (booking.status !== client_1.BookingStatus.PENDING) {
            throw new common_1.BadRequestException("Only pending bookings can be updated");
        }
        let totalAmount = booking.totalAmount;
        if (updateBookingDto.checkInDate || updateBookingDto.checkOutDate || updateBookingDto.guests) {
            const updatedBookingData = {
                ...booking,
                ...updateBookingDto,
                checkInDate: updateBookingDto.checkInDate || booking.checkInDate.toISOString(),
                checkOutDate: updateBookingDto.checkOutDate || booking.checkOutDate.toISOString(),
            };
            totalAmount = await this.calculateBookingAmount(updatedBookingData);
        }
        const updatedBooking = await this.prisma.booking.update({
            where: { id: bookingId },
            data: {
                ...updateBookingDto,
                ...(updateBookingDto.checkInDate && { checkInDate: new Date(updateBookingDto.checkInDate) }),
                ...(updateBookingDto.checkOutDate && { checkOutDate: new Date(updateBookingDto.checkOutDate) }),
                totalAmount,
            },
            include: {
                hotel: true,
                guide: true,
                package: true,
                room: true,
            },
        });
        return updatedBooking;
    }
    async cancelBooking(userId, bookingId) {
        const booking = await this.prisma.booking.findFirst({
            where: {
                id: bookingId,
                userId,
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException("Booking not found");
        }
        if (booking.status === client_1.BookingStatus.CANCELLED) {
            throw new common_1.BadRequestException("Booking is already cancelled");
        }
        if (booking.status === client_1.BookingStatus.COMPLETED) {
            throw new common_1.BadRequestException("Cannot cancel completed booking");
        }
        const updatedBooking = await this.prisma.booking.update({
            where: { id: bookingId },
            data: {
                status: client_1.BookingStatus.CANCELLED,
                cancelledAt: new Date(),
            },
            include: {
                hotel: true,
                guide: true,
                package: true,
                room: true,
            },
        });
        await this.restoreInventoryAfterCancellation(updatedBooking);
        return updatedBooking;
    }
    async confirmBooking(vendorUserId, bookingId) {
        const booking = await this.prisma.booking.findUnique({
            where: { id: bookingId },
            include: {
                hotel: { include: { vendor: true } },
                guide: { include: { vendor: true } },
                package: { include: { vendor: true } },
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException("Booking not found");
        }
        const isVendor = booking.hotel?.vendor?.userId === vendorUserId ||
            booking.guide?.vendor?.userId === vendorUserId ||
            booking.package?.vendor?.userId === vendorUserId;
        if (!isVendor) {
            throw new common_1.ForbiddenException("Access denied");
        }
        if (booking.status !== client_1.BookingStatus.PENDING) {
            throw new common_1.BadRequestException("Only pending bookings can be confirmed");
        }
        const updatedBooking = await this.prisma.booking.update({
            where: { id: bookingId },
            data: {
                status: client_1.BookingStatus.CONFIRMED,
                confirmedAt: new Date(),
            },
            include: {
                user: true,
                hotel: true,
                guide: true,
                package: true,
                room: true,
            },
        });
        return updatedBooking;
    }
    async rejectBooking(vendorUserId, bookingId, reason) {
        const booking = await this.prisma.booking.findUnique({
            where: { id: bookingId },
            include: {
                hotel: { include: { vendor: true } },
                guide: { include: { vendor: true } },
                package: { include: { vendor: true } },
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException("Booking not found");
        }
        const isVendor = booking.hotel?.vendor?.userId === vendorUserId ||
            booking.guide?.vendor?.userId === vendorUserId ||
            booking.package?.vendor?.userId === vendorUserId;
        if (!isVendor) {
            throw new common_1.ForbiddenException("Access denied");
        }
        if (booking.status !== client_1.BookingStatus.PENDING) {
            throw new common_1.BadRequestException("Only pending bookings can be rejected");
        }
        const updatedBooking = await this.prisma.booking.update({
            where: { id: bookingId },
            data: {
                status: client_1.BookingStatus.CANCELLED,
                cancelledAt: new Date(),
                cancellationReason: reason,
            },
            include: {
                user: true,
                hotel: true,
                guide: true,
                package: true,
                room: true,
            },
        });
        await this.restoreInventoryAfterCancellation(updatedBooking);
        return updatedBooking;
    }
    async generateInvoice(userId, bookingId, userRole) {
        const booking = await this.getBookingById(userId, bookingId, userRole);
        const invoice = {
            bookingReference: booking.bookingReference,
            invoiceNumber: `INV-${booking.bookingReference}`,
            issueDate: new Date().toISOString(),
            dueDate: booking.checkInDate,
            customer: {
                name: `${booking.user.firstName} ${booking.user.lastName}`,
                email: booking.user.email,
                phone: booking.user.phone,
            },
            vendor: {
                name: booking.hotel?.vendor?.businessName ||
                    booking.guide?.vendor?.businessName ||
                    booking.package?.vendor?.businessName,
                phone: booking.hotel?.vendor?.contactPhone ||
                    booking.guide?.vendor?.contactPhone ||
                    booking.package?.vendor?.contactPhone,
                email: booking.hotel?.vendor?.contactEmail ||
                    booking.guide?.vendor?.contactEmail ||
                    booking.package?.vendor?.contactEmail,
            },
            listing: {
                name: booking.hotel?.name || booking.guide?.name || booking.package?.name,
                type: booking.hotel ? "Hotel" : booking.guide ? "Guide" : "Package",
                location: `${booking.hotel?.city || booking.guide?.city || booking.package?.city}, ${booking.hotel?.state || booking.guide?.state || booking.package?.state}`,
            },
            bookingDetails: {
                checkIn: booking.checkInDate,
                checkOut: booking.checkOutDate,
                guests: booking.guests,
                duration: booking.package?.duration || this.calculateNights(booking.checkInDate, booking.checkOutDate),
                room: booking.room?.type,
            },
            amount: {
                subtotal: booking.totalAmount,
                taxes: booking.totalAmount * 0.18,
                total: booking.totalAmount * 1.18,
            },
            status: booking.status,
            payments: booking.payments,
        };
        return invoice;
    }
    async checkHotelAvailability(hotelId, checkIn, checkOut, guests, roomId) {
        const hotel = await this.prisma.hotel.findUnique({
            where: { id: hotelId, status: "ACTIVE" },
            include: {
                rooms: {
                    where: {
                        ...(roomId && { id: roomId }),
                        ...(guests && { maxOccupancy: { gte: guests } }),
                    },
                },
            },
        });
        if (!hotel) {
            throw new common_1.NotFoundException("Hotel not found");
        }
        const conflictingBookings = await this.prisma.booking.count({
            where: {
                hotelId,
                ...(roomId && { roomId }),
                status: { in: [client_1.BookingStatus.CONFIRMED, client_1.BookingStatus.PENDING] },
                OR: [
                    {
                        checkInDate: { lt: checkOut },
                        checkOutDate: { gt: checkIn },
                    },
                ],
            },
        });
        const availableRooms = hotel.rooms.filter((room) => room.availableRooms > 0);
        return {
            isAvailable: conflictingBookings === 0 && availableRooms.length > 0,
            availableRooms,
            conflictingBookings,
            hotel: {
                id: hotel.id,
                name: hotel.name,
                checkInTime: hotel.checkInTime,
                checkOutTime: hotel.checkOutTime,
            },
        };
    }
    async checkGuideAvailability(guideId, checkIn, checkOut) {
        const guide = await this.prisma.guide.findUnique({
            where: { id: guideId, status: "ACTIVE" },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        const unavailableDates = await this.prisma.guideAvailability.findMany({
            where: {
                guideId,
                date: {
                    gte: checkIn,
                    lte: checkOut,
                },
                isAvailable: false,
            },
        });
        const conflictingBookings = await this.prisma.booking.count({
            where: {
                guideId,
                status: { in: [client_1.BookingStatus.CONFIRMED, client_1.BookingStatus.PENDING] },
                OR: [
                    {
                        checkInDate: { lt: checkOut },
                        checkOutDate: { gt: checkIn },
                    },
                ],
            },
        });
        return {
            isAvailable: unavailableDates.length === 0 && conflictingBookings === 0,
            unavailableDates,
            conflictingBookings,
            guide: {
                id: guide.id,
                name: guide.name,
                maxGroupSize: guide.maxGroupSize,
                pricePerDay: guide.pricePerDay,
            },
        };
    }
    async checkPackageAvailability(packageId, checkIn, guests) {
        const packageData = await this.prisma.package.findUnique({
            where: { id: packageId, status: "ACTIVE" },
        });
        if (!packageData) {
            throw new common_1.NotFoundException("Package not found");
        }
        const isAvailable = !guests || guests <= packageData.maxGroupSize;
        return {
            isAvailable,
            package: {
                id: packageData.id,
                name: packageData.name,
                duration: packageData.duration,
                maxGroupSize: packageData.maxGroupSize,
                price: packageData.price,
            },
        };
    }
    async calculateBookingAmount(bookingData) {
        const { listingType, listingId, checkInDate, checkOutDate, guests, roomId } = bookingData;
        const checkIn = new Date(checkInDate);
        const checkOut = new Date(checkOutDate);
        const nights = this.calculateNights(checkIn, checkOut);
        switch (listingType) {
            case "hotel":
                const room = await this.prisma.room.findUnique({
                    where: { id: roomId },
                });
                return room ? room.basePrice * nights : 0;
            case "guide":
                const guide = await this.prisma.guide.findUnique({
                    where: { id: listingId },
                });
                return guide ? guide.pricePerDay * nights : 0;
            case "package":
                const packageData = await this.prisma.package.findUnique({
                    where: { id: listingId },
                });
                return packageData ? packageData.price * (guests || 1) : 0;
            default:
                return 0;
        }
    }
    calculateNights(checkIn, checkOut) {
        const timeDiff = checkOut.getTime() - checkIn.getTime();
        return Math.ceil(timeDiff / (1000 * 3600 * 24));
    }
    generateBookingReference() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8);
        return `MNT${timestamp}${random}`.toUpperCase();
    }
    async updateInventoryAfterBooking(booking) {
        if (booking.roomId) {
            await this.prisma.room.update({
                where: { id: booking.roomId },
                data: {
                    availableRooms: {
                        decrement: 1,
                    },
                },
            });
        }
    }
    async restoreInventoryAfterCancellation(booking) {
        if (booking.roomId) {
            await this.prisma.room.update({
                where: { id: booking.roomId },
                data: {
                    availableRooms: {
                        increment: 1,
                    },
                },
            });
        }
    }
};
exports.BookingService = BookingService;
exports.BookingService = BookingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function, Function])
], BookingService);
//# sourceMappingURL=booking.service.js.map