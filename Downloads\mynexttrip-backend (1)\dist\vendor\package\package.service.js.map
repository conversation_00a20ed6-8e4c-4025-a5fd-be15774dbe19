{"version": 3, "file": "package.service.js", "sourceRoot": "", "sources": ["../../../src/vendor/package/package.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmF;AAInF,2CAA8C;AAIvC,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YACU,MAAqB,EACrB,SAA2B;QAD3B,WAAM,GAAN,MAAM,CAAe;QACrB,cAAS,GAAT,SAAS,CAAkB;IAClC,CAAC;IAEJ,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,gBAAkC;QACpE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACnD,IAAI,EAAE;gBACJ,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,IAAI,EAAE,gBAAgB,CAAC,IAAI;gBAC3B,WAAW,EAAE,gBAAgB,CAAC,WAAW;gBACzC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;gBACnC,YAAY,EAAE,gBAAgB,CAAC,YAAY;gBAC3C,KAAK,EAAE,gBAAgB,CAAC,KAAK;gBAC7B,UAAU,EAAE,gBAAgB,CAAC,UAAU;gBACvC,UAAU,EAAE,gBAAgB,CAAC,UAAU;gBACvC,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,IAAI,EAAE,gBAAgB,CAAC,IAAI;gBAC3B,KAAK,EAAE,gBAAgB,CAAC,KAAK;gBAC7B,UAAU,EAAE,gBAAgB,CAAC,UAAU;gBACvC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;gBACnC,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB;gBACvD,MAAM,EAAE,sBAAa,CAAC,KAAK;aAC5B;YACD,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,YAAY,EAAE,IAAI;wBAClB,YAAY,EAAE,IAAI;wBAClB,YAAY,EAAE,IAAI;qBACnB;iBACF;aACF;SACF,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAA;QAC/C,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,KAAsB;QAC5D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,KAAK,CAAA;QACtE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAA;QAE/B,MAAM,KAAK,GAAQ;YACjB,QAAQ,EAAE,MAAM,CAAC,EAAE;SACpB,CAAA;QAED,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAA;QACvB,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,KAAK,CAAC,IAAI,GAAG;gBACX,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,aAAa;aACpB,CAAA;QACH,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAC3B,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,EAAE,GAAG;gBACT;oBACE,IAAI,EAAE;wBACJ,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,aAAa;qBACpB;iBACF;gBACD;oBACE,WAAW,EAAE;wBACX,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,aAAa;qBACpB;iBACF;aACF,CAAA;QACH,CAAC;QAED,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC3B,KAAK;gBACL,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,IAAI,EAAE,CAAC;wBACP,OAAO,EAAE;4BACP,SAAS,EAAE,MAAM;yBAClB;qBACF;oBACD,MAAM,EAAE;wBACN,MAAM,EAAE;4BACN,QAAQ,EAAE,IAAI;4BACd,OAAO,EAAE,IAAI;yBACd;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;gBACD,IAAI;gBACJ,IAAI,EAAE,KAAK;aACZ,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;SACrC,CAAC,CAAA;QAEF,OAAO;YACL,QAAQ;YACR,UAAU,EAAE;gBACV,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC;SACF,CAAA;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,SAAiB;QACpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACtD,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,MAAM,CAAC,EAAE;aACpB;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,SAAS,EAAE,MAAM;qBAClB;iBACF;gBACD,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,cAAc,EAAE,IAAI;6BACrB;yBACF;qBACF;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,MAAM;qBAClB;oBACD,IAAI,EAAE,EAAE;iBACT;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAE,IAAI;qBACd;iBACF;aACF;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAA;QAClD,CAAC;QAED,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,SAAiB,EAAE,gBAAkC;QACvF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACtD,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,MAAM,CAAC,EAAE;aACpB;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACtD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,IAAI,EAAE,gBAAgB;YACtB,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,YAAY,EAAE,IAAI;wBAClB,YAAY,EAAE,IAAI;wBAClB,YAAY,EAAE,IAAI;qBACnB;iBACF;aACF;SACF,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAA;QAClD,OAAO,cAAc,CAAA;IACvB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,SAAiB;QACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACtD,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,MAAM,CAAC,EAAE;aACpB;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACrD,KAAK,EAAE;gBACL,SAAS;gBACT,MAAM,EAAE;oBACN,EAAE,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;iBAC7B;aACF;SACF,CAAC,CAAA;QAEF,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,4BAAmB,CAAC,4CAA4C,CAAC,CAAA;QAC7E,CAAC;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;SACzB,CAAC,CAAA;QAEF,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAA;QACnF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAA;QAChE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,SAAiB,EAAE,KAA4B;QACvF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACtD,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,MAAM,CAAC,EAAE;aACpB;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAC9B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBACrC,IAAI,EAAE;oBACJ,SAAS;oBACT,GAAG,EAAE,qBAAqB,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACtD,OAAO,EAAE,GAAG,WAAW,CAAC,IAAI,YAAY,KAAK,GAAG,CAAC,EAAE;oBACnD,SAAS,EAAE,KAAK,KAAK,CAAC;iBACvB;aACF,CAAC,CAAA;QACJ,CAAC,CAAC,CACH,CAAA;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,SAAiB,EAAE,MAAc;QACzE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACtD,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,MAAM,CAAC,EAAE;aACpB;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACtD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,IAAI,EAAE,EAAE,MAAM,EAAE,MAAuB,EAAE;YACzC,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAA;QAClD,OAAO,cAAc,CAAA;IACvB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,WAAgB;QACpD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,YAAY,EAAE,WAAW,CAAC,YAAY;gBACtC,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,aAAa,EAAE,WAAW,CAAC,aAAa,IAAI,CAAC;gBAC7C,YAAY,EAAE,WAAW,CAAC,YAAY,IAAI,CAAC;gBAC3C,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,IAAI,EAAE,SAAS;aAChB,CAAA;YAED,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;CACF,CAAA;AAxWY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;;GACA,cAAc,CAwW1B"}