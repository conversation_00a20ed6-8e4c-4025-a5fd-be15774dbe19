import type { AdminService } from "./admin.service";
import type { AdminQueryDto, UserManagementDto, PlatformConfigDto } from "./dto/admin.dto";
import { ApiResponseDto } from "../common/dto/api-response.dto";
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getDashboard(): Promise<ApiResponseDto<any>>;
    getUsers(query: AdminQueryDto): Promise<ApiResponseDto<{
        users: {
            status: import(".prisma/client").$Enums.UserStatus;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            email: string;
            phone: string | null;
            password: string | null;
            firstName: string;
            lastName: string;
            avatar: string | null;
            role: import(".prisma/client").$Enums.UserRole;
            emailVerified: boolean;
            phoneVerified: boolean;
            googleId: string | null;
            preferredLanguage: string;
            preferredCurrency: string;
            lastLoginAt: Date | null;
            paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    getUserById(id: string): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>>;
    updateUser(id: string, updateDto: UserManagementDto): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>>;
    suspendUser(id: string, body: {
        reason: string;
        duration?: number;
    }): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>>;
    activateUser(id: string): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>>;
    getBookings(query: AdminQueryDto): Promise<ApiResponseDto<{
        bookings: {
            status: import(".prisma/client").$Enums.BookingStatus;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            availabilitySlotId: string | null;
            checkIn: Date;
            checkOut: Date | null;
            guests: number;
            totalAmount: number;
            currency: string;
            guestDetails: import("@prisma/client/runtime/library").JsonValue;
            specialRequests: string | null;
            paymentIntentId: string | null;
            paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
            confirmationCode: string;
            confirmedAt: Date | null;
            cancelledAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    getPayments(query: AdminQueryDto): Promise<ApiResponseDto<{
        payments: {
            status: import(".prisma/client").$Enums.PaymentStatus;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            currency: string;
            bookingId: string;
            amount: number;
            method: import(".prisma/client").$Enums.PaymentMethod;
            gatewayId: string | null;
            gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
            refundAmount: number | null;
            refundReason: string | null;
            refundedAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    getDisputes(query: AdminQueryDto): Promise<ApiResponseDto<{
        disputes: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>>;
    resolveDispute(id: string, body: {
        resolution: string;
        refundAmount?: number;
    }): Promise<ApiResponseDto<any>>;
    getConfig(): Promise<ApiResponseDto<any>>;
    updateConfig(configDto: PlatformConfigDto): Promise<ApiResponseDto<any>>;
    getRevenueReport(query: {
        startDate?: string;
        endDate?: string;
        groupBy?: string;
    }): Promise<ApiResponseDto<{
        summary: {
            totalRevenue: any;
            platformRevenue: any;
            taxRevenue: any;
            totalTransactions: any;
        };
        revenueByPeriod: any;
        revenueByGateway: any;
        topVendors: any;
    }>>;
    getActivityReport(query: {
        startDate?: string;
        endDate?: string;
    }): Promise<ApiResponseDto<{
        userActivity: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
            _count: number;
        })[];
        bookingActivity: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.BookingGroupByOutputType, "status"[]> & {
            _count: number;
        })[];
        listingActivity: unknown;
    }>>;
}
