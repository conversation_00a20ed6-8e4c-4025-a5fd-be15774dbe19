import type { AdminService } from "./admin.service";
import type { AdminQueryDto, UserManagementDto, PlatformConfigDto } from "./dto/admin.dto";
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getDashboard(): Promise<any>;
    getUsers(query: AdminQueryDto): Promise<any>;
    getUserById(id: string): Promise<any>;
    updateUser(id: string, updateDto: UserManagementDto): Promise<any>;
    suspendUser(id: string, body: {
        reason: string;
        duration?: number;
    }): Promise<any>;
    activateUser(id: string): Promise<any>;
    getBookings(query: AdminQueryDto): Promise<any>;
    getPayments(query: AdminQueryDto): Promise<any>;
    getDisputes(query: AdminQueryDto): Promise<any>;
    resolveDispute(id: string, body: {
        resolution: string;
        refundAmount?: number;
    }): Promise<any>;
    getConfig(): Promise<any>;
    updateConfig(configDto: PlatformConfigDto): Promise<any>;
    getRevenueReport(query: {
        startDate?: string;
        endDate?: string;
        groupBy?: string;
    }): Promise<any>;
    getActivityReport(query: {
        startDate?: string;
        endDate?: string;
    }): Promise<any>;
}
