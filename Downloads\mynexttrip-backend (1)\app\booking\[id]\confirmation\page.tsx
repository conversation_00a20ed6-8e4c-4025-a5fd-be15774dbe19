"use client"

import { useQuery } from "@tanstack/react-query"
import { useParams, useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Calendar, MapPin, Users, Download, Mail, Phone } from "lucide-react"
import { api } from "@/lib/api"
import { MainLayout } from "@/components/layout/main-layout"
import Link from "next/link"
import { format } from "date-fns"

interface BookingConfirmation {
  id: string
  bookingNumber: string
  status: "confirmed" | "pending"
  listingName: string
  listingType: string
  location: string
  checkIn: string
  checkOut: string
  guests: number
  totalAmount: number
  currency: string
  paymentId: string
  paymentMethod: string
  customerName: string
  customerEmail: string
  customerPhone: string
  vendorName: string
  vendorPhone: string
  specialInstructions?: string
  cancellationPolicy: string
}

export default function BookingConfirmationPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const paymentId = searchParams.get("payment_id")

  const { data: confirmation, isLoading } = useQuery({
    queryKey: ["booking-confirmation", params.id, paymentId],
    queryFn: async () => {
      const response = await api.get(`/bookings/${params.id}/confirmation?payment_id=${paymentId}`)
      return response.data as BookingConfirmation
    },
  })

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!confirmation) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Confirmation Not Found</h1>
          <p className="text-muted-foreground">Unable to find your booking confirmation.</p>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold mb-2">Booking Confirmed!</h1>
            <p className="text-lg text-muted-foreground">
              Your reservation has been successfully confirmed. We've sent the details to your email.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Booking Details */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Booking Details
                    <Badge className="bg-green-100 text-green-800">{confirmation.status}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Booking Number</p>
                      <p className="font-semibold">{confirmation.bookingNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Payment ID</p>
                      <p className="font-semibold">{confirmation.paymentId}</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="font-semibold text-lg">{confirmation.listingName}</h3>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        <span>{confirmation.location}</span>
                      </div>
                      <Badge variant="outline" className="capitalize">
                        {confirmation.listingType}
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Check-in</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(confirmation.checkIn), "MMM dd, yyyy")}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Check-out</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(confirmation.checkOut), "MMM dd, yyyy")}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Guests</p>
                        <p className="text-sm text-muted-foreground">{confirmation.guests}</p>
                      </div>
                    </div>
                  </div>

                  {confirmation.specialInstructions && (
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium mb-2">Special Instructions</h4>
                      <p className="text-sm text-muted-foreground">{confirmation.specialInstructions}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-3">Your Details</h4>
                      <div className="space-y-2">
                        <p className="text-sm">
                          <span className="font-medium">{confirmation.customerName}</span>
                        </p>
                        <p className="text-sm flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          {confirmation.customerEmail}
                        </p>
                        <p className="text-sm flex items-center gap-2">
                          <Phone className="h-3 w-3" />
                          {confirmation.customerPhone}
                        </p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-3">Host Contact</h4>
                      <div className="space-y-2">
                        <p className="text-sm">
                          <span className="font-medium">{confirmation.vendorName}</span>
                        </p>
                        <p className="text-sm flex items-center gap-2">
                          <Phone className="h-3 w-3" />
                          {confirmation.vendorPhone}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Actions Sidebar */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full">
                    <Download className="mr-2 h-4 w-4" />
                    Download Receipt
                  </Button>
                  <Button variant="outline" className="w-full bg-transparent" asChild>
                    <Link href={`/bookings/${confirmation.id}`}>View Booking</Link>
                  </Button>
                  <Button variant="outline" className="w-full bg-transparent">
                    Contact Host
                  </Button>
                  <Button variant="outline" className="w-full bg-transparent" asChild>
                    <Link href="/">Back to Home</Link>
                  </Button>
                </CardContent>
              </Card>

              {/* Payment Summary */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Payment Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Paid</span>
                      <span className="font-semibold">
                        {confirmation.currency === "INR" ? "₹" : "$"}
                        {confirmation.totalAmount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Payment Method</span>
                      <span className="text-sm capitalize">{confirmation.paymentMethod}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Cancellation Policy */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Cancellation Policy</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{confirmation.cancellationPolicy}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
