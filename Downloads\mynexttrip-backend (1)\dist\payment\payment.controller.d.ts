import type { PaymentService } from "./payment.service";
import type { CreatePaymentDto, RefundPaymentDto, PaymentQueryDto } from "./dto/payment.dto";
export declare class PaymentController {
    private readonly paymentService;
    constructor(paymentService: PaymentService);
    createPaymentIntent(req: any, createPaymentDto: CreatePaymentDto): Promise<any>;
    processPayment(req: any, body: {
        paymentIntentId: string;
        paymentMethodId?: string;
    }): Promise<any>;
    getPayments(req: any, query: PaymentQueryDto): Promise<any>;
    getPayment(req: any, id: string): Promise<any>;
    processRefund(req: any, id: string, refundDto: RefundPaymentDto): Promise<any>;
    getSavedPaymentMethods(req: any): Promise<any>;
    savePaymentMethod(req: any, body: {
        paymentMethodId: string;
        isDefault?: boolean;
    }): Promise<any>;
    setDefaultPaymentMethod(req: any, methodId: string): Promise<any>;
    getRevenueAnalytics(req: any, query: {
        startDate?: string;
        endDate?: string;
    }): Promise<any>;
}
