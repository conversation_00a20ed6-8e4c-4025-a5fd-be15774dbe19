import type { PaymentService } from "./payment.service";
import type { CreatePaymentDto, RefundPaymentDto, PaymentQueryDto } from "./dto/payment.dto";
import { ApiResponseDto } from "../common/dto/api-response.dto";
export declare class PaymentController {
    private readonly paymentService;
    constructor(paymentService: PaymentService);
    createPaymentIntent(req: any, createPaymentDto: CreatePaymentDto): Promise<ApiResponseDto<{
        paymentId: string;
        paymentReference: any;
        clientSecret: any;
        amount: number;
        currency: string;
        gateway: PaymentGateway;
        booking: {
            id: string;
            reference: any;
            checkIn: any;
            checkOut: any;
            guests: number;
            listing: {
                name: any;
                type: string;
                location: string;
            };
        };
    }>>;
    processPayment(req: any, body: {
        paymentIntentId: string;
        paymentMethodId?: string;
    }): Promise<ApiResponseDto<{
        id: string;
        amount: number;
        currency: string;
        method: import(".prisma/client").$Enums.PaymentMethod;
        status: import(".prisma/client").$Enums.PaymentStatus;
        gatewayId: string | null;
        gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
        refundAmount: number | null;
        refundReason: string | null;
        refundedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        bookingId: string;
    }>>;
    getPayments(req: any, query: PaymentQueryDto): Promise<ApiResponseDto<{
        payments: {
            id: string;
            amount: number;
            currency: string;
            method: import(".prisma/client").$Enums.PaymentMethod;
            status: import(".prisma/client").$Enums.PaymentStatus;
            gatewayId: string | null;
            gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
            refundAmount: number | null;
            refundReason: string | null;
            refundedAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
            bookingId: string;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    getPayment(req: any, id: string): Promise<ApiResponseDto<{
        id: string;
        amount: number;
        currency: string;
        method: import(".prisma/client").$Enums.PaymentMethod;
        status: import(".prisma/client").$Enums.PaymentStatus;
        gatewayId: string | null;
        gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
        refundAmount: number | null;
        refundReason: string | null;
        refundedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        bookingId: string;
    }>>;
    processRefund(req: any, id: string, refundDto: RefundPaymentDto): Promise<ApiResponseDto<any>>;
    getSavedPaymentMethods(req: any): Promise<ApiResponseDto<any[]>>;
    savePaymentMethod(req: any, body: {
        paymentMethodId: string;
        isDefault?: boolean;
    }): Promise<ApiResponseDto<{
        success: boolean;
        paymentMethodId: string;
        isDefault: boolean;
    }>>;
    setDefaultPaymentMethod(req: any, methodId: string): Promise<ApiResponseDto<any>>;
    getRevenueAnalytics(req: any, query: {
        startDate?: string;
        endDate?: string;
    }): Promise<ApiResponseDto<{
        totalRevenue: number;
        vendorRevenue: number;
        platformFees: any;
        totalPayments: number;
        monthlyRevenue: unknown;
        recentPayments: {
            id: string;
            amount: number;
            currency: string;
            method: import(".prisma/client").$Enums.PaymentMethod;
            status: import(".prisma/client").$Enums.PaymentStatus;
            gatewayId: string | null;
            gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
            refundAmount: number | null;
            refundReason: string | null;
            refundedAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
            bookingId: string;
        }[];
    }>>;
}
