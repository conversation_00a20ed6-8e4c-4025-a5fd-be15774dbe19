import { <PERSON>du<PERSON> } from "@nestjs/common"
import { VendorController } from "./vendor.controller"
import { VendorService } from "./vendor.service"
import { HotelController } from "./hotel/hotel.controller"
import { HotelService } from "./hotel/hotel.service"
import { GuideController } from "./guide/guide.controller"
import { GuideService } from "./guide/guide.service"
import { PackageController } from "./package/package.controller"
import { PackageService } from "./package/package.service"
import { PrismaModule } from "../common/modules/prisma.module"
import { TypesenseModule } from "../common/modules/typesense.module"

@Module({
  imports: [PrismaModule, TypesenseModule],
  controllers: [VendorController, HotelController, GuideController, PackageController],
  providers: [VendorService, HotelService, GuideService, PackageService],
  exports: [VendorService, HotelService, GuideService, PackageService],
})
export class VendorModule {}
