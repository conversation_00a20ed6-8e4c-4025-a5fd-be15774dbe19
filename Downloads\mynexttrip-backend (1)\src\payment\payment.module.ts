import { Module } from "@nestjs/common"
import { PaymentController } from "./payment.controller"
import { PaymentService } from "./payment.service"
import { StripeService } from "./services/stripe.service"
import { RazorpayService } from "./services/razorpay.service"
import { WebhookController } from "./webhook.controller"
import { PrismaModule } from "../common/modules/prisma.module"
import { RedisModule } from "../common/modules/redis.module"

@Module({
  imports: [PrismaModule, RedisModule],
  controllers: [PaymentController, WebhookController],
  providers: [PaymentService, StripeService, RazorpayService],
  exports: [PaymentService],
})
export class PaymentModule {}
