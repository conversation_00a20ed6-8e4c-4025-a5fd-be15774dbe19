import type { PrismaService } from "../services/prisma.service";
import type { RedisService } from "../services/redis.service";
export declare class HealthController {
    private prismaService;
    private redisService;
    constructor(prismaService: PrismaService, redisService: RedisService);
    healthCheck(): Promise<{
        status: string;
        checks: {
            database: boolean;
            redis: boolean;
            timestamp: string;
        };
    }>;
}
