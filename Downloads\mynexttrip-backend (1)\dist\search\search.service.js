"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchService = void 0;
const common_1 = require("@nestjs/common");
let SearchService = class SearchService {
    constructor(prisma, typesense, redis) {
        this.prisma = prisma;
        this.typesense = typesense;
        this.redis = redis;
    }
    async search(query) {
        const { q, type, city, state, minPrice, maxPrice, rating, amenities, starRating, checkIn, checkOut, guests, page = 1, limit = 20, sortBy = "relevance", } = query;
        const searchParams = {
            q: q || "*",
            query_by: "name,description,city,state",
            filter_by: this.buildFilterString({
                type,
                city,
                state,
                minPrice,
                maxPrice,
                rating,
                starRating,
            }),
            sort_by: this.buildSortString(sortBy),
            page,
            per_page: limit,
            facet_by: "type,city,state,category,difficulty,starRating",
        };
        if (amenities && amenities.length > 0) {
            const amenitiesFilter = amenities.map((amenity) => `amenities:=${amenity}`).join(" || ");
            searchParams.filter_by = searchParams.filter_by
                ? `${searchParams.filter_by} && (${amenitiesFilter})`
                : `(${amenitiesFilter})`;
        }
        try {
            const searchResults = await this.typesense.client.collections("listings").documents().search(searchParams);
            const listingIds = searchResults.hits?.map((hit) => hit.document.id) || [];
            const detailedResults = await this.getDetailedListings(listingIds, type);
            let availabilityResults = detailedResults;
            if (checkIn && checkOut && (type === "hotel" || !type)) {
                availabilityResults = await this.filterByAvailability(detailedResults, checkIn, checkOut, guests);
            }
            return {
                results: availabilityResults,
                pagination: {
                    page,
                    limit,
                    total: searchResults.found || 0,
                    totalPages: Math.ceil((searchResults.found || 0) / limit),
                },
                facets: searchResults.facet_counts || [],
                searchTime: searchResults.search_time_ms,
            };
        }
        catch (error) {
            console.error("Typesense search error:", error);
            return this.fallbackDatabaseSearch(query);
        }
    }
    async searchHotels(query) {
        return this.search({ ...query, type: "hotel" });
    }
    async searchGuides(query) {
        return this.search({ ...query, type: "guide" });
    }
    async searchPackages(query) {
        return this.search({ ...query, type: "package" });
    }
    async getSuggestions(searchQuery) {
        if (!searchQuery || searchQuery.length < 2) {
            return [];
        }
        const cacheKey = `suggestions:${searchQuery.toLowerCase()}`;
        const cached = await this.redis.get(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }
        try {
            const searchParams = {
                q: searchQuery,
                query_by: "name,city,state",
                per_page: 10,
                sort_by: "totalReviews:desc",
            };
            const results = await this.typesense.client.collections("listings").documents().search(searchParams);
            const suggestions = results.hits?.map((hit) => ({
                id: hit.document.id,
                name: hit.document.name,
                type: hit.document.type,
                city: hit.document.city,
                state: hit.document.state,
            })) || [];
            await this.redis.setex(cacheKey, 3600, JSON.stringify(suggestions));
            return suggestions;
        }
        catch (error) {
            console.error("Error getting suggestions:", error);
            return [];
        }
    }
    async searchLocations(searchQuery) {
        if (!searchQuery || searchQuery.length < 2) {
            return [];
        }
        const locations = await this.prisma.$queryRaw `
      SELECT DISTINCT city, state, COUNT(*) as count
      FROM (
        SELECT city, state FROM "Hotel" WHERE status = 'ACTIVE'
        UNION ALL
        SELECT city, state FROM "Guide" WHERE status = 'ACTIVE'
        UNION ALL
        SELECT city, state FROM "Package" WHERE status = 'ACTIVE'
      ) AS locations
      WHERE LOWER(city) LIKE LOWER(${`%${searchQuery}%`})
         OR LOWER(state) LIKE LOWER(${`%${searchQuery}%`})
      GROUP BY city, state
      ORDER BY count DESC
      LIMIT 10
    `;
        return locations;
    }
    async getAvailableFilters(type, city) {
        const filters = {
            priceRanges: [
                { label: "Under ₹2,000", min: 0, max: 2000 },
                { label: "₹2,000 - ₹5,000", min: 2000, max: 5000 },
                { label: "₹5,000 - ₹10,000", min: 5000, max: 10000 },
                { label: "Above ₹10,000", min: 10000, max: null },
            ],
            ratings: [
                { label: "4.5+ Excellent", value: 4.5 },
                { label: "4.0+ Very Good", value: 4.0 },
                { label: "3.5+ Good", value: 3.5 },
                { label: "3.0+ Average", value: 3.0 },
            ],
        };
        if (type === "hotel" || !type) {
            const hotelFilters = await this.prisma.hotel.groupBy({
                by: ["starRating"],
                where: {
                    status: "ACTIVE",
                    ...(city && { city: { contains: city, mode: "insensitive" } }),
                },
                _count: true,
            });
            filters.starRatings = hotelFilters.map((item) => ({
                label: `${item.starRating} Star`,
                value: item.starRating,
                count: item._count,
            }));
            const amenities = await this.prisma.$queryRaw `
        SELECT UNNEST(amenities) as amenity, COUNT(*) as count
        FROM "Hotel"
        WHERE status = 'ACTIVE'
        ${city ? `AND LOWER(city) = LOWER(${city})` : ""}
        GROUP BY amenity
        ORDER BY count DESC
        LIMIT 20
      `;
            filters.amenities = amenities;
        }
        if (type === "package" || !type) {
            const packageFilters = await this.prisma.package.groupBy({
                by: ["category", "difficulty"],
                where: {
                    status: "ACTIVE",
                    ...(city && { city: { contains: city, mode: "insensitive" } }),
                },
                _count: true,
            });
            filters.categories = packageFilters
                .filter((item) => item.category)
                .map((item) => ({
                label: item.category,
                value: item.category,
                count: item._count,
            }));
            filters.difficulties = packageFilters
                .filter((item) => item.difficulty)
                .map((item) => ({
                label: item.difficulty,
                value: item.difficulty,
                count: item._count,
            }));
        }
        return filters;
    }
    async getPopularListings() {
        const cacheKey = "popular:listings";
        const cached = await this.redis.get(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }
        const [popularHotels, popularGuides, popularPackages, popularDestinations] = await Promise.all([
            this.prisma.hotel.findMany({
                where: { status: "ACTIVE" },
                include: {
                    images: { take: 1, orderBy: { isPrimary: "desc" } },
                    _count: { select: { reviews: true, bookings: true } },
                },
                orderBy: [{ averageRating: "desc" }, { totalReviews: "desc" }],
                take: 8,
            }),
            this.prisma.guide.findMany({
                where: { status: "ACTIVE" },
                include: {
                    images: { take: 1, orderBy: { isPrimary: "desc" } },
                    _count: { select: { reviews: true, bookings: true } },
                },
                orderBy: [{ averageRating: "desc" }, { totalReviews: "desc" }],
                take: 8,
            }),
            this.prisma.package.findMany({
                where: { status: "ACTIVE" },
                include: {
                    images: { take: 1, orderBy: { isPrimary: "desc" } },
                    _count: { select: { reviews: true, bookings: true } },
                },
                orderBy: [{ averageRating: "desc" }, { totalReviews: "desc" }],
                take: 8,
            }),
            this.prisma.$queryRaw `
        SELECT city, state, COUNT(*) as listings_count,
               AVG(CASE WHEN "averageRating" > 0 THEN "averageRating" END) as avg_rating
        FROM (
          SELECT city, state, "averageRating" FROM "Hotel" WHERE status = 'ACTIVE'
          UNION ALL
          SELECT city, state, "averageRating" FROM "Guide" WHERE status = 'ACTIVE'
          UNION ALL
          SELECT city, state, "averageRating" FROM "Package" WHERE status = 'ACTIVE'
        ) AS all_listings
        GROUP BY city, state
        HAVING COUNT(*) >= 5
        ORDER BY avg_rating DESC, listings_count DESC
        LIMIT 12
      `,
        ]);
        const popular = {
            hotels: popularHotels,
            guides: popularGuides,
            packages: popularPackages,
            destinations: popularDestinations,
        };
        await this.redis.setex(cacheKey, 21600, JSON.stringify(popular));
        return popular;
    }
    buildFilterString(filters) {
        const conditions = [];
        if (filters.type) {
            conditions.push(`type:=${filters.type}`);
        }
        if (filters.city) {
            conditions.push(`city:=${filters.city}`);
        }
        if (filters.state) {
            conditions.push(`state:=${filters.state}`);
        }
        if (filters.minPrice !== undefined) {
            conditions.push(`minPrice:>=${filters.minPrice}`);
        }
        if (filters.maxPrice !== undefined) {
            conditions.push(`maxPrice:<=${filters.maxPrice}`);
        }
        if (filters.rating !== undefined) {
            conditions.push(`averageRating:>=${filters.rating}`);
        }
        if (filters.starRating !== undefined) {
            conditions.push(`starRating:=${filters.starRating}`);
        }
        conditions.push(`status:=ACTIVE`);
        return conditions.join(" && ");
    }
    buildSortString(sortBy) {
        switch (sortBy) {
            case "price_low":
                return "minPrice:asc";
            case "price_high":
                return "minPrice:desc";
            case "rating":
                return "averageRating:desc,totalReviews:desc";
            case "reviews":
                return "totalReviews:desc";
            case "newest":
                return "_text_match:desc";
            default:
                return "_text_match:desc,averageRating:desc";
        }
    }
    async getDetailedListings(listingIds, type) {
        if (listingIds.length === 0)
            return [];
        const results = [];
        if (!type || type === "hotel") {
            const hotels = await this.prisma.hotel.findMany({
                where: { id: { in: listingIds }, status: "ACTIVE" },
                include: {
                    images: { take: 3, orderBy: { isPrimary: "desc" } },
                    rooms: {
                        select: {
                            id: true,
                            type: true,
                            basePrice: true,
                            maxOccupancy: true,
                            amenities: true,
                        },
                        orderBy: { basePrice: "asc" },
                    },
                    vendor: {
                        select: { businessName: true, contactPhone: true },
                    },
                    _count: { select: { reviews: true, bookings: true } },
                },
            });
            results.push(...hotels.map((hotel) => ({ ...hotel, listingType: "hotel" })));
        }
        if (!type || type === "guide") {
            const guides = await this.prisma.guide.findMany({
                where: { id: { in: listingIds }, status: "ACTIVE" },
                include: {
                    images: { take: 3, orderBy: { isPrimary: "desc" } },
                    vendor: {
                        select: { businessName: true, contactPhone: true },
                    },
                    _count: { select: { reviews: true, bookings: true } },
                },
            });
            results.push(...guides.map((guide) => ({ ...guide, listingType: "guide" })));
        }
        if (!type || type === "package") {
            const packages = await this.prisma.package.findMany({
                where: { id: { in: listingIds }, status: "ACTIVE" },
                include: {
                    images: { take: 3, orderBy: { isPrimary: "desc" } },
                    vendor: {
                        select: { businessName: true, contactPhone: true },
                    },
                    _count: { select: { reviews: true, bookings: true } },
                },
            });
            results.push(...packages.map((pkg) => ({ ...pkg, listingType: "package" })));
        }
        return results;
    }
    async filterByAvailability(listings, checkIn, checkOut, guests) {
        const checkInDate = new Date(checkIn);
        const checkOutDate = new Date(checkOut);
        const availableListings = [];
        for (const listing of listings) {
            if (listing.listingType === "hotel") {
                const availableRooms = await this.prisma.room.findMany({
                    where: {
                        hotelId: listing.id,
                        maxOccupancy: guests ? { gte: guests } : undefined,
                        availableRooms: { gt: 0 },
                    },
                });
                if (availableRooms.length > 0) {
                    const conflictingBookings = await this.prisma.booking.count({
                        where: {
                            hotelId: listing.id,
                            status: { in: ["CONFIRMED", "PENDING"] },
                            OR: [
                                {
                                    checkInDate: { lte: checkOutDate },
                                    checkOutDate: { gte: checkInDate },
                                },
                            ],
                        },
                    });
                    if (conflictingBookings === 0) {
                        availableListings.push({
                            ...listing,
                            availableRooms: availableRooms.map((room) => ({
                                ...room,
                                isAvailable: true,
                            })),
                        });
                    }
                }
            }
            else {
                availableListings.push(listing);
            }
        }
        return availableListings;
    }
    async fallbackDatabaseSearch(query) {
        const { q, type, city, state, minPrice, maxPrice, page = 1, limit = 20 } = query;
        const skip = (page - 1) * limit;
        const results = [];
        let total = 0;
        if (!type || type === "hotel") {
            const where = {
                status: "ACTIVE",
                ...(q && {
                    OR: [{ name: { contains: q, mode: "insensitive" } }, { description: { contains: q, mode: "insensitive" } }],
                }),
                ...(city && { city: { contains: city, mode: "insensitive" } }),
                ...(state && { state: { contains: state, mode: "insensitive" } }),
            };
            const [hotels, hotelCount] = await Promise.all([
                this.prisma.hotel.findMany({
                    where,
                    include: {
                        images: { take: 1, orderBy: { isPrimary: "desc" } },
                        rooms: { select: { basePrice: true }, orderBy: { basePrice: "asc" }, take: 1 },
                    },
                    skip,
                    take: limit,
                }),
                this.prisma.hotel.count({ where }),
            ]);
            results.push(...hotels.map((hotel) => ({ ...hotel, listingType: "hotel" })));
            total += hotelCount;
        }
        return {
            results,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
            facets: [],
            searchTime: 0,
        };
    }
};
exports.SearchService = SearchService;
exports.SearchService = SearchService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function, Function, Function])
], SearchService);
//# sourceMappingURL=search.service.js.map