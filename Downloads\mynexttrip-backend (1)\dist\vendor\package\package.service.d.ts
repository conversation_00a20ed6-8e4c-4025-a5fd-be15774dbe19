import type { PrismaService } from "../../common/services/prisma.service";
import type { TypesenseService } from "../../common/services/typesense.service";
import type { CreatePackageDto, UpdatePackageDto, PackageQueryDto } from "./dto/package.dto";
import type { Express } from "express";
export declare class PackageService {
    private prisma;
    private typesense;
    constructor(prisma: PrismaService, typesense: TypesenseService);
    createPackage(userId: string, createPackageDto: CreatePackageDto): Promise<any>;
    getVendorPackages(userId: string, query: PackageQueryDto): Promise<{
        packages: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getPackageById(userId: string, packageId: string): Promise<any>;
    updatePackage(userId: string, packageId: string, updatePackageDto: UpdatePackageDto): Promise<any>;
    deletePackage(userId: string, packageId: string): Promise<void>;
    uploadPackageImages(userId: string, packageId: string, files: Express.Multer.File[]): Promise<any>;
    updatePackageStatus(userId: string, packageId: string, status: string): Promise<any>;
    private indexPackageInTypesense;
}
