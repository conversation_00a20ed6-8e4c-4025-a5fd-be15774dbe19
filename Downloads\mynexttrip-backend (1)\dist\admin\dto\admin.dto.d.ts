import { UserStatus, UserRole } from "@prisma/client";
export declare class AdminQueryDto {
    page?: number;
    limit?: number;
    search?: string;
    status?: UserStatus;
    role?: UserRole;
    startDate?: string;
    endDate?: string;
}
export declare class UserManagementDto {
    firstName?: string;
    lastName?: string;
    phone?: string;
    role?: UserRole;
    status?: UserStatus;
    emailVerified?: boolean;
    phoneVerified?: boolean;
}
export declare class PlatformConfigDto {
    platformCommission?: number;
    taxRate?: number;
    payoutDelay?: number;
    maxRefundDays?: number;
    maintenanceMode?: boolean;
    allowNewRegistrations?: boolean;
    requireVendorApproval?: boolean;
    requireListingApproval?: boolean;
}
