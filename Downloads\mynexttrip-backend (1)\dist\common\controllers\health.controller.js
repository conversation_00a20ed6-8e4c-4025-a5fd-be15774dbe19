"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const public_decorator_1 = require("../decorators/public.decorator");
let HealthController = class HealthController {
    constructor(prismaService, redisService) {
        this.prismaService = prismaService;
        this.redisService = redisService;
    }
    async healthCheck() {
        const checks = {
            database: false,
            redis: false,
            timestamp: new Date().toISOString(),
        };
        try {
            await this.prismaService.$queryRaw `SELECT 1`;
            checks.database = true;
        }
        catch (error) {
            console.error("Database health check failed:", error);
        }
        try {
            await this.redisService.set("health-check", "ok", 10);
            checks.redis = true;
        }
        catch (error) {
            console.error("Redis health check failed:", error);
        }
        const isHealthy = checks.database && checks.redis;
        return {
            status: isHealthy ? "healthy" : "unhealthy",
            checks,
        };
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: "Health check endpoint" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Service is healthy" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "healthCheck", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)("health"),
    (0, common_1.Controller)("health"),
    __metadata("design:paramtypes", [Function, Function])
], HealthController);
//# sourceMappingURL=health.controller.js.map