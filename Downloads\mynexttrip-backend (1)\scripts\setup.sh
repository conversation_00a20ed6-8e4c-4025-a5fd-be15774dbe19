#!/bin/bash

# MyNextTrip Backend Setup Script

echo "🔧 Setting up MyNextTrip Backend..."

# Create .env file from example
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "⚠️  Please update the environment variables in .env file"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Setup Git hooks (if using Git)
if [ -d .git ]; then
    echo "🪝 Setting up Git hooks..."
    npx husky install
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update environment variables in .env file"
echo "2. Start infrastructure: docker-compose up -d"
echo "3. Run migrations: npx prisma migrate dev"
echo "4. Seed database: npx prisma db seed"
echo "5. Start development: npm run start:dev"
