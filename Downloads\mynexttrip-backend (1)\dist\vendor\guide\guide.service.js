"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuideService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let GuideService = class GuideService {
    constructor(prisma, typesense) {
        this.prisma = prisma;
        this.typesense = typesense;
    }
    async createGuide(userId, createGuideDto) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.create({
            data: {
                vendorId: vendor.id,
                name: createGuideDto.name,
                description: createGuideDto.description,
                experience: createGuideDto.experience,
                languages: createGuideDto.languages,
                specializations: createGuideDto.specializations,
                city: createGuideDto.city,
                state: createGuideDto.state,
                pricePerDay: createGuideDto.pricePerDay,
                pricePerHour: createGuideDto.pricePerHour,
                maxGroupSize: createGuideDto.maxGroupSize,
                cancellationPolicy: createGuideDto.cancellationPolicy,
                status: client_1.GuideStatus.DRAFT,
            },
            include: {
                images: true,
                vendor: {
                    select: {
                        businessName: true,
                        contactPhone: true,
                        contactEmail: true,
                    },
                },
            },
        });
        await this.indexGuideInTypesense(guide);
        return guide;
    }
    async getVendorGuides(userId, query) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const { page = 1, limit = 10, status, city, search } = query;
        const skip = (page - 1) * limit;
        const where = {
            vendorId: vendor.id,
        };
        if (status) {
            where.status = status;
        }
        if (city) {
            where.city = {
                contains: city,
                mode: "insensitive",
            };
        }
        if (search) {
            where.OR = [
                {
                    name: {
                        contains: search,
                        mode: "insensitive",
                    },
                },
                {
                    description: {
                        contains: search,
                        mode: "insensitive",
                    },
                },
                {
                    specializations: {
                        hasSome: [search],
                    },
                },
            ];
        }
        const [guides, total] = await Promise.all([
            this.prisma.guide.findMany({
                where,
                include: {
                    images: {
                        take: 1,
                        orderBy: {
                            isPrimary: "desc",
                        },
                    },
                    _count: {
                        select: {
                            bookings: true,
                            reviews: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
                skip,
                take: limit,
            }),
            this.prisma.guide.count({ where }),
        ]);
        return {
            guides,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getGuideById(userId, guideId) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.findFirst({
            where: {
                id: guideId,
                vendorId: vendor.id,
            },
            include: {
                images: {
                    orderBy: {
                        isPrimary: "desc",
                    },
                },
                reviews: {
                    include: {
                        user: {
                            select: {
                                firstName: true,
                                lastName: true,
                                profilePicture: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 10,
                },
                _count: {
                    select: {
                        bookings: true,
                        reviews: true,
                    },
                },
            },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        return guide;
    }
    async updateGuide(userId, guideId, updateGuideDto) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.findFirst({
            where: {
                id: guideId,
                vendorId: vendor.id,
            },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        const updatedGuide = await this.prisma.guide.update({
            where: { id: guideId },
            data: updateGuideDto,
            include: {
                images: true,
                vendor: {
                    select: {
                        businessName: true,
                        contactPhone: true,
                        contactEmail: true,
                    },
                },
            },
        });
        await this.indexGuideInTypesense(updatedGuide);
        return updatedGuide;
    }
    async deleteGuide(userId, guideId) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.findFirst({
            where: {
                id: guideId,
                vendorId: vendor.id,
            },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        const activeBookings = await this.prisma.booking.count({
            where: {
                guideId,
                status: {
                    in: ["PENDING", "CONFIRMED"],
                },
            },
        });
        if (activeBookings > 0) {
            throw new common_1.BadRequestException("Cannot delete guide with active bookings");
        }
        await this.prisma.guide.delete({
            where: { id: guideId },
        });
        try {
            await this.typesense.client.collections("listings").documents(guideId).delete();
        }
        catch (error) {
            console.error("Error removing guide from Typesense:", error);
        }
    }
    async uploadGuideImages(userId, guideId, files) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.findFirst({
            where: {
                id: guideId,
                vendorId: vendor.id,
            },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        const images = await Promise.all(files.map(async (file, index) => {
            return this.prisma.guideImage.create({
                data: {
                    guideId,
                    url: `/uploads/guides/${guideId}/${file.filename}`,
                    altText: `${guide.name} - Image ${index + 1}`,
                    isPrimary: index === 0,
                },
            });
        }));
        return images;
    }
    async updateGuideStatus(userId, guideId, status) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.findFirst({
            where: {
                id: guideId,
                vendorId: vendor.id,
            },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        const updatedGuide = await this.prisma.guide.update({
            where: { id: guideId },
            data: { status: status },
            include: {
                images: true,
            },
        });
        await this.indexGuideInTypesense(updatedGuide);
        return updatedGuide;
    }
    async getGuideAvailability(userId, guideId, startDate, endDate) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.findFirst({
            where: {
                id: guideId,
                vendorId: vendor.id,
            },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        const availability = await this.prisma.guideAvailability.findMany({
            where: {
                guideId,
                date: {
                    gte: new Date(startDate),
                    lte: new Date(endDate),
                },
            },
            orderBy: {
                date: "asc",
            },
        });
        return availability;
    }
    async updateGuideAvailability(userId, guideId, dates, available) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const guide = await this.prisma.guide.findFirst({
            where: {
                id: guideId,
                vendorId: vendor.id,
            },
        });
        if (!guide) {
            throw new common_1.NotFoundException("Guide not found");
        }
        const availabilityData = dates.map((date) => ({
            guideId,
            date: new Date(date),
            isAvailable: available,
        }));
        const results = await Promise.all(availabilityData.map((data) => this.prisma.guideAvailability.upsert({
            where: {
                guideId_date: {
                    guideId: data.guideId,
                    date: data.date,
                },
            },
            update: {
                isAvailable: data.isAvailable,
            },
            create: data,
        })));
        return results;
    }
    async indexGuideInTypesense(guide) {
        try {
            const document = {
                id: guide.id,
                name: guide.name,
                description: guide.description,
                city: guide.city,
                state: guide.state,
                experience: guide.experience,
                languages: guide.languages,
                specializations: guide.specializations,
                pricePerDay: guide.pricePerDay,
                pricePerHour: guide.pricePerHour,
                maxGroupSize: guide.maxGroupSize,
                averageRating: guide.averageRating || 0,
                totalReviews: guide.totalReviews || 0,
                status: guide.status,
                type: "guide",
            };
            await this.typesense.client.collections("listings").documents().upsert(document);
        }
        catch (error) {
            console.error("Error indexing guide in Typesense:", error);
        }
    }
};
exports.GuideService = GuideService;
exports.GuideService = GuideService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function, Function])
], GuideService);
//# sourceMappingURL=guide.service.js.map