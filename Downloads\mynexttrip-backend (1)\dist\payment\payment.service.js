"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let PaymentService = class PaymentService {
    constructor(prisma, redis, stripeService, razorpayService) {
        this.prisma = prisma;
        this.redis = redis;
        this.stripeService = stripeService;
        this.razorpayService = razorpayService;
    }
    async createPaymentIntent(userId, createPaymentDto) {
        const { bookingId, gateway, currency = "INR", savePaymentMethod = false } = createPaymentDto;
        const booking = await this.prisma.booking.findFirst({
            where: {
                id: bookingId,
                userId,
                status: "PENDING",
            },
            include: {
                hotel: {
                    include: {
                        vendor: true,
                    },
                },
                guide: {
                    include: {
                        vendor: true,
                    },
                },
                package: {
                    include: {
                        vendor: true,
                    },
                },
                user: true,
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException("Booking not found or not eligible for payment");
        }
        const subtotal = booking.totalAmount;
        const taxAmount = subtotal * 0.18;
        const platformFee = subtotal * 0.05;
        const totalAmount = subtotal + taxAmount + platformFee;
        const payment = await this.prisma.payment.create({
            data: {
                bookingId,
                userId,
                gateway,
                currency,
                amount: totalAmount,
                subtotal,
                taxAmount,
                platformFee,
                status: client_1.PaymentStatus.PENDING,
                paymentReference: this.generatePaymentReference(),
            },
        });
        let paymentIntent;
        if (gateway === client_1.PaymentGateway.STRIPE) {
            paymentIntent = await this.stripeService.createPaymentIntent({
                amount: Math.round(totalAmount * 100),
                currency: currency.toLowerCase(),
                metadata: {
                    paymentId: payment.id,
                    bookingId,
                    userId,
                },
                customer: booking.user.stripeCustomerId,
                setupFutureUsage: savePaymentMethod ? "off_session" : undefined,
            });
        }
        else if (gateway === client_1.PaymentGateway.RAZORPAY) {
            paymentIntent = await this.razorpayService.createOrder({
                amount: Math.round(totalAmount * 100),
                currency,
                receipt: payment.paymentReference,
                notes: {
                    paymentId: payment.id,
                    bookingId,
                    userId,
                },
            });
        }
        await this.prisma.payment.update({
            where: { id: payment.id },
            data: {
                gatewayPaymentId: paymentIntent.id,
                gatewayResponse: paymentIntent,
            },
        });
        return {
            paymentId: payment.id,
            paymentReference: payment.paymentReference,
            clientSecret: paymentIntent.client_secret || paymentIntent.id,
            amount: totalAmount,
            currency,
            gateway,
            booking: {
                id: booking.id,
                reference: booking.bookingReference,
                checkIn: booking.checkInDate,
                checkOut: booking.checkOutDate,
                guests: booking.guests,
                listing: {
                    name: booking.hotel?.name || booking.guide?.name || booking.package?.name,
                    type: booking.hotel ? "hotel" : booking.guide ? "guide" : "package",
                    location: `${booking.hotel?.city || booking.guide?.city || booking.package?.city}, ${booking.hotel?.state || booking.guide?.state || booking.package?.state}`,
                },
            },
        };
    }
    async processPayment(userId, paymentIntentId, paymentMethodId) {
        const payment = await this.prisma.payment.findFirst({
            where: {
                gatewayPaymentId: paymentIntentId,
                userId,
            },
            include: {
                booking: {
                    include: {
                        hotel: { include: { vendor: true } },
                        guide: { include: { vendor: true } },
                        package: { include: { vendor: true } },
                    },
                },
            },
        });
        if (!payment) {
            throw new common_1.NotFoundException("Payment not found");
        }
        if (payment.status !== client_1.PaymentStatus.PENDING) {
            throw new common_1.BadRequestException("Payment is not in pending status");
        }
        try {
            let gatewayResponse;
            if (payment.gateway === client_1.PaymentGateway.STRIPE) {
                gatewayResponse = await this.stripeService.confirmPaymentIntent(paymentIntentId, paymentMethodId);
            }
            else if (payment.gateway === client_1.PaymentGateway.RAZORPAY) {
                gatewayResponse = await this.razorpayService.getOrder(paymentIntentId);
            }
            const updatedPayment = await this.prisma.payment.update({
                where: { id: payment.id },
                data: {
                    status: client_1.PaymentStatus.COMPLETED,
                    paidAt: new Date(),
                    gatewayResponse,
                },
                include: {
                    booking: true,
                },
            });
            await this.prisma.booking.update({
                where: { id: payment.bookingId },
                data: {
                    status: "CONFIRMED",
                    confirmedAt: new Date(),
                },
            });
            await this.createVendorPayout(payment);
            return updatedPayment;
        }
        catch (error) {
            await this.prisma.payment.update({
                where: { id: payment.id },
                data: {
                    status: client_1.PaymentStatus.FAILED,
                    failureReason: error.message,
                },
            });
            throw new common_1.BadRequestException(`Payment failed: ${error.message}`);
        }
    }
    async getUserPayments(userId, userRole, query) {
        const { page = 1, limit = 10, status, startDate, endDate } = query;
        const skip = (page - 1) * limit;
        let where = {};
        if (userRole === "user") {
            where.userId = userId;
        }
        else if (userRole === "vendor") {
            const vendor = await this.prisma.vendor.findUnique({
                where: { userId },
            });
            if (!vendor) {
                throw new common_1.NotFoundException("Vendor profile not found");
            }
            where = {
                booking: {
                    OR: [
                        { hotel: { vendorId: vendor.id } },
                        { guide: { vendorId: vendor.id } },
                        { package: { vendorId: vendor.id } },
                    ],
                },
            };
        }
        if (status) {
            where.status = status;
        }
        if (startDate || endDate) {
            where.createdAt = {};
            if (startDate)
                where.createdAt.gte = new Date(startDate);
            if (endDate)
                where.createdAt.lte = new Date(endDate);
        }
        const [payments, total] = await Promise.all([
            this.prisma.payment.findMany({
                where,
                include: {
                    booking: {
                        include: {
                            hotel: {
                                select: {
                                    name: true,
                                    city: true,
                                    state: true,
                                },
                            },
                            guide: {
                                select: {
                                    name: true,
                                    city: true,
                                    state: true,
                                },
                            },
                            package: {
                                select: {
                                    name: true,
                                    city: true,
                                    state: true,
                                },
                            },
                            user: {
                                select: {
                                    firstName: true,
                                    lastName: true,
                                    email: true,
                                },
                            },
                        },
                    },
                    refunds: true,
                },
                orderBy: {
                    createdAt: "desc",
                },
                skip,
                take: limit,
            }),
            this.prisma.payment.count({ where }),
        ]);
        return {
            payments,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getPaymentById(userId, paymentId, userRole) {
        const payment = await this.prisma.payment.findUnique({
            where: { id: paymentId },
            include: {
                booking: {
                    include: {
                        hotel: {
                            include: {
                                vendor: {
                                    select: {
                                        businessName: true,
                                        userId: true,
                                    },
                                },
                            },
                        },
                        guide: {
                            include: {
                                vendor: {
                                    select: {
                                        businessName: true,
                                        userId: true,
                                    },
                                },
                            },
                        },
                        package: {
                            include: {
                                vendor: {
                                    select: {
                                        businessName: true,
                                        userId: true,
                                    },
                                },
                            },
                        },
                        user: {
                            select: {
                                firstName: true,
                                lastName: true,
                                email: true,
                            },
                        },
                    },
                },
                refunds: true,
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        });
        if (!payment) {
            throw new common_1.NotFoundException("Payment not found");
        }
        const isOwner = payment.userId === userId;
        const isVendor = userRole === "vendor" &&
            (payment.booking.hotel?.vendor?.userId === userId ||
                payment.booking.guide?.vendor?.userId === userId ||
                payment.booking.package?.vendor?.userId === userId);
        const isAdmin = userRole === "admin";
        if (!isOwner && !isVendor && !isAdmin) {
            throw new common_1.ForbiddenException("Access denied");
        }
        return payment;
    }
    async processRefund(userId, paymentId, refundDto, userRole) {
        const payment = await this.getPaymentById(userId, paymentId, userRole);
        if (payment.status !== client_1.PaymentStatus.COMPLETED) {
            throw new common_1.BadRequestException("Only completed payments can be refunded");
        }
        const { amount, reason } = refundDto;
        const maxRefundAmount = payment.amount - (payment.refunds?.reduce((sum, r) => sum + r.amount, 0) || 0);
        if (amount > maxRefundAmount) {
            throw new common_1.BadRequestException("Refund amount exceeds available refund amount");
        }
        try {
            let gatewayRefund;
            if (payment.gateway === client_1.PaymentGateway.STRIPE) {
                gatewayRefund = await this.stripeService.createRefund({
                    paymentIntent: payment.gatewayPaymentId,
                    amount: Math.round(amount * 100),
                    reason: "requested_by_customer",
                    metadata: {
                        paymentId: payment.id,
                        refundReason: reason,
                    },
                });
            }
            else if (payment.gateway === client_1.PaymentGateway.RAZORPAY) {
                gatewayRefund = await this.razorpayService.createRefund({
                    paymentId: payment.gatewayPaymentId,
                    amount: Math.round(amount * 100),
                    notes: {
                        paymentId: payment.id,
                        refundReason: reason,
                    },
                });
            }
            const refund = await this.prisma.refund.create({
                data: {
                    paymentId: payment.id,
                    amount,
                    reason,
                    status: "PENDING",
                    gatewayRefundId: gatewayRefund.id,
                    gatewayResponse: gatewayRefund,
                    processedBy: userId,
                },
            });
            if (amount === payment.amount) {
                await this.prisma.booking.update({
                    where: { id: payment.bookingId },
                    data: {
                        status: "CANCELLED",
                        cancelledAt: new Date(),
                        cancellationReason: reason,
                    },
                });
            }
            return refund;
        }
        catch (error) {
            throw new common_1.BadRequestException(`Refund failed: ${error.message}`);
        }
    }
    async getSavedPaymentMethods(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: {
                stripeCustomerId: true,
                razorpayCustomerId: true,
            },
        });
        if (!user) {
            throw new common_1.NotFoundException("User not found");
        }
        const methods = [];
        if (user.stripeCustomerId) {
            const stripeMethods = await this.stripeService.getCustomerPaymentMethods(user.stripeCustomerId);
            methods.push(...stripeMethods.map((method) => ({ ...method, gateway: "STRIPE" })));
        }
        if (user.razorpayCustomerId) {
        }
        return methods;
    }
    async savePaymentMethod(userId, paymentMethodId, isDefault = false) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException("User not found");
        }
        let stripeCustomerId = user.stripeCustomerId;
        if (!stripeCustomerId) {
            const customer = await this.stripeService.createCustomer({
                email: user.email,
                name: `${user.firstName} ${user.lastName}`,
                metadata: {
                    userId: user.id,
                },
            });
            stripeCustomerId = customer.id;
            await this.prisma.user.update({
                where: { id: userId },
                data: { stripeCustomerId },
            });
        }
        await this.stripeService.attachPaymentMethodToCustomer(paymentMethodId, stripeCustomerId);
        if (isDefault) {
            await this.stripeService.setDefaultPaymentMethod(stripeCustomerId, paymentMethodId);
        }
        return { success: true, paymentMethodId, isDefault };
    }
    async setDefaultPaymentMethod(userId, paymentMethodId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: { stripeCustomerId: true },
        });
        if (!user?.stripeCustomerId) {
            throw new common_1.NotFoundException("Customer not found");
        }
        await this.stripeService.setDefaultPaymentMethod(user.stripeCustomerId, paymentMethodId);
        return { success: true };
    }
    async getVendorRevenueAnalytics(userId, startDate, endDate) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const where = {
            booking: {
                OR: [
                    { hotel: { vendorId: vendor.id } },
                    { guide: { vendorId: vendor.id } },
                    { package: { vendorId: vendor.id } },
                ],
            },
            status: client_1.PaymentStatus.COMPLETED,
        };
        if (startDate || endDate) {
            where.paidAt = {};
            if (startDate)
                where.paidAt.gte = new Date(startDate);
            if (endDate)
                where.paidAt.lte = new Date(endDate);
        }
        const [totalRevenue, totalPayments, monthlyRevenue, recentPayments] = await Promise.all([
            this.prisma.payment.aggregate({
                where,
                _sum: {
                    amount: true,
                    subtotal: true,
                    platformFee: true,
                },
            }),
            this.prisma.payment.count({ where }),
            this.prisma.$queryRaw `
        SELECT 
          DATE_TRUNC('month', "paidAt") as month,
          SUM(amount) as total_revenue,
          SUM(subtotal) as vendor_revenue,
          SUM("platformFee") as platform_fees,
          COUNT(*) as payment_count
        FROM "Payment" p
        JOIN "Booking" b ON p."bookingId" = b.id
        WHERE p.status = 'COMPLETED'
        AND (
          EXISTS (SELECT 1 FROM "Hotel" h WHERE h.id = b."hotelId" AND h."vendorId" = ${vendor.id}) OR
          EXISTS (SELECT 1 FROM "Guide" g WHERE g.id = b."guideId" AND g."vendorId" = ${vendor.id}) OR
          EXISTS (SELECT 1 FROM "Package" pkg WHERE pkg.id = b."packageId" AND pkg."vendorId" = ${vendor.id})
        )
        ${startDate ? `AND p."paidAt" >= ${startDate}` : ""}
        ${endDate ? `AND p."paidAt" <= ${endDate}` : ""}
        GROUP BY DATE_TRUNC('month', "paidAt")
        ORDER BY month DESC
        LIMIT 12
      `,
            this.prisma.payment.findMany({
                where,
                include: {
                    booking: {
                        include: {
                            hotel: { select: { name: true } },
                            guide: { select: { name: true } },
                            package: { select: { name: true } },
                            user: {
                                select: {
                                    firstName: true,
                                    lastName: true,
                                    email: true,
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    paidAt: "desc",
                },
                take: 10,
            }),
        ]);
        return {
            totalRevenue: totalRevenue._sum.amount || 0,
            vendorRevenue: (totalRevenue._sum.subtotal || 0) - (totalRevenue._sum.platformFee || 0),
            platformFees: totalRevenue._sum.platformFee || 0,
            totalPayments,
            monthlyRevenue,
            recentPayments,
        };
    }
    async createVendorPayout(payment) {
        const vendorRevenue = payment.subtotal - payment.platformFee;
        await this.prisma.payout.create({
            data: {
                vendorId: payment.booking.hotel?.vendorId || payment.booking.guide?.vendorId || payment.booking.package?.vendorId,
                paymentId: payment.id,
                amount: vendorRevenue,
                status: "PENDING",
                scheduledFor: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            },
        });
    }
    generatePaymentReference() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8);
        return `PAY${timestamp}${random}`.toUpperCase();
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function, Function, Function, Function])
], PaymentService);
//# sourceMappingURL=payment.service.js.map