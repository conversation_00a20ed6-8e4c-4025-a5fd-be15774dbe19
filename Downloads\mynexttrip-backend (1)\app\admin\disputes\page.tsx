"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, AlertTriangle, Clock, CheckCircle, XCircle, MessageSquare, User, FileText } from "lucide-react"
import { api } from "@/lib/api"
import { MainLayout } from "@/components/layout/main-layout"
import { format } from "date-fns"

interface Dispute {
  id: string
  disputeNumber: string
  type: "booking" | "payment" | "service" | "refund" | "other"
  status: "open" | "investigating" | "resolved" | "closed"
  priority: "high" | "medium" | "low"
  subject: string
  description: string
  customerName: string
  customerEmail: string
  customerAvatar?: string
  vendorName: string
  vendorEmail: string
  bookingId?: string
  amount: number
  createdAt: string
  updatedAt: string
  assignedTo?: string
  resolution?: string
  messages: DisputeMessage[]
}

interface DisputeMessage {
  id: string
  sender: "customer" | "vendor" | "admin"
  senderName: string
  message: string
  timestamp: string
  attachments?: string[]
}

export default function AdminDisputes() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [selectedDispute, setSelectedDispute] = useState<Dispute | null>(null)
  const [resolutionNote, setResolutionNote] = useState("")

  const { data: disputes, isLoading } = useQuery({
    queryKey: ["admin-disputes", searchQuery, statusFilter, priorityFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        search: searchQuery,
        ...(statusFilter !== "all" && { status: statusFilter }),
        ...(priorityFilter !== "all" && { priority: priorityFilter }),
      })
      const response = await api.get(`/admin/disputes?${params}`)
      return response.data as Dispute[]
    },
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-red-100 text-red-800"
      case "investigating":
        return "bg-yellow-100 text-yellow-800"
      case "resolved":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open":
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case "investigating":
        return <Clock className="h-4 w-4 text-yellow-600" />
      case "resolved":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "closed":
        return <XCircle className="h-4 w-4 text-gray-600" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  const handleStatusUpdate = async (disputeId: string, newStatus: string) => {
    try {
      await api.patch(`/admin/disputes/${disputeId}/status`, {
        status: newStatus,
        resolution: resolutionNote,
      })
      setResolutionNote("")
      // Refetch disputes
    } catch (error) {
      console.error("Failed to update dispute status:", error)
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Dispute Management</h1>
          <p className="text-muted-foreground">Handle customer disputes and resolve issues</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Disputes List */}
          <div className="lg:col-span-2 space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search disputes..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="open">Open</SelectItem>
                      <SelectItem value="investigating">Investigating</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priority</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Disputes */}
            <div className="space-y-4">
              {disputes?.map((dispute) => (
                <Card
                  key={dispute.id}
                  className={`cursor-pointer transition-colors ${
                    selectedDispute?.id === dispute.id ? "ring-2 ring-primary" : ""
                  }`}
                  onClick={() => setSelectedDispute(dispute)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{dispute.disputeNumber}</h3>
                          <Badge className={getStatusColor(dispute.status)}>
                            {getStatusIcon(dispute.status)}
                            <span className="ml-1 capitalize">{dispute.status}</span>
                          </Badge>
                          <Badge className={getPriorityColor(dispute.priority)}>{dispute.priority}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground capitalize">{dispute.type} dispute</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">₹{dispute.amount.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(dispute.createdAt), "MMM dd, yyyy")}
                        </p>
                      </div>
                    </div>

                    <h4 className="font-medium mb-2">{dispute.subject}</h4>
                    <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{dispute.description}</p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>{dispute.customerName}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          <span>{dispute.messages.length} messages</span>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="bg-transparent">
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Dispute Details */}
          <div className="lg:col-span-1">
            {selectedDispute ? (
              <Card className="sticky top-4">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Dispute Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Info */}
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium mb-1">{selectedDispute.subject}</h4>
                      <p className="text-sm text-muted-foreground">{selectedDispute.description}</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Amount</p>
                        <p className="font-semibold">₹{selectedDispute.amount.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Type</p>
                        <p className="font-semibold capitalize">{selectedDispute.type}</p>
                      </div>
                    </div>
                  </div>

                  {/* Parties */}
                  <div className="space-y-3">
                    <h5 className="font-medium">Involved Parties</h5>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={selectedDispute.customerAvatar || "/placeholder.svg"} />
                          <AvatarFallback>{selectedDispute.customerName.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{selectedDispute.customerName}</p>
                          <p className="text-xs text-muted-foreground">Customer</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{selectedDispute.vendorName.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{selectedDispute.vendorName}</p>
                          <p className="text-xs text-muted-foreground">Vendor</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="space-y-3">
                    <h5 className="font-medium">Recent Messages</h5>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {selectedDispute.messages.slice(-3).map((message) => (
                        <div key={message.id} className="p-2 bg-muted/50 rounded text-xs">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">{message.senderName}</span>
                            <span className="text-muted-foreground">
                              {format(new Date(message.timestamp), "MMM dd, HH:mm")}
                            </span>
                          </div>
                          <p>{message.message}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  {selectedDispute.status !== "resolved" && selectedDispute.status !== "closed" && (
                    <div className="space-y-3">
                      <h5 className="font-medium">Resolution</h5>
                      <Textarea
                        placeholder="Add resolution notes..."
                        value={resolutionNote}
                        onChange={(e) => setResolutionNote(e.target.value)}
                        rows={3}
                      />
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleStatusUpdate(selectedDispute.id, "resolved")}
                          className="flex-1"
                        >
                          Resolve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(selectedDispute.id, "investigating")}
                          className="flex-1 bg-transparent"
                        >
                          Investigate
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card className="sticky top-4">
                <CardContent className="py-12 text-center">
                  <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-semibold text-lg mb-2">Select a Dispute</h3>
                  <p className="text-muted-foreground text-sm">
                    Choose a dispute from the list to view details and take action.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
