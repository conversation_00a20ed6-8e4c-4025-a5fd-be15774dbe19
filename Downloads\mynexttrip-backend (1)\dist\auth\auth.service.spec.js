"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const jwt_1 = require("@nestjs/jwt");
const auth_service_1 = require("./auth.service");
const prisma_service_1 = require("../common/services/prisma.service");
const redis_service_1 = require("../common/services/redis.service");
const common_1 = require("@nestjs/common");
const bcrypt = require("bcrypt");
const globals_1 = require("@jest/globals");
describe("AuthService", () => {
    let service;
    let prismaService;
    let jwtService;
    let redisService;
    const mockPrismaService = {
        user: {
            findUnique: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            update: globals_1.jest.fn(),
        },
    };
    const mockJwtService = {
        sign: globals_1.jest.fn(),
        verify: globals_1.jest.fn(),
    };
    const mockRedisService = {
        set: globals_1.jest.fn(),
        get: globals_1.jest.fn(),
        del: globals_1.jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                auth_service_1.AuthService,
                { provide: prisma_service_1.PrismaService, useValue: mockPrismaService },
                { provide: jwt_1.JwtService, useValue: mockJwtService },
                { provide: redis_service_1.RedisService, useValue: mockRedisService },
            ],
        }).compile();
        service = module.get(auth_service_1.AuthService);
        prismaService = module.get(prisma_service_1.PrismaService);
        jwtService = module.get(jwt_1.JwtService);
        redisService = module.get(redis_service_1.RedisService);
    });
    describe("register", () => {
        it("should create a new user successfully", async () => {
            const registerDto = {
                email: "<EMAIL>",
                password: "password123",
                firstName: "John",
                lastName: "Doe",
            };
            mockPrismaService.user.findUnique.mockResolvedValue(null);
            mockPrismaService.user.create.mockResolvedValue({
                id: "1",
                email: registerDto.email,
                firstName: registerDto.firstName,
                lastName: registerDto.lastName,
            });
            const result = await service.register(registerDto);
            expect(result).toHaveProperty("user");
            expect(result.user.email).toBe(registerDto.email);
        });
        it("should throw ConflictException if user already exists", async () => {
            const registerDto = {
                email: "<EMAIL>",
                password: "password123",
                firstName: "John",
                lastName: "Doe",
            };
            mockPrismaService.user.findUnique.mockResolvedValue({ id: "1" });
            await expect(service.register(registerDto)).rejects.toThrow(common_1.ConflictException);
        });
    });
    describe("login", () => {
        it("should login user successfully", async () => {
            const loginDto = {
                email: "<EMAIL>",
                password: "password123",
            };
            const hashedPassword = await bcrypt.hash(loginDto.password, 10);
            const user = {
                id: "1",
                email: loginDto.email,
                password: hashedPassword,
                role: "USER",
            };
            mockPrismaService.user.findUnique.mockResolvedValue(user);
            mockJwtService.sign.mockReturnValue("jwt-token");
            const result = await service.login(loginDto);
            expect(result).toHaveProperty("accessToken");
            expect(result).toHaveProperty("user");
        });
        it("should throw UnauthorizedException for invalid credentials", async () => {
            const loginDto = {
                email: "<EMAIL>",
                password: "wrongpassword",
            };
            mockPrismaService.user.findUnique.mockResolvedValue(null);
            await expect(service.login(loginDto)).rejects.toThrow(common_1.UnauthorizedException);
        });
    });
});
//# sourceMappingURL=auth.service.spec.js.map