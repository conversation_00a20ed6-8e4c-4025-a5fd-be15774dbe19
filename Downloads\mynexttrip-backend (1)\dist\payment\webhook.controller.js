"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
let WebhookController = class WebhookController {
    constructor(paymentService, stripeService, razorpayService, prisma) {
        this.paymentService = paymentService;
        this.stripeService = stripeService;
        this.razorpayService = razorpayService;
        this.prisma = prisma;
    }
    async handleStripeWebhook(req, signature) {
        try {
            const payload = req.body;
            const event = await this.stripeService.constructWebhookEvent(payload, signature);
            switch (event.type) {
                case "payment_intent.succeeded":
                    await this.handleStripePaymentSuccess(event.data.object);
                    break;
                case "payment_intent.payment_failed":
                    await this.handleStripePaymentFailed(event.data.object);
                    break;
                case "refund.created":
                    await this.handleStripeRefundCreated(event.data.object);
                    break;
                case "refund.updated":
                    await this.handleStripeRefundUpdated(event.data.object);
                    break;
                default:
                    console.log(`Unhandled Stripe event type: ${event.type}`);
            }
            return { received: true };
        }
        catch (error) {
            console.error("Stripe webhook error:", error);
            throw error;
        }
    }
    async handleRazorpayWebhook(req, signature) {
        try {
            const payload = req.body;
            const isValid = await this.razorpayService.verifyWebhookSignature(payload.toString(), signature);
            if (!isValid) {
                throw new Error("Invalid webhook signature");
            }
            const event = JSON.parse(payload.toString());
            switch (event.event) {
                case "payment.captured":
                    await this.handleRazorpayPaymentCaptured(event.payload.payment.entity);
                    break;
                case "payment.failed":
                    await this.handleRazorpayPaymentFailed(event.payload.payment.entity);
                    break;
                case "refund.created":
                    await this.handleRazorpayRefundCreated(event.payload.refund.entity);
                    break;
                case "refund.processed":
                    await this.handleRazorpayRefundProcessed(event.payload.refund.entity);
                    break;
                default:
                    console.log(`Unhandled Razorpay event type: ${event.event}`);
            }
            return { received: true };
        }
        catch (error) {
            console.error("Razorpay webhook error:", error);
            throw error;
        }
    }
    async handleStripePaymentSuccess(paymentIntent) {
        const payment = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: paymentIntent.id },
            include: { booking: true },
        });
        if (payment && payment.status === client_1.PaymentStatus.PENDING) {
            await this.prisma.payment.update({
                where: { id: payment.id },
                data: {
                    status: client_1.PaymentStatus.COMPLETED,
                    paidAt: new Date(),
                    gatewayResponse: paymentIntent,
                },
            });
            await this.prisma.booking.update({
                where: { id: payment.bookingId },
                data: {
                    status: "CONFIRMED",
                    confirmedAt: new Date(),
                },
            });
            await this.createVendorPayout(payment);
        }
    }
    async handleStripePaymentFailed(paymentIntent) {
        const payment = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: paymentIntent.id },
        });
        if (payment && payment.status === client_1.PaymentStatus.PENDING) {
            await this.prisma.payment.update({
                where: { id: payment.id },
                data: {
                    status: client_1.PaymentStatus.FAILED,
                    failureReason: paymentIntent.last_payment_error?.message || "Payment failed",
                    gatewayResponse: paymentIntent,
                },
            });
        }
    }
    async handleStripeRefundCreated(refund) {
        const payment = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: refund.payment_intent },
        });
        if (payment) {
            await this.prisma.refund.updateMany({
                where: {
                    paymentId: payment.id,
                    gatewayRefundId: refund.id,
                },
                data: {
                    status: "PROCESSING",
                    gatewayResponse: refund,
                },
            });
        }
    }
    async handleStripeRefundUpdated(refund) {
        const payment = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: refund.payment_intent },
        });
        if (payment) {
            await this.prisma.refund.updateMany({
                where: {
                    paymentId: payment.id,
                    gatewayRefundId: refund.id,
                },
                data: {
                    status: refund.status === "succeeded" ? "COMPLETED" : "FAILED",
                    processedAt: refund.status === "succeeded" ? new Date() : undefined,
                    gatewayResponse: refund,
                },
            });
        }
    }
    async handleRazorpayPaymentCaptured(payment) {
        const paymentRecord = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: payment.order_id },
            include: { booking: true },
        });
        if (paymentRecord && paymentRecord.status === client_1.PaymentStatus.PENDING) {
            await this.prisma.payment.update({
                where: { id: paymentRecord.id },
                data: {
                    status: client_1.PaymentStatus.COMPLETED,
                    paidAt: new Date(),
                    gatewayResponse: payment,
                },
            });
            await this.prisma.booking.update({
                where: { id: paymentRecord.bookingId },
                data: {
                    status: "CONFIRMED",
                    confirmedAt: new Date(),
                },
            });
            await this.createVendorPayout(paymentRecord);
        }
    }
    async handleRazorpayPaymentFailed(payment) {
        const paymentRecord = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: payment.order_id },
        });
        if (paymentRecord && paymentRecord.status === client_1.PaymentStatus.PENDING) {
            await this.prisma.payment.update({
                where: { id: paymentRecord.id },
                data: {
                    status: client_1.PaymentStatus.FAILED,
                    failureReason: payment.error_description || "Payment failed",
                    gatewayResponse: payment,
                },
            });
        }
    }
    async handleRazorpayRefundCreated(refund) {
        const payment = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: refund.payment_id },
        });
        if (payment) {
            await this.prisma.refund.updateMany({
                where: {
                    paymentId: payment.id,
                    gatewayRefundId: refund.id,
                },
                data: {
                    status: "PROCESSING",
                    gatewayResponse: refund,
                },
            });
        }
    }
    async handleRazorpayRefundProcessed(refund) {
        const payment = await this.prisma.payment.findFirst({
            where: { gatewayPaymentId: refund.payment_id },
        });
        if (payment) {
            await this.prisma.refund.updateMany({
                where: {
                    paymentId: payment.id,
                    gatewayRefundId: refund.id,
                },
                data: {
                    status: "COMPLETED",
                    processedAt: new Date(),
                    gatewayResponse: refund,
                },
            });
        }
    }
    async createVendorPayout(payment) {
        const vendorRevenue = payment.subtotal - payment.platformFee;
        await this.prisma.payout.create({
            data: {
                vendorId: payment.booking.hotel?.vendorId || payment.booking.guide?.vendorId || payment.booking.package?.vendorId,
                paymentId: payment.id,
                amount: vendorRevenue,
                status: "PENDING",
                scheduledFor: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            },
        });
    }
};
exports.WebhookController = WebhookController;
__decorate([
    (0, common_1.Post)("stripe"),
    (0, swagger_1.ApiExcludeEndpoint)(),
    (0, swagger_1.ApiOperation)({ summary: "Handle Stripe webhooks" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Webhook processed successfully" }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)("stripe-signature")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebhookController.prototype, "handleStripeWebhook", null);
__decorate([
    (0, common_1.Post)("razorpay"),
    (0, swagger_1.ApiExcludeEndpoint)(),
    (0, swagger_1.ApiOperation)({ summary: "Handle Razorpay webhooks" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Webhook processed successfully" }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)("x-razorpay-signature")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebhookController.prototype, "handleRazorpayWebhook", null);
exports.WebhookController = WebhookController = __decorate([
    (0, swagger_1.ApiTags)("Payment Webhooks"),
    (0, common_1.Controller)("webhooks"),
    __metadata("design:paramtypes", [Function, Function, Function, Function])
], WebhookController);
//# sourceMappingURL=webhook.controller.js.map