import { type NestInterceptor, type ExecutionContext, type <PERSON><PERSON><PERSON><PERSON> } from "@nestjs/common";
import type { Observable } from "rxjs";
export interface Response<T> {
    success: boolean;
    data: T;
    message?: string;
    timestamp: string;
}
export declare class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
    intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>>;
}
