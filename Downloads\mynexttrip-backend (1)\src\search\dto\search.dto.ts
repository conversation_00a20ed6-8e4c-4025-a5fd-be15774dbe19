import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsOptional, IsString, IsNumber, IsArray, IsEnum, IsDateString, IsInt, Min, Max } from "class-validator"
import { Type, Transform } from "class-transformer"

export class SearchQueryDto {
  @ApiPropertyOptional({ description: "Search query" })
  @IsOptional()
  @IsString()
  q?: string

  @ApiPropertyOptional({ description: "Listing type", enum: ["hotel", "guide", "package"] })
  @IsOptional()
  @IsEnum(["hotel", "guide", "package"])
  type?: string

  @ApiPropertyOptional({ description: "City" })
  @IsOptional()
  @IsString()
  city?: string

  @ApiPropertyOptional({ description: "State" })
  @IsOptional()
  @IsString()
  state?: string

  @ApiPropertyOptional({ description: "Minimum price" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minPrice?: number

  @ApiPropertyOptional({ description: "Maximum price" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxPrice?: number

  @ApiPropertyOptional({ description: "Minimum rating" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(5)
  rating?: number

  @ApiPropertyOptional({ description: "Star rating for hotels" })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(5)
  starRating?: number

  @ApiPropertyOptional({ description: "Amenities", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => (typeof value === "string" ? value.split(",") : value))
  amenities?: string[]

  @ApiPropertyOptional({ description: "Check-in date (YYYY-MM-DD)" })
  @IsOptional()
  @IsDateString()
  checkIn?: string

  @ApiPropertyOptional({ description: "Check-out date (YYYY-MM-DD)" })
  @IsOptional()
  @IsDateString()
  checkOut?: string

  @ApiPropertyOptional({ description: "Number of guests" })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(20)
  guests?: number

  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20

  @ApiPropertyOptional({
    description: "Sort by",
    enum: ["relevance", "price_low", "price_high", "rating", "reviews", "newest"],
    default: "relevance",
  })
  @IsOptional()
  @IsEnum(["relevance", "price_low", "price_high", "rating", "reviews", "newest"])
  sortBy?: string = "relevance"
}

export class SuggestionsQueryDto {
  @ApiPropertyOptional({ description: "Search query for suggestions" })
  @IsString()
  q: string
}

export class LocationSearchDto {
  @ApiPropertyOptional({ description: "Location search query" })
  @IsString()
  q: string
}
