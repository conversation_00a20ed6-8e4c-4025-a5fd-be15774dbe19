import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import {
  IsString,
  IsEmail,
  IsOptional,
  IsEnum,
  IsPhoneNumber,
  Length,
  IsDateString,
  IsInt,
  Min,
  Max,
} from "class-validator"
import { Type } from "class-transformer"
import { BusinessType } from "@prisma/client"

export class CreateVendorDto {
  @ApiProperty({ description: "Business name" })
  @IsString()
  @Length(2, 100)
  businessName: string

  @ApiProperty({ enum: BusinessType, description: "Type of business" })
  @IsEnum(BusinessType)
  businessType: BusinessType

  @ApiPropertyOptional({ description: "Business description" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string

  @ApiProperty({ description: "Contact phone number" })
  @IsPhoneNumber("IN")
  contactPhone: string

  @ApiProperty({ description: "Contact email" })
  @IsEmail()
  contactEmail: string

  @ApiProperty({ description: "Business address" })
  @IsString()
  @Length(10, 200)
  address: string

  @ApiProperty({ description: "City" })
  @IsString()
  @Length(2, 50)
  city: string

  @ApiProperty({ description: "State" })
  @IsString()
  @Length(2, 50)
  state: string

  @ApiProperty({ description: "PIN code" })
  @IsString()
  @Length(6, 6)
  pincode: string

  @ApiPropertyOptional({ description: "GST number" })
  @IsOptional()
  @IsString()
  @Length(15, 15)
  gstNumber?: string

  @ApiPropertyOptional({ description: "PAN number" })
  @IsOptional()
  @IsString()
  @Length(10, 10)
  panNumber?: string

  @ApiProperty({ description: "Bank account number" })
  @IsString()
  @Length(9, 18)
  bankAccountNumber: string

  @ApiProperty({ description: "Bank IFSC code" })
  @IsString()
  @Length(11, 11)
  bankIfscCode: string

  @ApiProperty({ description: "Bank account holder name" })
  @IsString()
  @Length(2, 100)
  bankAccountHolderName: string
}

export class UpdateVendorDto {
  @ApiPropertyOptional({ description: "Business name" })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  businessName?: string

  @ApiPropertyOptional({ description: "Business description" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string

  @ApiPropertyOptional({ description: "Contact phone number" })
  @IsOptional()
  @IsPhoneNumber("IN")
  contactPhone?: string

  @ApiPropertyOptional({ description: "Contact email" })
  @IsOptional()
  @IsEmail()
  contactEmail?: string

  @ApiPropertyOptional({ description: "Business address" })
  @IsOptional()
  @IsString()
  @Length(10, 200)
  address?: string

  @ApiPropertyOptional({ description: "City" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  city?: string

  @ApiPropertyOptional({ description: "State" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  state?: string

  @ApiPropertyOptional({ description: "PIN code" })
  @IsOptional()
  @IsString()
  @Length(6, 6)
  pincode?: string

  @ApiPropertyOptional({ description: "GST number" })
  @IsOptional()
  @IsString()
  @Length(15, 15)
  gstNumber?: string

  @ApiPropertyOptional({ description: "PAN number" })
  @IsOptional()
  @IsString()
  @Length(10, 10)
  panNumber?: string

  @ApiPropertyOptional({ description: "Bank account number" })
  @IsOptional()
  @IsString()
  @Length(9, 18)
  bankAccountNumber?: string

  @ApiPropertyOptional({ description: "Bank IFSC code" })
  @IsOptional()
  @IsString()
  @Length(11, 11)
  bankIfscCode?: string

  @ApiPropertyOptional({ description: "Bank account holder name" })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  bankAccountHolderName?: string
}

export class VendorQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10

  @ApiPropertyOptional({ description: "Filter by status" })
  @IsOptional()
  @IsString()
  status?: string

  @ApiPropertyOptional({ description: "Start date filter" })
  @IsOptional()
  @IsDateString()
  startDate?: string

  @ApiPropertyOptional({ description: "End date filter" })
  @IsOptional()
  @IsDateString()
  endDate?: string
}
