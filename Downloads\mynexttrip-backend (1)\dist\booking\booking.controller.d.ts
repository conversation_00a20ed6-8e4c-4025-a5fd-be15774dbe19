import type { BookingService } from "./booking.service";
import type { CreateBookingDto, UpdateBookingDto, BookingQueryDto, AvailabilityCheckDto } from "./dto/booking.dto";
export declare class BookingController {
    private readonly bookingService;
    constructor(bookingService: BookingService);
    checkAvailability(req: any, availabilityDto: AvailabilityCheckDto): Promise<any>;
    createBooking(req: any, createBookingDto: CreateBookingDto): Promise<any>;
    getUserBookings(req: any, query: BookingQueryDto): Promise<any>;
    getBooking(req: any, id: string): Promise<any>;
    updateBooking(req: any, id: string, updateBookingDto: UpdateBookingDto): Promise<any>;
    cancelBooking(req: any, id: string): Promise<any>;
    confirmBooking(req: any, id: string): Promise<any>;
    rejectBooking(req: any, id: string, body: {
        reason?: string;
    }): Promise<any>;
    getInvoice(req: any, id: string): Promise<any>;
}
