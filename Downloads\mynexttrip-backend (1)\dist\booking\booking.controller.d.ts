import type { BookingService } from "./booking.service";
import type { CreateBookingDto, UpdateBookingDto, BookingQueryDto, AvailabilityCheckDto } from "./dto/booking.dto";
import { ApiResponseDto } from "../common/dto/api-response.dto";
export declare class BookingController {
    private readonly bookingService;
    constructor(bookingService: BookingService);
    checkAvailability(req: any, availabilityDto: AvailabilityCheckDto): Promise<ApiResponseDto<{
        isAvailable: boolean;
        availableRooms: any;
        conflictingBookings: number;
        hotel: {
            id: any;
            name: any;
            checkInTime: any;
            checkOutTime: any;
        };
    } | {
        isAvailable: boolean;
        unavailableDates: any;
        conflictingBookings: number;
        guide: {
            id: any;
            name: any;
            maxGroupSize: any;
            pricePerDay: any;
        };
    } | {
        isAvailable: boolean;
        package: {
            id: any;
            name: any;
            duration: any;
            maxGroupSize: any;
            price: any;
        };
    }>>;
    createBooking(req: any, createBookingDto: CreateBookingDto): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>>;
    getUserBookings(req: any, query: BookingQueryDto): Promise<ApiResponseDto<{
        bookings: {
            status: import(".prisma/client").$Enums.BookingStatus;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            availabilitySlotId: string | null;
            checkIn: Date;
            checkOut: Date | null;
            guests: number;
            totalAmount: number;
            currency: string;
            guestDetails: import("@prisma/client/runtime/library").JsonValue;
            specialRequests: string | null;
            paymentIntentId: string | null;
            paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
            confirmationCode: string;
            confirmedAt: Date | null;
            cancelledAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>>;
    getBooking(req: any, id: string): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>>;
    updateBooking(req: any, id: string, updateBookingDto: UpdateBookingDto): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>>;
    cancelBooking(req: any, id: string): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>>;
    confirmBooking(req: any, id: string): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>>;
    rejectBooking(req: any, id: string, body: {
        reason?: string;
    }): Promise<ApiResponseDto<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>>;
    getInvoice(req: any, id: string): Promise<ApiResponseDto<{
        bookingReference: any;
        invoiceNumber: string;
        issueDate: string;
        dueDate: any;
        customer: {
            name: string;
            email: any;
            phone: any;
        };
        vendor: {
            name: any;
            phone: any;
            email: any;
        };
        listing: {
            name: any;
            type: string;
            location: string;
        };
        bookingDetails: {
            checkIn: any;
            checkOut: any;
            guests: number;
            duration: any;
            room: any;
        };
        amount: {
            subtotal: number;
            taxes: number;
            total: number;
        };
        status: import(".prisma/client").$Enums.BookingStatus;
        payments: any;
    }>>;
}
