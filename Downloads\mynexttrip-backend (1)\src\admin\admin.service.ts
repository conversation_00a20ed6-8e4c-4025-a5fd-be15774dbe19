import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import type { PrismaService } from "../common/services/prisma.service"
import type { RedisService } from "../common/services/redis.service"
import type { AdminQueryDto, UserManagementDto, PlatformConfigDto } from "./dto/admin.dto"
import { UserStatus } from "@prisma/client"

@Injectable()
export class AdminService {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  async getDashboardOverview() {
    const cacheKey = "admin:dashboard:overview"
    const cached = await this.redis.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    const [
      totalUsers,
      totalVendors,
      totalListings,
      totalBookings,
      totalRevenue,
      pendingApprovals,
      activeDisputes,
      recentActivity,
    ] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.vendor.count(),
      this.prisma.$queryRaw`
        SELECT COUNT(*) as total FROM (
          SELECT id FROM "Hotel" WHERE status = 'ACTIVE'
          UNION ALL
          SELECT id FROM "Guide" WHERE status = 'ACTIVE'
          UNION ALL
          SELECT id FROM "Package" WHERE status = 'ACTIVE'
        ) AS listings
      `,
      this.prisma.booking.count(),
      this.prisma.payment.aggregate({
        where: { status: "COMPLETED" },
        _sum: { amount: true },
      }),
      this.prisma.vendor.count({ where: { status: "PENDING" } }),
      this.prisma.dispute.count({ where: { status: "OPEN" } }),
      this.getRecentActivity(),
    ])

    const dashboard = {
      stats: {
        totalUsers,
        totalVendors,
        totalListings: Number((totalListings as any)[0]?.total || 0),
        totalBookings,
        totalRevenue: totalRevenue._sum.amount || 0,
        pendingApprovals,
        activeDisputes,
      },
      recentActivity,
    }

    // Cache for 5 minutes
    await this.redis.setex(cacheKey, 300, JSON.stringify(dashboard))
    return dashboard
  }

  async getUsers(query: AdminQueryDto) {
    const { page = 1, limit = 20, search, status, role, startDate, endDate } = query
    const skip = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: "insensitive" } },
        { lastName: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { phone: { contains: search, mode: "insensitive" } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (role) {
      where.role = role
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          role: true,
          status: true,
          emailVerified: true,
          phoneVerified: true,
          createdAt: true,
          lastLoginAt: true,
          _count: {
            select: {
              bookings: true,
              payments: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      this.prisma.user.count({ where }),
    ])

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getUserById(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        vendor: {
          include: {
            _count: {
              select: {
                hotels: true,
                guides: true,
                packages: true,
              },
            },
          },
        },
        bookings: {
          include: {
            hotel: { select: { name: true } },
            guide: { select: { name: true } },
            package: { select: { name: true } },
          },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        payments: {
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        reviews: {
          include: {
            hotel: { select: { name: true } },
            guide: { select: { name: true } },
            package: { select: { name: true } },
          },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        _count: {
          select: {
            bookings: true,
            payments: true,
            reviews: true,
          },
        },
      },
    })

    if (!user) {
      throw new NotFoundException("User not found")
    }

    return user
  }

  async updateUser(userId: string, updateDto: UserManagementDto) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      throw new NotFoundException("User not found")
    }

    return this.prisma.user.update({
      where: { id: userId },
      data: updateDto,
    })
  }

  async suspendUser(userId: string, reason: string, duration?: number) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      throw new NotFoundException("User not found")
    }

    if (user.role === "admin") {
      throw new BadRequestException("Cannot suspend admin users")
    }

    const suspendedUntil = duration ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000) : undefined

    return this.prisma.user.update({
      where: { id: userId },
      data: {
        status: UserStatus.SUSPENDED,
        suspendedUntil,
        suspensionReason: reason,
      },
    })
  }

  async activateUser(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      throw new NotFoundException("User not found")
    }

    return this.prisma.user.update({
      where: { id: userId },
      data: {
        status: UserStatus.ACTIVE,
        suspendedUntil: null,
        suspensionReason: null,
      },
    })
  }

  async getBookings(query: AdminQueryDto) {
    const { page = 1, limit = 20, search, status, startDate, endDate } = query
    const skip = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { bookingReference: { contains: search, mode: "insensitive" } },
        { user: { email: { contains: search, mode: "insensitive" } } },
        { hotel: { name: { contains: search, mode: "insensitive" } } },
        { guide: { name: { contains: search, mode: "insensitive" } } },
        { package: { name: { contains: search, mode: "insensitive" } } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const [bookings, total] = await Promise.all([
      this.prisma.booking.findMany({
        where,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          hotel: {
            select: {
              name: true,
              city: true,
              vendor: {
                select: {
                  businessName: true,
                },
              },
            },
          },
          guide: {
            select: {
              name: true,
              city: true,
              vendor: {
                select: {
                  businessName: true,
                },
              },
            },
          },
          package: {
            select: {
              name: true,
              city: true,
              vendor: {
                select: {
                  businessName: true,
                },
              },
            },
          },
          payments: {
            select: {
              status: true,
              amount: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      this.prisma.booking.count({ where }),
    ])

    return {
      bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getPayments(query: AdminQueryDto) {
    const { page = 1, limit = 20, search, status, startDate, endDate } = query
    const skip = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { paymentReference: { contains: search, mode: "insensitive" } },
        { user: { email: { contains: search, mode: "insensitive" } } },
        { booking: { bookingReference: { contains: search, mode: "insensitive" } } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const [payments, total] = await Promise.all([
      this.prisma.payment.findMany({
        where,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          booking: {
            select: {
              bookingReference: true,
              hotel: { select: { name: true } },
              guide: { select: { name: true } },
              package: { select: { name: true } },
            },
          },
          refunds: {
            select: {
              amount: true,
              status: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      this.prisma.payment.count({ where }),
    ])

    return {
      payments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getDisputes(query: AdminQueryDto) {
    const { page = 1, limit = 20, status } = query
    const skip = (page - 1) * limit

    const where: any = {}
    if (status) {
      where.status = status
    }

    const [disputes, total] = await Promise.all([
      this.prisma.dispute.findMany({
        where,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          booking: {
            include: {
              hotel: { select: { name: true } },
              guide: { select: { name: true } },
              package: { select: { name: true } },
            },
          },
          payment: {
            select: {
              amount: true,
              paymentReference: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      this.prisma.dispute.count({ where }),
    ])

    return {
      disputes,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async resolveDispute(disputeId: string, resolution: string, refundAmount?: number) {
    const dispute = await this.prisma.dispute.findUnique({
      where: { id: disputeId },
      include: {
        payment: true,
        booking: true,
      },
    })

    if (!dispute) {
      throw new NotFoundException("Dispute not found")
    }

    if (dispute.status !== "OPEN") {
      throw new BadRequestException("Dispute is not open")
    }

    // Update dispute
    const updatedDispute = await this.prisma.dispute.update({
      where: { id: disputeId },
      data: {
        status: "RESOLVED",
        resolution,
        resolvedAt: new Date(),
      },
    })

    // Process refund if specified
    if (refundAmount && refundAmount > 0) {
      await this.prisma.refund.create({
        data: {
          paymentId: dispute.paymentId,
          amount: refundAmount,
          reason: `Dispute resolution: ${resolution}`,
          status: "PENDING",
          processedBy: "admin", // This should be the admin user ID
        },
      })
    }

    return updatedDispute
  }

  async getPlatformConfig() {
    const config = await this.prisma.platformConfig.findFirst()
    return config || this.getDefaultConfig()
  }

  async updatePlatformConfig(configDto: PlatformConfigDto) {
    const existingConfig = await this.prisma.platformConfig.findFirst()

    if (existingConfig) {
      return this.prisma.platformConfig.update({
        where: { id: existingConfig.id },
        data: configDto,
      })
    } else {
      return this.prisma.platformConfig.create({
        data: configDto,
      })
    }
  }

  async getRevenueReport(startDate?: string, endDate?: string, groupBy = "month") {
    const where: any = {
      status: "COMPLETED",
    }

    if (startDate || endDate) {
      where.paidAt = {}
      if (startDate) where.paidAt.gte = new Date(startDate)
      if (endDate) where.paidAt.lte = new Date(endDate)
    }

    const [totalRevenue, revenueByPeriod, revenueByGateway, topVendors] = await Promise.all([
      this.prisma.payment.aggregate({
        where,
        _sum: {
          amount: true,
          platformFee: true,
          taxAmount: true,
        },
        _count: true,
      }),

      this.prisma.$queryRaw`
        SELECT 
          DATE_TRUNC(${groupBy}, "paidAt") as period,
          SUM(amount) as total_revenue,
          SUM("platformFee") as platform_revenue,
          SUM("taxAmount") as tax_revenue,
          COUNT(*) as transaction_count
        FROM "Payment"
        WHERE status = 'COMPLETED'
        ${startDate ? `AND "paidAt" >= ${startDate}` : ""}
        ${endDate ? `AND "paidAt" <= ${endDate}` : ""}
        GROUP BY DATE_TRUNC(${groupBy}, "paidAt")
        ORDER BY period DESC
        LIMIT 12
      `,

      this.prisma.payment.groupBy({
        by: ["gateway"],
        where,
        _sum: {
          amount: true,
        },
        _count: true,
      }),

      this.prisma.$queryRaw`
        SELECT 
          v."businessName",
          v.id as vendor_id,
          SUM(p.amount) as total_revenue,
          COUNT(p.id) as transaction_count
        FROM "Payment" p
        JOIN "Booking" b ON p."bookingId" = b.id
        JOIN "Vendor" v ON (
          (b."hotelId" IS NOT NULL AND EXISTS (SELECT 1 FROM "Hotel" h WHERE h.id = b."hotelId" AND h."vendorId" = v.id)) OR
          (b."guideId" IS NOT NULL AND EXISTS (SELECT 1 FROM "Guide" g WHERE g.id = b."guideId" AND g."vendorId" = v.id)) OR
          (b."packageId" IS NOT NULL AND EXISTS (SELECT 1 FROM "Package" pkg WHERE pkg.id = b."packageId" AND pkg."vendorId" = v.id))
        )
        WHERE p.status = 'COMPLETED'
        ${startDate ? `AND p."paidAt" >= ${startDate}` : ""}
        ${endDate ? `AND p."paidAt" <= ${endDate}` : ""}
        GROUP BY v.id, v."businessName"
        ORDER BY total_revenue DESC
        LIMIT 10
      `,
    ])

    return {
      summary: {
        totalRevenue: totalRevenue._sum.amount || 0,
        platformRevenue: totalRevenue._sum.platformFee || 0,
        taxRevenue: totalRevenue._sum.taxAmount || 0,
        totalTransactions: totalRevenue._count,
      },
      revenueByPeriod,
      revenueByGateway,
      topVendors,
    }
  }

  async getActivityReport(startDate?: string, endDate?: string) {
    const where: any = {}
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const [userActivity, bookingActivity, listingActivity] = await Promise.all([
      this.prisma.user.groupBy({
        by: ["role"],
        where,
        _count: true,
      }),

      this.prisma.booking.groupBy({
        by: ["status"],
        where,
        _count: true,
      }),

      this.prisma.$queryRaw`
        SELECT 
          'hotel' as type,
          status,
          COUNT(*) as count
        FROM "Hotel"
        ${startDate || endDate ? "WHERE" : ""}
        ${startDate ? `"createdAt" >= ${startDate}` : ""}
        ${startDate && endDate ? "AND" : ""}
        ${endDate ? `"createdAt" <= ${endDate}` : ""}
        GROUP BY status
        UNION ALL
        SELECT 
          'guide' as type,
          status,
          COUNT(*) as count
        FROM "Guide"
        ${startDate || endDate ? "WHERE" : ""}
        ${startDate ? `"createdAt" >= ${startDate}` : ""}
        ${startDate && endDate ? "AND" : ""}
        ${endDate ? `"createdAt" <= ${endDate}` : ""}
        GROUP BY status
        UNION ALL
        SELECT 
          'package' as type,
          status,
          COUNT(*) as count
        FROM "Package"
        ${startDate || endDate ? "WHERE" : ""}
        ${startDate ? `"createdAt" >= ${startDate}` : ""}
        ${startDate && endDate ? "AND" : ""}
        ${endDate ? `"createdAt" <= ${endDate}` : ""}
        GROUP BY status
      `,
    ])

    return {
      userActivity,
      bookingActivity,
      listingActivity,
    }
  }

  private async getRecentActivity() {
    const [recentUsers, recentBookings, recentPayments] = await Promise.all([
      this.prisma.user.findMany({
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          createdAt: true,
        },
        orderBy: { createdAt: "desc" },
        take: 5,
      }),

      this.prisma.booking.findMany({
        select: {
          id: true,
          bookingReference: true,
          status: true,
          totalAmount: true,
          createdAt: true,
          user: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 5,
      }),

      this.prisma.payment.findMany({
        select: {
          id: true,
          paymentReference: true,
          amount: true,
          status: true,
          createdAt: true,
        },
        orderBy: { createdAt: "desc" },
        take: 5,
      }),
    ])

    return {
      recentUsers,
      recentBookings,
      recentPayments,
    }
  }

  private getDefaultConfig() {
    return {
      platformCommission: 5.0,
      taxRate: 18.0,
      payoutDelay: 7,
      maxRefundDays: 30,
      maintenanceMode: false,
      allowNewRegistrations: true,
      requireVendorApproval: true,
      requireListingApproval: true,
    }
  }
}
