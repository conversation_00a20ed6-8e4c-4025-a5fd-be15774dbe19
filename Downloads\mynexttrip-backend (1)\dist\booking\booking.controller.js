"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const api_response_dto_1 = require("../common/dto/api-response.dto");
let BookingController = class BookingController {
    constructor(bookingService) {
        this.bookingService = bookingService;
    }
    async checkAvailability(req, availabilityDto) {
        const availability = await this.bookingService.checkAvailability(availabilityDto);
        return new api_response_dto_1.ApiResponseDto(true, "Availability checked successfully", availability);
    }
    async createBooking(req, createBookingDto) {
        const booking = await this.bookingService.createBooking(req.user.id, createBookingDto);
        return new api_response_dto_1.ApiResponseDto(true, "Booking created successfully", booking);
    }
    async getUserBookings(req, query) {
        const bookings = await this.bookingService.getUserBookings(req.user.id, query);
        return new api_response_dto_1.ApiResponseDto(true, "Bookings retrieved successfully", bookings);
    }
    async getBooking(req, id) {
        const booking = await this.bookingService.getBookingById(req.user.id, id, req.user.role);
        return new api_response_dto_1.ApiResponseDto(true, "Booking retrieved successfully", booking);
    }
    async updateBooking(req, id, updateBookingDto) {
        const booking = await this.bookingService.updateBooking(req.user.id, id, updateBookingDto);
        return new api_response_dto_1.ApiResponseDto(true, "Booking updated successfully", booking);
    }
    async cancelBooking(req, id) {
        const booking = await this.bookingService.cancelBooking(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Booking cancelled successfully", booking);
    }
    async confirmBooking(req, id) {
        const booking = await this.bookingService.confirmBooking(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Booking confirmed successfully", booking);
    }
    async rejectBooking(req, id, body) {
        const booking = await this.bookingService.rejectBooking(req.user.id, id, body.reason);
        return new api_response_dto_1.ApiResponseDto(true, "Booking rejected successfully", booking);
    }
    async getInvoice(req, id) {
        const invoice = await this.bookingService.generateInvoice(req.user.id, id, req.user.role);
        return new api_response_dto_1.ApiResponseDto(true, "Invoice generated successfully", invoice);
    }
};
exports.BookingController = BookingController;
__decorate([
    (0, common_1.Post)("check-availability"),
    (0, swagger_1.ApiOperation)({ summary: "Check availability for booking" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Availability checked successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "checkAvailability", null);
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Create new booking" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: "Booking created successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "createBooking", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Get user bookings" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "getUserBookings", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, roles_decorator_1.Roles)("user", "vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get booking by ID" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "getBooking", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Update booking" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Function]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "updateBooking", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Cancel booking" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "cancelBooking", null);
__decorate([
    (0, common_1.Put)(":id/confirm"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Confirm booking (vendor only)" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "confirmBooking", null);
__decorate([
    (0, common_1.Put)(":id/reject"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Reject booking (vendor only)" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "rejectBooking", null);
__decorate([
    (0, common_1.Get)(":id/invoice"),
    (0, roles_decorator_1.Roles)("user", "vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get booking invoice" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "getInvoice", null);
exports.BookingController = BookingController = __decorate([
    (0, swagger_1.ApiTags)("Bookings"),
    (0, common_1.Controller)("bookings"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [Function])
], BookingController);
//# sourceMappingURL=booking.controller.js.map