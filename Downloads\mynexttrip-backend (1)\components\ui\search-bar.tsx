"use client"

import { useState } from "react"
import { Search, MapPin, Calendar, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { useSearchStore } from "@/lib/store"
import { format } from "date-fns"

interface SearchBarProps {
  onSearch?: () => void
  className?: string
}

export function SearchBar({ onSearch, className }: SearchBarProps) {
  const { destination, checkIn, checkOut, guests, setDestination, setCheckIn, setCheckOut, setGuests } =
    useSearchStore()
  const [showDatePicker, setShowDatePicker] = useState(false)

  const handleSearch = () => {
    if (destination && checkIn && checkOut) {
      onSearch?.()
    }
  }

  return (
    <Card className={`p-6 glass-effect luxury-shadow ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Destination */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">Where to?</label>
          <div className="relative">
            <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Delhi, Agra, Jaipur..."
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Check-in */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">Check-in</label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Select date"
              value={checkIn ? format(checkIn, "MMM dd, yyyy") : ""}
              onClick={() => setShowDatePicker(true)}
              readOnly
              className="pl-10 cursor-pointer"
            />
          </div>
        </div>

        {/* Check-out */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">Check-out</label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Select date"
              value={checkOut ? format(checkOut, "MMM dd, yyyy") : ""}
              onClick={() => setShowDatePicker(true)}
              readOnly
              className="pl-10 cursor-pointer"
            />
          </div>
        </div>

        {/* Guests */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">Guests</label>
          <div className="relative">
            <Users className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              type="number"
              min="1"
              max="10"
              value={guests}
              onChange={(e) => setGuests(Number.parseInt(e.target.value) || 1)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-center">
        <Button onClick={handleSearch} size="lg" className="px-8">
          <Search className="mr-2 h-4 w-4" />
          Search
        </Button>
      </div>
    </Card>
  )
}

export default SearchBar
