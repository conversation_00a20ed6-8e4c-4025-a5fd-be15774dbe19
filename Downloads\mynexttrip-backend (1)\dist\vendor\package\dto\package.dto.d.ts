import { PackageStatus, PackageCategory, DifficultyLevel } from "@prisma/client";
export declare class CreatePackageDto {
    name: string;
    description?: string;
    duration: number;
    maxGroupSize: number;
    price: number;
    inclusions: string[];
    exclusions?: string[];
    itinerary?: any;
    city: string;
    state: string;
    difficulty: DifficultyLevel;
    category: PackageCategory;
    cancellationPolicy?: string;
}
export declare class UpdatePackageDto {
    name?: string;
    description?: string;
    duration?: number;
    maxGroupSize?: number;
    price?: number;
    inclusions?: string[];
    exclusions?: string[];
    itinerary?: any;
    city?: string;
    state?: string;
    difficulty?: DifficultyLevel;
    category?: PackageCategory;
    cancellationPolicy?: string;
}
export declare class PackageQueryDto {
    page?: number;
    limit?: number;
    status?: PackageStatus;
    city?: string;
    category?: PackageCategory;
    search?: string;
}
