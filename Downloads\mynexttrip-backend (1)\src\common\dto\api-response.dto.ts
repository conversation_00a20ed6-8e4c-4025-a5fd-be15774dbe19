import { ApiProperty } from "@nestjs/swagger"

export class ApiResponseDto<T = any> {
  @ApiProperty({ description: "Success status of the request" })
  success: boolean

  @ApiProperty({ description: "Response message" })
  message: string

  @ApiProperty({ description: "Response data", required: false })
  data?: T

  @ApiProperty({ description: "Error details", required: false })
  error?: any

  @ApiProperty({ description: "Response timestamp" })
  timestamp: string

  @ApiProperty({ description: "Request path" })
  path: string

  constructor(
    success: boolean,
    message: string,
    data?: T,
    error?: any,
    path?: string,
  ) {
    this.success = success
    this.message = message
    this.data = data
    this.error = error
    this.timestamp = new Date().toISOString()
    this.path = path || ""
  }

  static success<T>(message: string, data?: T, path?: string): ApiResponseDto<T> {
    return new ApiResponseDto(true, message, data, undefined, path)
  }

  static error(message: string, error?: any, path?: string): ApiResponseDto {
    return new ApiResponseDto(false, message, undefined, error, path)
  }
}
