import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { CreateBookingDto, UpdateBookingDto, BookingQueryDto, AvailabilityCheckDto } from "./dto/booking.dto";
export declare class BookingService {
    private prisma;
    private redis;
    constructor(prisma: PrismaService, redis: RedisService);
    checkAvailability(availabilityDto: AvailabilityCheckDto): Promise<{
        isAvailable: boolean;
        availableRooms: any;
        conflictingBookings: any;
        hotel: {
            id: any;
            name: any;
            checkInTime: any;
            checkOutTime: any;
        };
    } | {
        isAvailable: boolean;
        unavailableDates: any;
        conflictingBookings: any;
        guide: {
            id: any;
            name: any;
            maxGroupSize: any;
            pricePerDay: any;
        };
    } | {
        isAvailable: boolean;
        package: {
            id: any;
            name: any;
            duration: any;
            maxGroupSize: any;
            price: any;
        };
    }>;
    createBooking(userId: string, createBookingDto: CreateBookingDto): Promise<any>;
    getUserBookings(userId: string, query: BookingQueryDto): Promise<{
        bookings: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getBookingById(userId: string, bookingId: string, userRole: string): Promise<any>;
    updateBooking(userId: string, bookingId: string, updateBookingDto: UpdateBookingDto): Promise<any>;
    cancelBooking(userId: string, bookingId: string): Promise<any>;
    confirmBooking(vendorUserId: string, bookingId: string): Promise<any>;
    rejectBooking(vendorUserId: string, bookingId: string, reason?: string): Promise<any>;
    generateInvoice(userId: string, bookingId: string, userRole: string): Promise<{
        bookingReference: any;
        invoiceNumber: string;
        issueDate: string;
        dueDate: any;
        customer: {
            name: string;
            email: any;
            phone: any;
        };
        vendor: {
            name: any;
            phone: any;
            email: any;
        };
        listing: {
            name: any;
            type: string;
            location: string;
        };
        bookingDetails: {
            checkIn: any;
            checkOut: any;
            guests: any;
            duration: any;
            room: any;
        };
        amount: {
            subtotal: any;
            taxes: number;
            total: number;
        };
        status: any;
        payments: any;
    }>;
    private checkHotelAvailability;
    private checkGuideAvailability;
    private checkPackageAvailability;
    private calculateBookingAmount;
    private calculateNights;
    private generateBookingReference;
    private updateInventoryAfterBooking;
    private restoreInventoryAfterCancellation;
}
