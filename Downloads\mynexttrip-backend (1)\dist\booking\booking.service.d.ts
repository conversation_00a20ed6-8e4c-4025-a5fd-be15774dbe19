import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { CreateBookingDto, UpdateBookingDto, BookingQueryDto, AvailabilityCheckDto } from "./dto/booking.dto";
export declare class BookingService {
    private prisma;
    private redis;
    constructor(prisma: PrismaService, redis: RedisService);
    checkAvailability(availabilityDto: AvailabilityCheckDto): Promise<{
        isAvailable: boolean;
        availableRooms: any;
        conflictingBookings: number;
        hotel: {
            id: any;
            name: any;
            checkInTime: any;
            checkOutTime: any;
        };
    } | {
        isAvailable: boolean;
        unavailableDates: any;
        conflictingBookings: number;
        guide: {
            id: any;
            name: any;
            maxGroupSize: any;
            pricePerDay: any;
        };
    } | {
        isAvailable: boolean;
        package: {
            id: any;
            name: any;
            duration: any;
            maxGroupSize: any;
            price: any;
        };
    }>;
    createBooking(userId: string, createBookingDto: CreateBookingDto): Promise<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>;
    getUserBookings(userId: string, query: BookingQueryDto): Promise<{
        bookings: {
            status: import(".prisma/client").$Enums.BookingStatus;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            availabilitySlotId: string | null;
            checkIn: Date;
            checkOut: Date | null;
            guests: number;
            totalAmount: number;
            currency: string;
            guestDetails: import("@prisma/client/runtime/library").JsonValue;
            specialRequests: string | null;
            paymentIntentId: string | null;
            paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
            confirmationCode: string;
            confirmedAt: Date | null;
            cancelledAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getBookingById(userId: string, bookingId: string, userRole: string): Promise<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>;
    updateBooking(userId: string, bookingId: string, updateBookingDto: UpdateBookingDto): Promise<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>;
    cancelBooking(userId: string, bookingId: string): Promise<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>;
    confirmBooking(vendorUserId: string, bookingId: string): Promise<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>;
    rejectBooking(vendorUserId: string, bookingId: string, reason?: string): Promise<{
        status: import(".prisma/client").$Enums.BookingStatus;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        availabilitySlotId: string | null;
        checkIn: Date;
        checkOut: Date | null;
        guests: number;
        totalAmount: number;
        currency: string;
        guestDetails: import("@prisma/client/runtime/library").JsonValue;
        specialRequests: string | null;
        paymentIntentId: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        confirmationCode: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
    }>;
    generateInvoice(userId: string, bookingId: string, userRole: string): Promise<{
        bookingReference: any;
        invoiceNumber: string;
        issueDate: string;
        dueDate: any;
        customer: {
            name: string;
            email: any;
            phone: any;
        };
        vendor: {
            name: any;
            phone: any;
            email: any;
        };
        listing: {
            name: any;
            type: string;
            location: string;
        };
        bookingDetails: {
            checkIn: any;
            checkOut: any;
            guests: number;
            duration: any;
            room: any;
        };
        amount: {
            subtotal: number;
            taxes: number;
            total: number;
        };
        status: import(".prisma/client").$Enums.BookingStatus;
        payments: any;
    }>;
    private checkHotelAvailability;
    private checkGuideAvailability;
    private checkPackageAvailability;
    private calculateBookingAmount;
    private calculateNights;
    private generateBookingReference;
    private updateInventoryAfterBooking;
    private restoreInventoryAfterCancellation;
}
