"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RazorpayService = void 0;
const common_1 = require("@nestjs/common");
const razorpay_1 = require("razorpay");
const crypto_1 = require("crypto");
let RazorpayService = class RazorpayService {
    constructor() {
        this.razorpay = new razorpay_1.default({
            key_id: process.env.RAZORPAY_KEY_ID,
            key_secret: process.env.RAZORPAY_KEY_SECRET,
        });
    }
    async createOrder(params) {
        return this.razorpay.orders.create(params);
    }
    async getOrder(orderId) {
        return this.razorpay.orders.fetch(orderId);
    }
    async createRefund(params) {
        return this.razorpay.payments.refund(params.paymentId, {
            amount: params.amount,
            notes: params.notes,
        });
    }
    async verifyWebhookSignature(payload, signature) {
        const expectedSignature = crypto_1.default
            .createHmac("sha256", process.env.RAZORPAY_WEBHOOK_SECRET)
            .update(payload)
            .digest("hex");
        return crypto_1.default.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
    }
    async getPayment(paymentId) {
        return this.razorpay.payments.fetch(paymentId);
    }
};
exports.RazorpayService = RazorpayService;
exports.RazorpayService = RazorpayService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], RazorpayService);
//# sourceMappingURL=razorpay.service.js.map