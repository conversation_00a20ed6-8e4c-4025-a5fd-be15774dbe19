import type { SearchService } from "./search.service";
import type { SearchQueryDto, LocationSearchDto, SuggestionsQueryDto } from "./dto/search.dto";
import { ApiResponseDto } from "../common/dto/api-response.dto";
export declare class SearchController {
    private readonly searchService;
    constructor(searchService: SearchService);
    search(query: SearchQueryDto): Promise<ApiResponseDto<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>>;
    searchHotels(query: SearchQueryDto): Promise<ApiResponseDto<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>>;
    searchGuides(query: SearchQueryDto): Promise<ApiResponseDto<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>>;
    searchPackages(query: SearchQueryDto): Promise<ApiResponseDto<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>>;
    getSuggestions(query: SuggestionsQueryDto): Promise<ApiResponseDto<any>>;
    searchLocations(query: LocationSearchDto): Promise<ApiResponseDto<unknown>>;
    getFilters(query: {
        type?: string;
        city?: string;
    }): Promise<ApiResponseDto<any>>;
    getPopular(): Promise<ApiResponseDto<any>>;
}
