import type { SearchService } from "./search.service";
import type { SearchQueryDto, LocationSearchDto, SuggestionsQueryDto } from "./dto/search.dto";
export declare class SearchController {
    private readonly searchService;
    constructor(searchService: SearchService);
    search(query: SearchQueryDto): Promise<any>;
    searchHotels(query: SearchQueryDto): Promise<any>;
    searchGuides(query: SearchQueryDto): Promise<any>;
    searchPackages(query: SearchQueryDto): Promise<any>;
    getSuggestions(query: SuggestionsQueryDto): Promise<any>;
    searchLocations(query: LocationSearchDto): Promise<any>;
    getFilters(query: {
        type?: string;
        city?: string;
    }): Promise<any>;
    getPopular(): Promise<any>;
}
