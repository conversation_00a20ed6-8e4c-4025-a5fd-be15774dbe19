import { Injectable } from "@nestjs/common"
import Stripe from "stripe"

@Injectable()
export class StripeService {
  private stripe: Stripe

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: "2025-07-30.basil",
    })
  }

  async createPaymentIntent(params: Stripe.PaymentIntentCreateParams) {
    return this.stripe.paymentIntents.create(params)
  }

  async confirmPaymentIntent(paymentIntentId: string, paymentMethodId?: string) {
    const params: Stripe.PaymentIntentConfirmParams = {}
    if (paymentMethodId) {
      params.payment_method = paymentMethodId
    }
    return this.stripe.paymentIntents.confirm(paymentIntentId, params)
  }

  async createCustomer(params: Stripe.CustomerCreateParams) {
    return this.stripe.customers.create(params)
  }

  async getCustomerPaymentMethods(customerId: string) {
    const paymentMethods = await this.stripe.paymentMethods.list({
      customer: customerId,
      type: "card",
    })
    return paymentMethods.data
  }

  async attachPaymentMethodToCustomer(paymentMethodId: string, customerId: string) {
    return this.stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    })
  }

  async setDefaultPaymentMethod(customerId: string, paymentMethodId: string) {
    return this.stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    })
  }

  async createRefund(params: Stripe.RefundCreateParams) {
    return this.stripe.refunds.create(params)
  }

  async constructWebhookEvent(payload: string | Buffer, signature: string) {
    return this.stripe.webhooks.constructEvent(payload, signature, process.env.STRIPE_WEBHOOK_SECRET)
  }

  async retrievePaymentIntent(paymentIntentId: string) {
    return this.stripe.paymentIntents.retrieve(paymentIntentId)
  }
}
