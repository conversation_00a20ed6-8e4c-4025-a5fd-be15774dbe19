import { Injectable, type NestInterceptor, type ExecutionContext, type <PERSON><PERSON><PERSON><PERSON>, <PERSON>gger } from "@nestjs/common"
import type { Observable } from "rxjs"
import { tap } from "rxjs/operators"

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name)

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest()
    const { method, url, body, user } = request
    const now = Date.now()

    this.logger.log(`[${method}] ${url} - User: ${user?.id || "Anonymous"}`)

    return next.handle().pipe(
      tap(() => {
        const response = context.switchToHttp().getResponse()
        const { statusCode } = response
        const responseTime = Date.now() - now

        this.logger.log(`[${method}] ${url} - ${statusCode} - ${responseTime}ms - User: ${user?.id || "Anonymous"}`)
      }),
    )
  }
}
