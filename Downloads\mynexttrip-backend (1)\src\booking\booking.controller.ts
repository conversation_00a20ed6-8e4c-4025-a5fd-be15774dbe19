import { Controller, Get, Post, Put, Delete, Param, UseGuards, HttpStatus, ParseUUIDPipe } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger"
import { JwtAuthGuard } from "../common/guards/jwt-auth.guard"
import { RolesGuard } from "../common/guards/roles.guard"
import { Roles } from "../common/decorators/roles.decorator"
import type { BookingService } from "./booking.service"
import type { CreateBookingDto, UpdateBookingDto, BookingQueryDto, AvailabilityCheckDto } from "./dto/booking.dto"
import { ApiResponseDto } from "../common/dto/api-response.dto"

@ApiTags("Bookings")
@Controller("bookings")
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BookingController {
  constructor(private readonly bookingService: BookingService) {}

  @Post("check-availability")
  @ApiOperation({ summary: "Check availability for booking" })
  @ApiResponse({ status: HttpStatus.OK, description: "Availability checked successfully" })
  async checkAvailability(req, availabilityDto: AvailabilityCheckDto) {
    const availability = await this.bookingService.checkAvailability(availabilityDto)
    return new ApiResponseDto(true, "Availability checked successfully", availability)
  }

  @Post()
  @Roles("user")
  @ApiOperation({ summary: "Create new booking" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Booking created successfully" })
  async createBooking(req, createBookingDto: CreateBookingDto) {
    const booking = await this.bookingService.createBooking(req.user.id, createBookingDto)
    return new ApiResponseDto(true, "Booking created successfully", booking)
  }

  @Get()
  @Roles("user")
  @ApiOperation({ summary: "Get user bookings" })
  async getUserBookings(req, query: BookingQueryDto) {
    const bookings = await this.bookingService.getUserBookings(req.user.id, query)
    return new ApiResponseDto(true, "Bookings retrieved successfully", bookings)
  }

  @Get(":id")
  @Roles("user", "vendor")
  @ApiOperation({ summary: "Get booking by ID" })
  async getBooking(req, @Param('id', ParseUUIDPipe) id: string) {
    const booking = await this.bookingService.getBookingById(req.user.id, id, req.user.role)
    return new ApiResponseDto(true, "Booking retrieved successfully", booking)
  }

  @Put(":id")
  @Roles("user")
  @ApiOperation({ summary: "Update booking" })
  async updateBooking(req, @Param('id', ParseUUIDPipe) id: string, updateBookingDto: UpdateBookingDto) {
    const booking = await this.bookingService.updateBooking(req.user.id, id, updateBookingDto)
    return new ApiResponseDto(true, "Booking updated successfully", booking)
  }

  @Delete(":id")
  @Roles("user")
  @ApiOperation({ summary: "Cancel booking" })
  async cancelBooking(req, @Param('id', ParseUUIDPipe) id: string) {
    const booking = await this.bookingService.cancelBooking(req.user.id, id)
    return new ApiResponseDto(true, "Booking cancelled successfully", booking)
  }

  @Put(":id/confirm")
  @Roles("vendor")
  @ApiOperation({ summary: "Confirm booking (vendor only)" })
  async confirmBooking(req, @Param('id', ParseUUIDPipe) id: string) {
    const booking = await this.bookingService.confirmBooking(req.user.id, id)
    return new ApiResponseDto(true, "Booking confirmed successfully", booking)
  }

  @Put(":id/reject")
  @Roles("vendor")
  @ApiOperation({ summary: "Reject booking (vendor only)" })
  async rejectBooking(req, @Param('id', ParseUUIDPipe) id: string, body: { reason?: string }) {
    const booking = await this.bookingService.rejectBooking(req.user.id, id, body.reason)
    return new ApiResponseDto(true, "Booking rejected successfully", booking)
  }

  @Get(":id/invoice")
  @Roles("user", "vendor")
  @ApiOperation({ summary: "Get booking invoice" })
  async getInvoice(req, @Param('id', ParseUUIDPipe) id: string) {
    const invoice = await this.bookingService.generateInvoice(req.user.id, id, req.user.role)
    return new ApiResponseDto(true, "Invoice generated successfully", invoice)
  }
}
