{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@auth/core": "latest", "@babel/runtime": "latest", "@emotion/is-prop-valid": "latest", "@fastify/static": "latest", "@hookform/resolvers": "latest", "@jest/globals": "latest", "@nestjs/common": "latest", "@nestjs/config": "latest", "@nestjs/core": "latest", "@nestjs/jwt": "latest", "@nestjs/microservices": "latest", "@nestjs/passport": "latest", "@nestjs/platform-express": "latest", "@nestjs/swagger": "latest", "@nestjs/testing": "latest", "@nestjs/throttler": "latest", "@nestjs/websockets": "latest", "@prisma/client": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@stripe/react-stripe-js": "latest", "@stripe/stripe-js": "latest", "@tanstack/react-query": "latest", "@tanstack/react-query-devtools": "latest", "@testing-library/dom": "latest", "@testing-library/jest-dom": "latest", "@testing-library/react": "latest", "autoprefixer": "^10.4.20", "axios": "latest", "bcryptjs": "latest", "class-transformer": "latest", "class-validator": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "compression": "latest", "crypto": "latest", "cypress": "latest", "date-fns": "latest", "embla-carousel-react": "8.5.1", "express": "latest", "framer-motion": "latest", "geist": "^1.3.1", "helmet": "latest", "immer": "latest", "input-otp": "1.4.1", "jest": "latest", "lucide-react": "^0.454.0", "next": "14.2.16", "next-auth": "latest", "next-themes": "^0.4.4", "node-notifier": "latest", "nodemailer": "latest", "passport": "latest", "prisma": "latest", "razorpay": "latest", "react": "^18", "react-day-picker": "9.8.0", "react-dom": "^18", "react-hook-form": "latest", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redis": "latest", "reflect-metadata": "latest", "rxjs": "latest", "sonner": "latest", "stripe": "latest", "supertest": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "typesense": "latest", "use-sync-external-store": "latest", "util": "latest", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}