"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminModule = void 0;
const common_1 = require("@nestjs/common");
const admin_controller_1 = require("./admin.controller");
const admin_service_1 = require("./admin.service");
const vendor_approval_controller_1 = require("./vendor-approval.controller");
const vendor_approval_service_1 = require("./vendor-approval.service");
const listing_moderation_controller_1 = require("./listing-moderation.controller");
const listing_moderation_service_1 = require("./listing-moderation.service");
const analytics_controller_1 = require("./analytics.controller");
const analytics_service_1 = require("./analytics.service");
const prisma_module_1 = require("../common/modules/prisma.module");
const redis_module_1 = require("../common/modules/redis.module");
const typesense_module_1 = require("../common/modules/typesense.module");
let AdminModule = class AdminModule {
};
exports.AdminModule = AdminModule;
exports.AdminModule = AdminModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, redis_module_1.RedisModule, typesense_module_1.TypesenseModule],
        controllers: [admin_controller_1.AdminController, vendor_approval_controller_1.VendorApprovalController, listing_moderation_controller_1.ListingModerationController, analytics_controller_1.AnalyticsController],
        providers: [admin_service_1.AdminService, vendor_approval_service_1.VendorApprovalService, listing_moderation_service_1.ListingModerationService, analytics_service_1.AnalyticsService],
        exports: [admin_service_1.AdminService],
    })
], AdminModule);
//# sourceMappingURL=admin.module.js.map