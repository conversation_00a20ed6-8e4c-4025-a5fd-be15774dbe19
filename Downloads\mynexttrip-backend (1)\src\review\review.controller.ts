import { Controller, Get, Post, Put, Delete, Param, UseGuards, HttpStatus } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger"
import { JwtAuthGuard } from "../common/guards/jwt-auth.guard"
import { RolesGuard } from "../common/guards/roles.guard"
import { Roles } from "../common/decorators/roles.decorator"
import type { ReviewService } from "./review.service"
import type { CreateReviewDto, UpdateReviewDto, ReviewQueryDto, ReviewModerationDto } from "./dto/review.dto"
import { ApiResponseDto } from "../common/dto/api-response.dto"

@ApiTags("Reviews")
@Controller("reviews")
export class ReviewController {
  constructor(private readonly reviewService: ReviewService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("user")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create review" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Review created successfully" })
  async createReview(req, createReviewDto: CreateReviewDto) {
    const review = await this.reviewService.createReview(req.user.id, createReviewDto)
    return new ApiResponseDto(true, "Review created successfully", review)
  }

  @Get("listing/:listingId")
  @ApiOperation({ summary: "Get reviews for a listing" })
  async getListingReviews(@Param('listingId') listingId: string, query: ReviewQueryDto) {
    const reviews = await this.reviewService.getListingReviews(listingId, query)
    return new ApiResponseDto(true, "Reviews retrieved successfully", reviews)
  }

  @Get("user/:userId")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get user reviews" })
  async getUserReviews(req, @Param('userId') userId: string, query: ReviewQueryDto) {
    // Users can only see their own reviews unless admin
    const targetUserId = req.user.role === "admin" ? userId : req.user.id
    const reviews = await this.reviewService.getUserReviews(targetUserId, query)
    return new ApiResponseDto(true, "Reviews retrieved successfully", reviews)
  }

  @Get(":id")
  @ApiOperation({ summary: "Get review by ID" })
  async getReview(@Param('id') id: string) {
    const review = await this.reviewService.getReviewById(id)
    return new ApiResponseDto(true, "Review retrieved successfully", review)
  }

  @Put(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("user")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update review" })
  async updateReview(req, @Param('id') id: string, updateReviewDto: UpdateReviewDto) {
    const review = await this.reviewService.updateReview(req.user.id, id, updateReviewDto)
    return new ApiResponseDto(true, "Review updated successfully", review)
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("user", "admin")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete review" })
  async deleteReview(req, @Param('id') id: string) {
    await this.reviewService.deleteReview(req.user.id, id, req.user.role)
    return new ApiResponseDto(true, "Review deleted successfully")
  }

  @Post(":id/helpful")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("user")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Mark review as helpful" })
  async markHelpful(req, @Param('id') id: string) {
    const result = await this.reviewService.markReviewHelpful(req.user.id, id)
    return new ApiResponseDto(true, "Review marked as helpful", result)
  }

  @Post(":id/report")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("user")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Report inappropriate review" })
  async reportReview(req, @Param('id') id: string, body: { reason: string }) {
    const result = await this.reviewService.reportReview(req.user.id, id, body.reason)
    return new ApiResponseDto(true, "Review reported successfully", result)
  }

  // Admin endpoints
  @Get("admin/pending")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("admin")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get pending reviews for moderation" })
  async getPendingReviews(query: ReviewQueryDto) {
    const reviews = await this.reviewService.getPendingReviews(query)
    return new ApiResponseDto(true, "Pending reviews retrieved successfully", reviews)
  }

  @Put("admin/:id/moderate")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("admin")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Moderate review" })
  async moderateReview(@Param('id') id: string, moderationDto: ReviewModerationDto) {
    const review = await this.reviewService.moderateReview(id, moderationDto)
    return new ApiResponseDto(true, "Review moderated successfully", review)
  }

  @Get("admin/reports")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles("admin")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get reported reviews" })
  async getReportedReviews(query: ReviewQueryDto) {
    const reports = await this.reviewService.getReportedReviews(query)
    return new ApiResponseDto(true, "Reported reviews retrieved successfully", reports)
  }
}
