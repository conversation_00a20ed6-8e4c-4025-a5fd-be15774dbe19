"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
let PackageController = class PackageController {
    constructor(packageService) {
        this.packageService = packageService;
    }
    async createPackage(req, createPackageDto) {
        const packageData = await this.packageService.createPackage(req.user.id, createPackageDto);
        return new api_response_dto_1.ApiResponseDto(true, "Package created successfully", packageData);
    }
    async getPackages(req, query) {
        const packages = await this.packageService.getVendorPackages(req.user.id, query);
        return new api_response_dto_1.ApiResponseDto(true, "Packages retrieved successfully", packages);
    }
    async getPackage(req, id) {
        const packageData = await this.packageService.getPackageById(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Package retrieved successfully", packageData);
    }
    async updatePackage(req, id, updatePackageDto) {
        const packageData = await this.packageService.updatePackage(req.user.id, id, updatePackageDto);
        return new api_response_dto_1.ApiResponseDto(true, "Package updated successfully", packageData);
    }
    async deletePackage(req, id) {
        await this.packageService.deletePackage(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Package deleted successfully");
    }
    async uploadImages(req, id, files) {
        const images = await this.packageService.uploadPackageImages(req.user.id, id, files);
        return new api_response_dto_1.ApiResponseDto(true, "Images uploaded successfully", images);
    }
    async updateStatus(req, id, body) {
        const packageData = await this.packageService.updatePackageStatus(req.user.id, id, body.status);
        return new api_response_dto_1.ApiResponseDto(true, "Package status updated successfully", packageData);
    }
};
exports.PackageController = PackageController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: "Create new package listing" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: "Package created successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PackageController.prototype, "createPackage", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor packages" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PackageController.prototype, "getPackages", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Get package by ID" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PackageController.prototype, "getPackage", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Update package" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], PackageController.prototype, "updatePackage", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Delete package" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PackageController.prototype, "deletePackage", null);
__decorate([
    (0, common_1.Post)(":id/images"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", 10)),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, swagger_1.ApiOperation)({ summary: "Upload package images" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Array]),
    __metadata("design:returntype", Promise)
], PackageController.prototype, "uploadImages", null);
__decorate([
    (0, common_1.Put)(":id/status"),
    (0, swagger_1.ApiOperation)({ summary: "Update package status" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], PackageController.prototype, "updateStatus", null);
exports.PackageController = PackageController = __decorate([
    (0, swagger_1.ApiTags)("Package Management"),
    (0, common_1.Controller)("vendor/packages"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [Function])
], PackageController);
//# sourceMappingURL=package.controller.js.map