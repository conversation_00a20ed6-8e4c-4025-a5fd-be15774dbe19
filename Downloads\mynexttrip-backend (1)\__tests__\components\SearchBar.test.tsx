import type React from "react"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import SearchBar from "@/components/ui/search-bar"
import jest from "jest" // Import jest to fix the undeclared variable error

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>)
}

describe("SearchBar", () => {
  it("renders search form elements", () => {
    renderWithProviders(<SearchBar />)

    expect(screen.getByPlaceholderText(/where do you want to go/i)).toBeInTheDocument()
    expect(screen.getByText(/check-in/i)).toBeInTheDocument()
    expect(screen.getByText(/check-out/i)).toBeInTheDocument()
    expect(screen.getByText(/guests/i)).toBeInTheDocument()
    expect(screen.getByRole("button", { name: /search/i })).toBeInTheDocument()
  })

  it("handles form submission", async () => {
    const mockOnSearch = jest.fn()
    renderWithProviders(<SearchBar onSearch={mockOnSearch} />)

    const destinationInput = screen.getByPlaceholderText(/where do you want to go/i)
    const searchButton = screen.getByRole("button", { name: /search/i })

    fireEvent.change(destinationInput, { target: { value: "Delhi" } })
    fireEvent.click(searchButton)

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith(
        expect.objectContaining({
          destination: "Delhi",
        }),
      )
    })
  })

  it("validates required fields", async () => {
    renderWithProviders(<SearchBar />)

    const searchButton = screen.getByRole("button", { name: /search/i })
    fireEvent.click(searchButton)

    await waitFor(() => {
      expect(screen.getByText(/destination is required/i)).toBeInTheDocument()
    })
  })
})
