import { <PERSON><PERSON><PERSON> } from "@nestjs/common"
import { SearchController } from "./search.controller"
import { SearchService } from "./search.service"
import { PrismaModule } from "../common/modules/prisma.module"
import { TypesenseModule } from "../common/modules/typesense.module"
import { RedisModule } from "../common/modules/redis.module"

@Module({
  imports: [PrismaModule, TypesenseModule, RedisModule],
  controllers: [SearchController],
  providers: [SearchService],
  exports: [SearchService],
})
export class SearchModule {}
