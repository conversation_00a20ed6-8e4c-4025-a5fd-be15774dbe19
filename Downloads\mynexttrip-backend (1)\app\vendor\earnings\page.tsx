"use client"

import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { DollarSign, TrendingUp, Calendar, Download, CreditCard, Clock, CheckCircle } from "lucide-react"
import { api } from "@/lib/api"
import { MainLayout } from "@/components/layout/main-layout"
import { format } from "date-fns"

interface EarningsData {
  totalEarnings: number
  pendingPayouts: number
  completedPayouts: number
  thisMonthEarnings: number
  lastMonthEarnings: number
  growthPercentage: number
  nextPayoutDate: string
  nextPayoutAmount: number
}

interface Transaction {
  id: string
  type: "booking" | "payout" | "refund"
  amount: number
  commission: number
  netAmount: number
  description: string
  status: "pending" | "completed" | "failed"
  date: string
  bookingId?: string
}

export default function VendorEarnings() {
  const { data: earnings, isLoading: earningsLoading } = useQuery({
    queryKey: ["vendor-earnings"],
    queryFn: async () => {
      const response = await api.get("/vendor/earnings")
      return response.data as EarningsData
    },
  })

  const { data: transactions, isLoading: transactionsLoading } = useQuery({
    queryKey: ["vendor-transactions"],
    queryFn: async () => {
      const response = await api.get("/vendor/transactions")
      return response.data as Transaction[]
    },
  })

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "booking":
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case "payout":
        return <CreditCard className="h-4 w-4 text-blue-600" />
      case "refund":
        return <Clock className="h-4 w-4 text-red-600" />
      default:
        return <DollarSign className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "failed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Earnings & Payouts</h1>
            <p className="text-muted-foreground">Track your revenue and manage payouts</p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Download Report
          </Button>
        </div>

        {/* Earnings Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{earnings?.totalEarnings?.toLocaleString() || 0}</div>
              <p className="text-xs text-muted-foreground">All time revenue</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{earnings?.thisMonthEarnings?.toLocaleString() || 0}</div>
              <p className="text-xs text-muted-foreground">
                {earnings?.growthPercentage && earnings.growthPercentage > 0 ? "+" : ""}
                {earnings?.growthPercentage?.toFixed(1) || 0}% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payouts</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{earnings?.pendingPayouts?.toLocaleString() || 0}</div>
              <p className="text-xs text-muted-foreground">Awaiting transfer</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Next Payout</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{earnings?.nextPayoutAmount?.toLocaleString() || 0}</div>
              <p className="text-xs text-muted-foreground">
                {earnings?.nextPayoutDate ? format(new Date(earnings.nextPayoutDate), "MMM dd, yyyy") : "TBD"}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="transactions" className="space-y-6">
          <TabsList>
            <TabsTrigger value="transactions">Transaction History</TabsTrigger>
            <TabsTrigger value="payouts">Payout Schedule</TabsTrigger>
            <TabsTrigger value="analytics">Revenue Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="transactions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transactions?.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getTransactionIcon(transaction.type)}
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-muted-foreground">
                            {format(new Date(transaction.date), "MMM dd, yyyy 'at' HH:mm")}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2">
                          <div>
                            <p className="font-semibold">
                              {transaction.type === "refund" ? "-" : "+"}₹{transaction.netAmount.toLocaleString()}
                            </p>
                            {transaction.commission > 0 && (
                              <p className="text-xs text-muted-foreground">
                                Commission: ₹{transaction.commission.toLocaleString()}
                              </p>
                            )}
                          </div>
                          <Badge className={getStatusColor(transaction.status)}>{transaction.status}</Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payouts" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payout Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-blue-600" />
                    <h3 className="font-semibold text-blue-900">Automatic Payouts Enabled</h3>
                  </div>
                  <p className="text-sm text-blue-700">
                    Payouts are processed automatically every 7 days after booking completion. Your next payout of ₹
                    {earnings?.nextPayoutAmount?.toLocaleString() || 0} is scheduled for{" "}
                    {earnings?.nextPayoutDate ? format(new Date(earnings.nextPayoutDate), "MMMM dd, yyyy") : "TBD"}.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium">Payout Schedule</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Frequency:</span>
                        <span>Weekly (Every 7 days)</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Processing Time:</span>
                        <span>2-3 business days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Minimum Payout:</span>
                        <span>₹1,000</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">Commission Structure</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Hotels:</span>
                        <span>12% commission</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Guides:</span>
                        <span>8% commission</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Packages:</span>
                        <span>10% commission</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-muted-foreground">
                    Chart placeholder - Monthly revenue trends
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Revenue by Listing Type</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-muted-foreground">
                    Chart placeholder - Revenue breakdown by type
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
