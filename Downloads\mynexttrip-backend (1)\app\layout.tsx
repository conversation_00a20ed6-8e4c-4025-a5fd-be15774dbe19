import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { QueryProvider } from "@/components/providers/query-provider"
import { Toaster } from "@/components/ui/sonner"

const geist = Geist({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-geist",
})

const manrope = Manrope({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-manrope",
})

export const metadata: Metadata = {
  title: "MyNextTrip - Luxury Travel & Experiences in Northern India",
  description:
    "Discover premium hotels, authentic experiences, and expert guides in Northern India. Book luxury accommodations and unforgettable adventures.",
  keywords: "luxury travel, Northern India, hotels, experiences, tour guides, travel packages, premium accommodation",
  authors: [{ name: "MyNextTrip" }],
  creator: "MyNextTrip",
  publisher: "MyNextTrip",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://mynexttrip.com",
    title: "MyNextTrip - Luxury Travel & Experiences in Northern India",
    description: "Discover premium hotels, authentic experiences, and expert guides in Northern India.",
    siteName: "MyNextTrip",
  },
  twitter: {
    card: "summary_large_image",
    title: "MyNextTrip - Luxury Travel & Experiences in Northern India",
    description: "Discover premium hotels, authentic experiences, and expert guides in Northern India.",
  },
  robots: {
    index: true,
    follow: true,
  },
    generator: 'v0.app'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${geist.variable} ${manrope.variable}`} suppressHydrationWarning>
      <body className="font-sans antialiased">
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <QueryProvider>
            {children}
            <Toaster />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
