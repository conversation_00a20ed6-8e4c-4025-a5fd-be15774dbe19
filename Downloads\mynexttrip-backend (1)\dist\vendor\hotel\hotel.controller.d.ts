import type { HotelService } from "./hotel.service";
import type { CreateHotelDto, UpdateHotelDto, HotelQueryDto } from "./dto/hotel.dto";
import type { Express } from "express";
export declare class HotelController {
    private readonly hotelService;
    constructor(hotelService: HotelService);
    createHotel(req: any, createHotelDto: CreateHotelDto): Promise<any>;
    getHotels(req: any, query: HotelQueryDto): Promise<any>;
    getHotel(req: any, id: string): Promise<any>;
    updateHotel(req: any, id: string, updateHotelDto: UpdateHotelDto): Promise<any>;
    deleteHotel(req: any, id: string): Promise<any>;
    uploadImages(req: any, id: string, files: Express.Multer.File[]): Promise<any>;
    deleteImage(req: any, id: string, imageId: string): Promise<any>;
    updateStatus(req: any, id: string, body: {
        status: string;
    }): Promise<any>;
}
