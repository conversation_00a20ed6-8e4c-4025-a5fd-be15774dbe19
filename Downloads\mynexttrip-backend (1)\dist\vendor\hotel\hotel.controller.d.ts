import type { HotelService } from "./hotel.service";
import type { CreateHotelDto, UpdateHotelDto, HotelQueryDto } from "./dto/hotel.dto";
import { ApiResponseDto } from "../../common/dto/api-response.dto";
import type { Express } from "express";
export declare class HotelController {
    private readonly hotelService;
    constructor(hotelService: HotelService);
    createHotel(req: any, createHotelDto: CreateHotelDto): Promise<ApiResponseDto<any>>;
    getHotels(req: any, query: HotelQueryDto): Promise<ApiResponseDto<{
        hotels: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>>;
    getHotel(req: any, id: string): Promise<ApiResponseDto<any>>;
    updateHotel(req: any, id: string, updateHotelDto: UpdateHotelDto): Promise<ApiResponseDto<any>>;
    deleteHotel(req: any, id: string): Promise<ApiResponseDto<any>>;
    uploadImages(req: any, id: string, files: Express.Multer.File[]): Promise<ApiResponseDto<any>>;
    deleteImage(req: any, id: string, imageId: string): Promise<ApiResponseDto<any>>;
    updateStatus(req: any, id: string, body: {
        status: string;
    }): Promise<ApiResponseDto<any>>;
}
