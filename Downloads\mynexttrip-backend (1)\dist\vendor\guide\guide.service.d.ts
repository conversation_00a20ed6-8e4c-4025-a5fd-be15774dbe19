import type { PrismaService } from "../../common/services/prisma.service";
import type { TypesenseService } from "../../common/services/typesense.service";
import type { CreateGuideDto, UpdateGuideDto, GuideQueryDto } from "./dto/guide.dto";
import type { Express } from "express";
export declare class GuideService {
    private prisma;
    private typesense;
    constructor(prisma: PrismaService, typesense: TypesenseService);
    createGuide(userId: string, createGuideDto: CreateGuideDto): Promise<any>;
    getVendorGuides(userId: string, query: GuideQueryDto): Promise<{
        guides: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getGuideById(userId: string, guideId: string): Promise<any>;
    updateGuide(userId: string, guideId: string, updateGuideDto: UpdateGuideDto): Promise<any>;
    deleteGuide(userId: string, guideId: string): Promise<void>;
    uploadGuideImages(userId: string, guideId: string, files: Express.Multer.File[]): Promise<any>;
    updateGuideStatus(userId: string, guideId: string, status: string): Promise<any>;
    getGuideAvailability(userId: string, guideId: string, startDate: string, endDate: string): Promise<any>;
    updateGuideAvailability(userId: string, guideId: string, dates: string[], available: boolean): Promise<any>;
    private indexGuideInTypesense;
}
