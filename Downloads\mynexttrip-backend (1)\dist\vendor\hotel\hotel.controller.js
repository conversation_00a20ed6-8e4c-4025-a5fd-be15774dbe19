"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotelController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
let HotelController = class HotelController {
    constructor(hotelService) {
        this.hotelService = hotelService;
    }
    async createHotel(req, createHotelDto) {
        const hotel = await this.hotelService.createHotel(req.user.id, createHotelDto);
        return new api_response_dto_1.ApiResponseDto(true, "Hotel created successfully", hotel);
    }
    async getHotels(req, query) {
        const hotels = await this.hotelService.getVendorHotels(req.user.id, query);
        return new api_response_dto_1.ApiResponseDto(true, "Hotels retrieved successfully", hotels);
    }
    async getHotel(req, id) {
        const hotel = await this.hotelService.getHotelById(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Hotel retrieved successfully", hotel);
    }
    async updateHotel(req, id, updateHotelDto) {
        const hotel = await this.hotelService.updateHotel(req.user.id, id, updateHotelDto);
        return new api_response_dto_1.ApiResponseDto(true, "Hotel updated successfully", hotel);
    }
    async deleteHotel(req, id) {
        await this.hotelService.deleteHotel(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Hotel deleted successfully");
    }
    async uploadImages(req, id, files) {
        const images = await this.hotelService.uploadHotelImages(req.user.id, id, files);
        return new api_response_dto_1.ApiResponseDto(true, "Images uploaded successfully", images);
    }
    async deleteImage(req, id, imageId) {
        await this.hotelService.deleteHotelImage(req.user.id, id, imageId);
        return new api_response_dto_1.ApiResponseDto(true, "Image deleted successfully");
    }
    async updateStatus(req, id, body) {
        const hotel = await this.hotelService.updateHotelStatus(req.user.id, id, body.status);
        return new api_response_dto_1.ApiResponseDto(true, "Hotel status updated successfully", hotel);
    }
};
exports.HotelController = HotelController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: "Create new hotel listing" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: "Hotel created successfully" }),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "createHotel", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor hotels" }),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "getHotels", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Get hotel by ID" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "getHotel", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Update hotel" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Function]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "updateHotel", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Delete hotel" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "deleteHotel", null);
__decorate([
    (0, common_1.Post)(":id/images"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", 10)),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, swagger_1.ApiOperation)({ summary: "Upload hotel images" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Array]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "uploadImages", null);
__decorate([
    (0, common_1.Delete)(":id/images/:imageId"),
    (0, swagger_1.ApiOperation)({ summary: "Delete hotel image" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Param)('imageId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "deleteImage", null);
__decorate([
    (0, common_1.Put)(":id/status"),
    (0, swagger_1.ApiOperation)({ summary: "Update hotel status" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "updateStatus", null);
exports.HotelController = HotelController = __decorate([
    (0, swagger_1.ApiTags)("Hotel Management"),
    (0, common_1.Controller)("vendor/hotels"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [Function])
], HotelController);
//# sourceMappingURL=hotel.controller.js.map