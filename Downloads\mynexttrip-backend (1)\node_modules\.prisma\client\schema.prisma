// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  CUSTOMER
  VENDOR
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum VendorType {
  HOTEL
  TOUR_GUIDE
  PACKAGE_PROVIDER
  EXPERIENCE_PROVIDER
}

enum VendorStatus {
  PENDING
  APPROVED
  REJECTED
  SUSPENDED
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
}

enum PaymentMethod {
  STRIPE
  RAZORPAY
  WALLET
}

enum ListingType {
  HOTEL
  VACATION_RENTAL
  TOUR_PACKAGE
  EXPERIENCE
  GUIDE_SERVICE
}

enum ListingStatus {
  DRAFT
  ACTIVE
  INACTIVE
  SUSPENDED
}

model User {
  id                String     @id @default(cuid())
  email             String     @unique
  phone             String?    @unique
  password          String?
  firstName         String
  lastName          String
  avatar            String?
  role              UserRole   @default(CUSTOMER)
  status            UserStatus @default(PENDING_VERIFICATION)
  emailVerified     Boolean    @default(false)
  phoneVerified     Boolean    @default(false)
  googleId          String?    @unique
  preferredLanguage String     @default("en")
  preferredCurrency String     @default("INR")

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastLoginAt DateTime?

  // Relations
  vendor         Vendor?
  bookings       Booking[]
  reviews        Review[]
  paymentMethods PaymentMethod[]
  notifications  Notification[]

  @@map("users")
}

model Vendor {
  id           String       @id @default(cuid())
  userId       String       @unique
  businessName String
  businessType VendorType
  description  String?
  logo         String?
  status       VendorStatus @default(PENDING)

  // Business Details
  gstNumber       String?
  panNumber       String?
  businessAddress Json // {street, city, state, country, pincode}
  bankDetails     Json // {accountNumber, ifscCode, bankName, accountHolderName}

  // Verification
  documentsUploaded Boolean   @default(false)
  verificationNotes String?
  verifiedAt        DateTime?

  // Commission & Pricing
  commissionRate Float @default(10.0) // Percentage

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  listings Listing[]
  payouts  Payout[]

  @@map("vendors")
}

model Listing {
  id       String        @id @default(cuid())
  vendorId String
  type     ListingType
  status   ListingStatus @default(DRAFT)

  // Basic Info
  title       String
  description String
  images      String[] // Array of image URLs

  // Location
  location Json // {address, city, state, country, coordinates: {lat, lng}}

  // Pricing
  basePrice Float
  currency  String @default("INR")

  // Availability & Capacity
  maxCapacity Int

  // Features & Amenities (JSON for flexibility)
  amenities Json? // Different for hotels vs experiences
  policies  Json? // Cancellation, check-in/out, etc.

  // SEO & Search
  slug String   @unique
  tags String[]

  // Ratings
  averageRating Float @default(0)
  totalReviews  Int   @default(0)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  vendor            Vendor             @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  bookings          Booking[]
  reviews           Review[]
  availabilitySlots AvailabilitySlot[]

  @@map("listings")
}

model AvailabilitySlot {
  id        String   @id @default(cuid())
  listingId String
  date      DateTime @db.Date

  // Capacity Management
  totalCapacity     Int
  availableCapacity Int

  // Dynamic Pricing
  price    Float
  currency String @default("INR")

  // Special Conditions
  isBlocked   Boolean @default(false)
  blockReason String?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  listing  Listing   @relation(fields: [listingId], references: [id], onDelete: Cascade)
  bookings Booking[]

  @@unique([listingId, date])
  @@map("availability_slots")
}

model Booking {
  id                 String  @id @default(cuid())
  userId             String
  listingId          String
  availabilitySlotId String?

  // Booking Details
  checkIn     DateTime
  checkOut    DateTime?
  guests      Int
  totalAmount Float
  currency    String    @default("INR")

  // Status Management
  status BookingStatus @default(PENDING)

  // Customer Info
  guestDetails    Json // {names, ages, contact info}
  specialRequests String?

  // Payment
  paymentIntentId String?
  paymentStatus   PaymentStatus @default(PENDING)

  // Confirmation
  confirmationCode String @unique

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  confirmedAt DateTime?
  cancelledAt DateTime?

  // Relations
  user             User              @relation(fields: [userId], references: [id])
  listing          Listing           @relation(fields: [listingId], references: [id])
  availabilitySlot AvailabilitySlot? @relation(fields: [availabilitySlotId], references: [id])
  payments         Payment[]
  reviews          Review[]

  @@map("bookings")
}

model Payment {
  id        String        @id @default(cuid())
  bookingId String
  amount    Float
  currency  String        @default("INR")
  method    PaymentMethod
  status    PaymentStatus @default(PENDING)

  // Gateway Details
  gatewayId       String? // Stripe/Razorpay payment ID
  gatewayResponse Json? // Full gateway response

  // Refund Info
  refundAmount Float?
  refundReason String?
  refundedAt   DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  booking Booking @relation(fields: [bookingId], references: [id])

  @@map("payments")
}

model Review {
  id        String @id @default(cuid())
  userId    String
  listingId String
  bookingId String @unique

  // Review Content
  rating  Int // 1-5 stars
  title   String?
  comment String
  images  String[] // Optional review images

  // Moderation
  isApproved      Boolean   @default(false)
  moderatedAt     DateTime?
  moderationNotes String?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id])
  listing Listing @relation(fields: [listingId], references: [id])
  booking Booking @relation(fields: [bookingId], references: [id])

  @@map("reviews")
}

model Payout {
  id       String @id @default(cuid())
  vendorId String
  amount   Float
  currency String @default("INR")

  // Period
  periodStart DateTime
  periodEnd   DateTime

  // Status
  status String @default("PENDING") // PENDING, PROCESSING, COMPLETED, FAILED

  // Gateway Details
  gatewayId       String?
  gatewayResponse Json?

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  processedAt DateTime?

  // Relations
  vendor Vendor @relation(fields: [vendorId], references: [id])

  @@map("payouts")
}

model Notification {
  id      String @id @default(cuid())
  userId  String
  type    String // EMAIL, SMS, PUSH
  title   String
  message String
  data    Json? // Additional data for the notification

  // Status
  isRead Boolean   @default(false)
  sentAt DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("notifications")
}

// System Configuration
model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value Json

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

// Audit Log
model AuditLog {
  id         String  @id @default(cuid())
  userId     String?
  action     String
  resource   String
  resourceId String?
  oldValues  Json?
  newValues  Json?
  ipAddress  String?
  userAgent  String?

  // Timestamps
  createdAt DateTime @default(now())

  @@map("audit_logs")
}
