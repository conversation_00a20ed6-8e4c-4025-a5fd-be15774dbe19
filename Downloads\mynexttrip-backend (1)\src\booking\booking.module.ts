import { Module } from "@nestjs/common"
import { BookingController } from "./booking.controller"
import { BookingService } from "./booking.service"
import { PrismaModule } from "../common/modules/prisma.module"
import { RedisModule } from "../common/modules/redis.module"

@Module({
  imports: [PrismaModule, RedisModule],
  controllers: [BookingController],
  providers: [BookingService],
  exports: [BookingService],
})
export class BookingModule {}
