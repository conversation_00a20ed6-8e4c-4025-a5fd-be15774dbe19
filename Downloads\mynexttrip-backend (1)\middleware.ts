import { withAuth } from "next-auth/middleware"

export default with<PERSON>uth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Protect admin routes
        if (req.nextUrl.pathname.startsWith("/admin")) {
          return token?.role === "admin"
        }

        // Protect vendor routes
        if (req.nextUrl.pathname.startsWith("/vendor")) {
          return token?.role === "vendor" || token?.role === "admin"
        }

        // Protect user routes
        if (req.nextUrl.pathname.startsWith("/profile") || req.nextUrl.pathname.startsWith("/bookings")) {
          return !!token
        }

        return true
      },
    },
  },
)

export const config = {
  matcher: ["/admin/:path*", "/vendor/:path*", "/profile/:path*", "/bookings/:path*"],
}
