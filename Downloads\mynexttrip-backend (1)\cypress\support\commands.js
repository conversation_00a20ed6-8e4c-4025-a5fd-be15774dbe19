// Custom commands for MyNextTrip testing

// Login command
const Cypress = require("cypress")
const cy = Cypress.cy

Cypress.Commands.add("login", (email = "<EMAIL>", password = "password123") => {
  cy.visit("/login")
  cy.get('[data-testid="email-input"]').type(email)
  cy.get('[data-testid="password-input"]').type(password)
  cy.get('[data-testid="login-button"]').click()
  cy.url().should("not.include", "/login")
})

// Search hotels command
Cypress.Commands.add("searchHotels", (destination, checkIn, checkOut, guests = 2) => {
  cy.visit("/")
  cy.get('[data-testid="destination-input"]').type(destination)
  cy.get('[data-testid="checkin-input"]').type(checkIn)
  cy.get('[data-testid="checkout-input"]').type(checkOut)
  cy.get('[data-testid="guests-input"]').clear().type(guests.toString())
  cy.get('[data-testid="search-button"]').click()
  cy.url().should("include", "/hotels")
})

// Mock API responses
Cypress.Commands.add("mockApiResponse", (method, url, response, statusCode = 200) => {
  cy.intercept(method, url, {
    statusCode,
    body: response,
  }).as("apiCall")
})

// Wait for page to load
Cypress.Commands.add("waitForPageLoad", () => {
  cy.get('[data-testid="loading"]').should("not.exist")
  cy.get("body").should("be.visible")
})
