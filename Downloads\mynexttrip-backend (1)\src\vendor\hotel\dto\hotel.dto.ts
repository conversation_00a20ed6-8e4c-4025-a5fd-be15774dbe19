import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import {
  IsString,
  IsOptional,
  IsNumber,
  IsArray,
  IsEnum,
  Length,
  Min,
  Max,
  IsInt,
  ValidateNested,
  IsLatitude,
  IsLongitude,
} from "class-validator"
import { Type } from "class-transformer"
import { HotelStatus, RoomType } from "@prisma/client"

export class CreateRoomDto {
  @ApiProperty({ enum: RoomType, description: "Room type" })
  @IsEnum(RoomType)
  type: RoomType

  @ApiPropertyOptional({ description: "Room description" })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  description?: string

  @ApiProperty({ description: "Maximum occupancy" })
  @IsInt()
  @Min(1)
  @Max(10)
  maxOccupancy: number

  @ApiProperty({ description: "Base price per night" })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  basePrice: number

  @ApiPropertyOptional({ description: "Room amenities", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  amenities?: string[]

  @ApiProperty({ description: "Total number of rooms" })
  @IsInt()
  @Min(1)
  totalRooms: number
}

export class CreateHotelDto {
  @ApiProperty({ description: "Hotel name" })
  @IsString()
  @Length(2, 100)
  name: string

  @ApiPropertyOptional({ description: "Hotel description" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  description?: string

  @ApiProperty({ description: "Hotel address" })
  @IsString()
  @Length(10, 200)
  address: string

  @ApiProperty({ description: "City" })
  @IsString()
  @Length(2, 50)
  city: string

  @ApiProperty({ description: "State" })
  @IsString()
  @Length(2, 50)
  state: string

  @ApiProperty({ description: "PIN code" })
  @IsString()
  @Length(6, 6)
  pincode: string

  @ApiProperty({ description: "Latitude" })
  @IsLatitude()
  latitude: number

  @ApiProperty({ description: "Longitude" })
  @IsLongitude()
  longitude: number

  @ApiProperty({ description: "Star rating", minimum: 1, maximum: 5 })
  @IsInt()
  @Min(1)
  @Max(5)
  starRating: number

  @ApiPropertyOptional({ description: "Hotel amenities", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  amenities?: string[]

  @ApiProperty({ description: "Check-in time (HH:MM format)" })
  @IsString()
  checkInTime: string

  @ApiProperty({ description: "Check-out time (HH:MM format)" })
  @IsString()
  checkOutTime: string

  @ApiPropertyOptional({ description: "Cancellation policy" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  cancellationPolicy?: string

  @ApiPropertyOptional({ description: "Hotel rooms", type: [CreateRoomDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRoomDto)
  rooms?: CreateRoomDto[]
}

export class UpdateHotelDto {
  @ApiPropertyOptional({ description: "Hotel name" })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  name?: string

  @ApiPropertyOptional({ description: "Hotel description" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  description?: string

  @ApiPropertyOptional({ description: "Hotel address" })
  @IsOptional()
  @IsString()
  @Length(10, 200)
  address?: string

  @ApiPropertyOptional({ description: "City" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  city?: string

  @ApiPropertyOptional({ description: "State" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  state?: string

  @ApiPropertyOptional({ description: "PIN code" })
  @IsOptional()
  @IsString()
  @Length(6, 6)
  pincode?: string

  @ApiPropertyOptional({ description: "Latitude" })
  @IsOptional()
  @IsLatitude()
  latitude?: number

  @ApiPropertyOptional({ description: "Longitude" })
  @IsOptional()
  @IsLongitude()
  longitude?: number

  @ApiPropertyOptional({ description: "Star rating", minimum: 1, maximum: 5 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  starRating?: number

  @ApiPropertyOptional({ description: "Hotel amenities", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  amenities?: string[]

  @ApiPropertyOptional({ description: "Check-in time (HH:MM format)" })
  @IsOptional()
  @IsString()
  checkInTime?: string

  @ApiPropertyOptional({ description: "Check-out time (HH:MM format)" })
  @IsOptional()
  @IsString()
  checkOutTime?: string

  @ApiPropertyOptional({ description: "Cancellation policy" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  cancellationPolicy?: string
}

export class HotelQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10

  @ApiPropertyOptional({ enum: HotelStatus, description: "Filter by status" })
  @IsOptional()
  @IsEnum(HotelStatus)
  status?: HotelStatus

  @ApiPropertyOptional({ description: "Filter by city" })
  @IsOptional()
  @IsString()
  city?: string

  @ApiPropertyOptional({ description: "Search query" })
  @IsOptional()
  @IsString()
  search?: string
}
