import { Controller, Get, Put, UseGuards, HttpStatus } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger"
import { JwtAuthGuard } from "../common/guards/jwt-auth.guard"
import { RolesGuard } from "../common/guards/roles.guard"
import { Roles } from "../common/decorators/roles.decorator"
import type { AdminService } from "./admin.service"
import type { AdminQueryDto, UserManagementDto, PlatformConfigDto } from "./dto/admin.dto"
import { ApiResponseDto } from "../common/dto/api-response.dto"

@ApiTags("Admin Management")
@Controller("admin")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles("admin")
@ApiBearerAuth()
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get("dashboard")
  @ApiOperation({ summary: "Get admin dashboard overview" })
  @ApiResponse({ status: HttpStatus.OK, description: "Dashboard data retrieved successfully" })
  async getDashboard() {
    const dashboard = await this.adminService.getDashboardOverview()
    return new ApiResponseDto(true, "Dashboard data retrieved successfully", dashboard)
  }

  @Get("users")
  @ApiOperation({ summary: "Get all users with filters" })
  async getUsers(query: AdminQueryDto) {
    const users = await this.adminService.getUsers(query)
    return new ApiResponseDto(true, "Users retrieved successfully", users)
  }

  @Get("users/:id")
  @ApiOperation({ summary: "Get user details by ID" })
  async getUserById(id: string) {
    const user = await this.adminService.getUserById(id)
    return new ApiResponseDto(true, "User details retrieved successfully", user)
  }

  @Put("users/:id")
  @ApiOperation({ summary: "Update user details" })
  async updateUser(id: string, updateDto: UserManagementDto) {
    const user = await this.adminService.updateUser(id, updateDto)
    return new ApiResponseDto(true, "User updated successfully", user)
  }

  @Put("users/:id/suspend")
  @ApiOperation({ summary: "Suspend user account" })
  async suspendUser(id: string, body: { reason: string; duration?: number }) {
    const result = await this.adminService.suspendUser(id, body.reason, body.duration)
    return new ApiResponseDto(true, "User suspended successfully", result)
  }

  @Put("users/:id/activate")
  @ApiOperation({ summary: "Activate suspended user account" })
  async activateUser(id: string) {
    const result = await this.adminService.activateUser(id)
    return new ApiResponseDto(true, "User activated successfully", result)
  }

  @Get("bookings")
  @ApiOperation({ summary: "Get all bookings with filters" })
  async getBookings(query: AdminQueryDto) {
    const bookings = await this.adminService.getBookings(query)
    return new ApiResponseDto(true, "Bookings retrieved successfully", bookings)
  }

  @Get("payments")
  @ApiOperation({ summary: "Get all payments with filters" })
  async getPayments(query: AdminQueryDto) {
    const payments = await this.adminService.getPayments(query)
    return new ApiResponseDto(true, "Payments retrieved successfully", payments)
  }

  @Get("disputes")
  @ApiOperation({ summary: "Get all disputes" })
  async getDisputes(query: AdminQueryDto) {
    const disputes = await this.adminService.getDisputes(query)
    return new ApiResponseDto(true, "Disputes retrieved successfully", disputes)
  }

  @Put("disputes/:id/resolve")
  @ApiOperation({ summary: "Resolve dispute" })
  async resolveDispute(id: string, body: { resolution: string; refundAmount?: number }) {
    const result = await this.adminService.resolveDispute(id, body.resolution, body.refundAmount)
    return new ApiResponseDto(true, "Dispute resolved successfully", result)
  }

  @Get("config")
  @ApiOperation({ summary: "Get platform configuration" })
  async getConfig() {
    const config = await this.adminService.getPlatformConfig()
    return new ApiResponseDto(true, "Configuration retrieved successfully", config)
  }

  @Put("config")
  @ApiOperation({ summary: "Update platform configuration" })
  async updateConfig(configDto: PlatformConfigDto) {
    const config = await this.adminService.updatePlatformConfig(configDto)
    return new ApiResponseDto(true, "Configuration updated successfully", config)
  }

  @Get("reports/revenue")
  @ApiOperation({ summary: "Get revenue reports" })
  async getRevenueReport(query: { startDate?: string; endDate?: string; groupBy?: string }) {
    const report = await this.adminService.getRevenueReport(query.startDate, query.endDate, query.groupBy)
    return new ApiResponseDto(true, "Revenue report generated successfully", report)
  }

  @Get("reports/activity")
  @ApiOperation({ summary: "Get platform activity reports" })
  async getActivityReport(query: { startDate?: string; endDate?: string }) {
    const report = await this.adminService.getActivityReport(query.startDate, query.endDate)
    return new ApiResponseDto(true, "Activity report generated successfully", report)
  }
}
