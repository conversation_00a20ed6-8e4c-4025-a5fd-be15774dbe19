import { type OnM<PERSON>ule<PERSON><PERSON><PERSON> } from "@nestjs/common";
import type { ConfigService } from "@nestjs/config";
export declare class RedisService implements OnModuleDestroy {
    private configService;
    private client;
    constructor(configService: ConfigService);
    onModuleDestroy(): Promise<void>;
    get(key: string): Promise<string | null>;
    set(key: string, value: string, ttl?: number): Promise<void>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
    incr(key: string): Promise<number>;
    expire(key: string, seconds: number): Promise<void>;
    hSet(key: string, field: string, value: string): Promise<void>;
    hGet(key: string, field: string): Promise<string | undefined>;
    hGetAll(key: string): Promise<Record<string, string>>;
}
