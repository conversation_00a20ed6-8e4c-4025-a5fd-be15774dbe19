import { <PERSON>, <PERSON>, Headers, Req, HttpStatus } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse, ApiExcludeEndpoint } from "@nestjs/swagger"
import type { PaymentService } from "./payment.service"
import type { StripeService } from "./services/stripe.service"
import type { RazorpayService } from "./services/razorpay.service"
import type { PrismaService } from "../common/services/prisma.service"
import { PaymentStatus } from "@prisma/client"

@ApiTags("Payment Webhooks")
@Controller("webhooks")
export class WebhookController {
  constructor(
    private paymentService: PaymentService,
    private stripeService: StripeService,
    private razorpayService: RazorpayService,
    private prisma: PrismaService,
  ) {}

  @Post("stripe")
  @ApiExcludeEndpoint()
  @ApiOperation({ summary: "Handle Stripe webhooks" })
  @ApiResponse({ status: HttpStatus.OK, description: "Webhook processed successfully" })
  async handleStripeWebhook(@Req() req: any, @Headers("stripe-signature") signature: string) {
    try {
      const payload = req.body
      const event = await this.stripeService.constructWebhookEvent(payload, signature)

      switch (event.type) {
        case "payment_intent.succeeded":
          await this.handleStripePaymentSuccess(event.data.object as any)
          break

        case "payment_intent.payment_failed":
          await this.handleStripePaymentFailed(event.data.object as any)
          break

        case "refund.created":
          await this.handleStripeRefundCreated(event.data.object as any)
          break

        case "refund.updated":
          await this.handleStripeRefundUpdated(event.data.object as any)
          break

        default:
          console.log(`Unhandled Stripe event type: ${event.type}`)
      }

      return { received: true }
    } catch (error) {
      console.error("Stripe webhook error:", error)
      throw error
    }
  }

  @Post("razorpay")
  @ApiExcludeEndpoint()
  @ApiOperation({ summary: "Handle Razorpay webhooks" })
  @ApiResponse({ status: HttpStatus.OK, description: "Webhook processed successfully" })
  async handleRazorpayWebhook(@Req() req: any, @Headers("x-razorpay-signature") signature: string) {
    try {
      const payload = req.body
      const isValid = await this.razorpayService.verifyWebhookSignature(payload.toString(), signature)

      if (!isValid) {
        throw new Error("Invalid webhook signature")
      }

      const event = JSON.parse(payload.toString())

      switch (event.event) {
        case "payment.captured":
          await this.handleRazorpayPaymentCaptured(event.payload.payment.entity)
          break

        case "payment.failed":
          await this.handleRazorpayPaymentFailed(event.payload.payment.entity)
          break

        case "refund.created":
          await this.handleRazorpayRefundCreated(event.payload.refund.entity)
          break

        case "refund.processed":
          await this.handleRazorpayRefundProcessed(event.payload.refund.entity)
          break

        default:
          console.log(`Unhandled Razorpay event type: ${event.event}`)
      }

      return { received: true }
    } catch (error) {
      console.error("Razorpay webhook error:", error)
      throw error
    }
  }

  private async handleStripePaymentSuccess(paymentIntent: any) {
    const payment = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: paymentIntent.id },
      include: { booking: true },
    })

    if (payment && payment.status === PaymentStatus.PENDING) {
      await this.prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          gatewayResponse: paymentIntent,
        },
      })

      // Update booking status
      await this.prisma.booking.update({
        where: { id: payment.bookingId },
        data: {
          status: "CONFIRMED",
          confirmedAt: new Date(),
        },
      })

      // Create vendor payout
      await this.createVendorPayout(payment)
    }
  }

  private async handleStripePaymentFailed(paymentIntent: any) {
    const payment = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: paymentIntent.id },
    })

    if (payment && payment.status === PaymentStatus.PENDING) {
      await this.prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: PaymentStatus.FAILED,
          failureReason: paymentIntent.last_payment_error?.message || "Payment failed",
          gatewayResponse: paymentIntent,
        },
      })
    }
  }

  private async handleStripeRefundCreated(refund: any) {
    const payment = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: refund.payment_intent },
    })

    if (payment) {
      await this.prisma.refund.updateMany({
        where: {
          paymentId: payment.id,
          gatewayRefundId: refund.id,
        },
        data: {
          status: "PROCESSING",
          gatewayResponse: refund,
        },
      })
    }
  }

  private async handleStripeRefundUpdated(refund: any) {
    const payment = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: refund.payment_intent },
    })

    if (payment) {
      await this.prisma.refund.updateMany({
        where: {
          paymentId: payment.id,
          gatewayRefundId: refund.id,
        },
        data: {
          status: refund.status === "succeeded" ? "COMPLETED" : "FAILED",
          processedAt: refund.status === "succeeded" ? new Date() : undefined,
          gatewayResponse: refund,
        },
      })
    }
  }

  private async handleRazorpayPaymentCaptured(payment: any) {
    const paymentRecord = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: payment.order_id },
      include: { booking: true },
    })

    if (paymentRecord && paymentRecord.status === PaymentStatus.PENDING) {
      await this.prisma.payment.update({
        where: { id: paymentRecord.id },
        data: {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          gatewayResponse: payment,
        },
      })

      // Update booking status
      await this.prisma.booking.update({
        where: { id: paymentRecord.bookingId },
        data: {
          status: "CONFIRMED",
          confirmedAt: new Date(),
        },
      })

      // Create vendor payout
      await this.createVendorPayout(paymentRecord)
    }
  }

  private async handleRazorpayPaymentFailed(payment: any) {
    const paymentRecord = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: payment.order_id },
    })

    if (paymentRecord && paymentRecord.status === PaymentStatus.PENDING) {
      await this.prisma.payment.update({
        where: { id: paymentRecord.id },
        data: {
          status: PaymentStatus.FAILED,
          failureReason: payment.error_description || "Payment failed",
          gatewayResponse: payment,
        },
      })
    }
  }

  private async handleRazorpayRefundCreated(refund: any) {
    const payment = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: refund.payment_id },
    })

    if (payment) {
      await this.prisma.refund.updateMany({
        where: {
          paymentId: payment.id,
          gatewayRefundId: refund.id,
        },
        data: {
          status: "PROCESSING",
          gatewayResponse: refund,
        },
      })
    }
  }

  private async handleRazorpayRefundProcessed(refund: any) {
    const payment = await this.prisma.payment.findFirst({
      where: { gatewayPaymentId: refund.payment_id },
    })

    if (payment) {
      await this.prisma.refund.updateMany({
        where: {
          paymentId: payment.id,
          gatewayRefundId: refund.id,
        },
        data: {
          status: "COMPLETED",
          processedAt: new Date(),
          gatewayResponse: refund,
        },
      })
    }
  }

  private async createVendorPayout(payment: any) {
    const vendorRevenue = payment.subtotal - payment.platformFee

    await this.prisma.payout.create({
      data: {
        vendorId:
          payment.booking.hotel?.vendorId || payment.booking.guide?.vendorId || payment.booking.package?.vendorId,
        paymentId: payment.id,
        amount: vendorRevenue,
        status: "PENDING",
        scheduledFor: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      },
    })
  }
}
