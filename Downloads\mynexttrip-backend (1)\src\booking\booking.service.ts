import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from "@nestjs/common"
import type { PrismaService } from "../common/services/prisma.service"
import type { RedisService } from "../common/services/redis.service"
import type { CreateBookingDto, UpdateBookingDto, BookingQueryDto, AvailabilityCheckDto } from "./dto/booking.dto"
import { BookingStatus } from "@prisma/client"

@Injectable()
export class BookingService {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  async checkAvailability(availabilityDto: AvailabilityCheckDto) {
    const { listingType, listingId, checkInDate, checkOutDate, guests, roomId } = availabilityDto

    const checkIn = new Date(checkInDate)
    const checkOut = new Date(checkOutDate)

    if (checkIn >= checkOut) {
      throw new BadRequestException("Check-out date must be after check-in date")
    }

    if (checkIn < new Date()) {
      throw new BadRequestException("Check-in date cannot be in the past")
    }

    switch (listingType) {
      case "hotel":
        return this.checkHotelAvailability(listingId, checkIn, checkOut, guests, roomId)
      case "guide":
        return this.checkGuideAvailability(listingId, checkIn, checkOut)
      case "package":
        return this.checkPackageAvailability(listingId, checkIn, guests)
      default:
        throw new BadRequestException("Invalid listing type")
    }
  }

  async createBooking(userId: string, createBookingDto: CreateBookingDto) {
    const { listingType, listingId, checkInDate, checkOutDate, guests, roomId, specialRequests } = createBookingDto

    // Check availability first
    const availability = await this.checkAvailability({
      listingType,
      listingId,
      checkInDate,
      checkOutDate,
      guests,
      roomId,
    })

    if (!availability.isAvailable) {
      throw new BadRequestException("Selected dates are not available")
    }

    // Calculate total amount
    const totalAmount = await this.calculateBookingAmount(createBookingDto)

    // Create booking
    const booking = await this.prisma.booking.create({
      data: {
        userId,
        ...(listingType === "hotel" && { hotelId: listingId, roomId }),
        ...(listingType === "guide" && { guideId: listingId }),
        ...(listingType === "package" && { packageId: listingId }),
        checkInDate: new Date(checkInDate),
        checkOutDate: new Date(checkOutDate),
        guests,
        totalAmount,
        specialRequests,
        status: BookingStatus.PENDING,
        bookingReference: this.generateBookingReference(),
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        },
        hotel: {
          include: {
            vendor: {
              select: {
                businessName: true,
                contactPhone: true,
                contactEmail: true,
              },
            },
          },
        },
        guide: {
          include: {
            vendor: {
              select: {
                businessName: true,
                contactPhone: true,
                contactEmail: true,
              },
            },
          },
        },
        package: {
          include: {
            vendor: {
              select: {
                businessName: true,
                contactPhone: true,
                contactEmail: true,
              },
            },
          },
        },
        room: true,
      },
    })

    // Update availability if needed
    await this.updateInventoryAfterBooking(booking)

    return booking
  }

  async getUserBookings(userId: string, query: BookingQueryDto) {
    const { page = 1, limit = 10, status, startDate, endDate } = query
    const skip = (page - 1) * limit

    const where: any = {
      userId,
    }

    if (status) {
      where.status = status
    }

    if (startDate || endDate) {
      where.checkInDate = {}
      if (startDate) where.checkInDate.gte = new Date(startDate)
      if (endDate) where.checkInDate.lte = new Date(endDate)
    }

    const [bookings, total] = await Promise.all([
      this.prisma.booking.findMany({
        where,
        include: {
          hotel: {
            select: {
              name: true,
              city: true,
              state: true,
              images: {
                take: 1,
                orderBy: { isPrimary: "desc" },
              },
            },
          },
          guide: {
            select: {
              name: true,
              city: true,
              state: true,
              images: {
                take: 1,
                orderBy: { isPrimary: "desc" },
              },
            },
          },
          package: {
            select: {
              name: true,
              city: true,
              state: true,
              duration: true,
              images: {
                take: 1,
                orderBy: { isPrimary: "desc" },
              },
            },
          },
          room: {
            select: {
              type: true,
              maxOccupancy: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      this.prisma.booking.count({ where }),
    ])

    return {
      bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getBookingById(userId: string, bookingId: string, userRole: string) {
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        },
        hotel: {
          include: {
            vendor: {
              select: {
                businessName: true,
                contactPhone: true,
                contactEmail: true,
                userId: true,
              },
            },
            images: true,
          },
        },
        guide: {
          include: {
            vendor: {
              select: {
                businessName: true,
                contactPhone: true,
                contactEmail: true,
                userId: true,
              },
            },
            images: true,
          },
        },
        package: {
          include: {
            vendor: {
              select: {
                businessName: true,
                contactPhone: true,
                contactEmail: true,
                userId: true,
              },
            },
            images: true,
          },
        },
        room: true,
        payments: true,
      },
    })

    if (!booking) {
      throw new NotFoundException("Booking not found")
    }

    // Check access permissions
    const isOwner = booking.userId === userId
    const isVendor =
      userRole === "vendor" &&
      (booking.hotel?.vendor?.userId === userId ||
        booking.guide?.vendor?.userId === userId ||
        booking.package?.vendor?.userId === userId)

    if (!isOwner && !isVendor) {
      throw new ForbiddenException("Access denied")
    }

    return booking
  }

  async updateBooking(userId: string, bookingId: string, updateBookingDto: UpdateBookingDto) {
    const booking = await this.prisma.booking.findFirst({
      where: {
        id: bookingId,
        userId,
      },
    })

    if (!booking) {
      throw new NotFoundException("Booking not found")
    }

    if (booking.status !== BookingStatus.PENDING) {
      throw new BadRequestException("Only pending bookings can be updated")
    }

    // Check if update affects dates and recalculate if needed
    let totalAmount = booking.totalAmount
    if (updateBookingDto.checkInDate || updateBookingDto.checkOutDate || updateBookingDto.guests) {
      const updatedBookingData = {
        ...booking,
        ...updateBookingDto,
        checkInDate: updateBookingDto.checkInDate || booking.checkInDate.toISOString(),
        checkOutDate: updateBookingDto.checkOutDate || booking.checkOutDate.toISOString(),
      }
      totalAmount = await this.calculateBookingAmount(updatedBookingData as any)
    }

    const updatedBooking = await this.prisma.booking.update({
      where: { id: bookingId },
      data: {
        ...updateBookingDto,
        ...(updateBookingDto.checkInDate && { checkInDate: new Date(updateBookingDto.checkInDate) }),
        ...(updateBookingDto.checkOutDate && { checkOutDate: new Date(updateBookingDto.checkOutDate) }),
        totalAmount,
      },
      include: {
        hotel: true,
        guide: true,
        package: true,
        room: true,
      },
    })

    return updatedBooking
  }

  async cancelBooking(userId: string, bookingId: string) {
    const booking = await this.prisma.booking.findFirst({
      where: {
        id: bookingId,
        userId,
      },
    })

    if (!booking) {
      throw new NotFoundException("Booking not found")
    }

    if (booking.status === BookingStatus.CANCELLED) {
      throw new BadRequestException("Booking is already cancelled")
    }

    if (booking.status === BookingStatus.COMPLETED) {
      throw new BadRequestException("Cannot cancel completed booking")
    }

    const updatedBooking = await this.prisma.booking.update({
      where: { id: bookingId },
      data: {
        status: BookingStatus.CANCELLED,
        cancelledAt: new Date(),
      },
      include: {
        hotel: true,
        guide: true,
        package: true,
        room: true,
      },
    })

    // Restore inventory
    await this.restoreInventoryAfterCancellation(updatedBooking)

    return updatedBooking
  }

  async confirmBooking(vendorUserId: string, bookingId: string) {
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        hotel: { include: { vendor: true } },
        guide: { include: { vendor: true } },
        package: { include: { vendor: true } },
      },
    })

    if (!booking) {
      throw new NotFoundException("Booking not found")
    }

    // Check if user is the vendor
    const isVendor =
      booking.hotel?.vendor?.userId === vendorUserId ||
      booking.guide?.vendor?.userId === vendorUserId ||
      booking.package?.vendor?.userId === vendorUserId

    if (!isVendor) {
      throw new ForbiddenException("Access denied")
    }

    if (booking.status !== BookingStatus.PENDING) {
      throw new BadRequestException("Only pending bookings can be confirmed")
    }

    const updatedBooking = await this.prisma.booking.update({
      where: { id: bookingId },
      data: {
        status: BookingStatus.CONFIRMED,
        confirmedAt: new Date(),
      },
      include: {
        user: true,
        hotel: true,
        guide: true,
        package: true,
        room: true,
      },
    })

    return updatedBooking
  }

  async rejectBooking(vendorUserId: string, bookingId: string, reason?: string) {
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        hotel: { include: { vendor: true } },
        guide: { include: { vendor: true } },
        package: { include: { vendor: true } },
      },
    })

    if (!booking) {
      throw new NotFoundException("Booking not found")
    }

    // Check if user is the vendor
    const isVendor =
      booking.hotel?.vendor?.userId === vendorUserId ||
      booking.guide?.vendor?.userId === vendorUserId ||
      booking.package?.vendor?.userId === vendorUserId

    if (!isVendor) {
      throw new ForbiddenException("Access denied")
    }

    if (booking.status !== BookingStatus.PENDING) {
      throw new BadRequestException("Only pending bookings can be rejected")
    }

    const updatedBooking = await this.prisma.booking.update({
      where: { id: bookingId },
      data: {
        status: BookingStatus.CANCELLED,
        cancelledAt: new Date(),
        cancellationReason: reason,
      },
      include: {
        user: true,
        hotel: true,
        guide: true,
        package: true,
        room: true,
      },
    })

    // Restore inventory
    await this.restoreInventoryAfterCancellation(updatedBooking)

    return updatedBooking
  }

  async generateInvoice(userId: string, bookingId: string, userRole: string) {
    const booking = await this.getBookingById(userId, bookingId, userRole)

    const invoice = {
      bookingReference: booking.bookingReference,
      invoiceNumber: `INV-${booking.bookingReference}`,
      issueDate: new Date().toISOString(),
      dueDate: booking.checkInDate,
      customer: {
        name: `${booking.user.firstName} ${booking.user.lastName}`,
        email: booking.user.email,
        phone: booking.user.phone,
      },
      vendor: {
        name:
          booking.hotel?.vendor?.businessName ||
          booking.guide?.vendor?.businessName ||
          booking.package?.vendor?.businessName,
        phone:
          booking.hotel?.vendor?.contactPhone ||
          booking.guide?.vendor?.contactPhone ||
          booking.package?.vendor?.contactPhone,
        email:
          booking.hotel?.vendor?.contactEmail ||
          booking.guide?.vendor?.contactEmail ||
          booking.package?.vendor?.contactEmail,
      },
      listing: {
        name: booking.hotel?.name || booking.guide?.name || booking.package?.name,
        type: booking.hotel ? "Hotel" : booking.guide ? "Guide" : "Package",
        location: `${booking.hotel?.city || booking.guide?.city || booking.package?.city}, ${
          booking.hotel?.state || booking.guide?.state || booking.package?.state
        }`,
      },
      bookingDetails: {
        checkIn: booking.checkInDate,
        checkOut: booking.checkOutDate,
        guests: booking.guests,
        duration: booking.package?.duration || this.calculateNights(booking.checkInDate, booking.checkOutDate),
        room: booking.room?.type,
      },
      amount: {
        subtotal: booking.totalAmount,
        taxes: booking.totalAmount * 0.18, // 18% GST
        total: booking.totalAmount * 1.18,
      },
      status: booking.status,
      payments: booking.payments,
    }

    return invoice
  }

  private async checkHotelAvailability(
    hotelId: string,
    checkIn: Date,
    checkOut: Date,
    guests?: number,
    roomId?: string,
  ) {
    const hotel = await this.prisma.hotel.findUnique({
      where: { id: hotelId, status: "ACTIVE" },
      include: {
        rooms: {
          where: {
            ...(roomId && { id: roomId }),
            ...(guests && { maxOccupancy: { gte: guests } }),
          },
        },
      },
    })

    if (!hotel) {
      throw new NotFoundException("Hotel not found")
    }

    // Check for conflicting bookings
    const conflictingBookings = await this.prisma.booking.count({
      where: {
        hotelId,
        ...(roomId && { roomId }),
        status: { in: [BookingStatus.CONFIRMED, BookingStatus.PENDING] },
        OR: [
          {
            checkInDate: { lt: checkOut },
            checkOutDate: { gt: checkIn },
          },
        ],
      },
    })

    const availableRooms = hotel.rooms.filter((room) => room.availableRooms > 0)

    return {
      isAvailable: conflictingBookings === 0 && availableRooms.length > 0,
      availableRooms,
      conflictingBookings,
      hotel: {
        id: hotel.id,
        name: hotel.name,
        checkInTime: hotel.checkInTime,
        checkOutTime: hotel.checkOutTime,
      },
    }
  }

  private async checkGuideAvailability(guideId: string, checkIn: Date, checkOut: Date) {
    const guide = await this.prisma.guide.findUnique({
      where: { id: guideId, status: "ACTIVE" },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    // Check guide availability calendar
    const unavailableDates = await this.prisma.guideAvailability.findMany({
      where: {
        guideId,
        date: {
          gte: checkIn,
          lte: checkOut,
        },
        isAvailable: false,
      },
    })

    // Check for conflicting bookings
    const conflictingBookings = await this.prisma.booking.count({
      where: {
        guideId,
        status: { in: [BookingStatus.CONFIRMED, BookingStatus.PENDING] },
        OR: [
          {
            checkInDate: { lt: checkOut },
            checkOutDate: { gt: checkIn },
          },
        ],
      },
    })

    return {
      isAvailable: unavailableDates.length === 0 && conflictingBookings === 0,
      unavailableDates,
      conflictingBookings,
      guide: {
        id: guide.id,
        name: guide.name,
        maxGroupSize: guide.maxGroupSize,
        pricePerDay: guide.pricePerDay,
      },
    }
  }

  private async checkPackageAvailability(packageId: string, checkIn: Date, guests?: number) {
    const packageData = await this.prisma.package.findUnique({
      where: { id: packageId, status: "ACTIVE" },
    })

    if (!packageData) {
      throw new NotFoundException("Package not found")
    }

    const isAvailable = !guests || guests <= packageData.maxGroupSize

    return {
      isAvailable,
      package: {
        id: packageData.id,
        name: packageData.name,
        duration: packageData.duration,
        maxGroupSize: packageData.maxGroupSize,
        price: packageData.price,
      },
    }
  }

  private async calculateBookingAmount(bookingData: any): Promise<number> {
    const { listingType, listingId, checkInDate, checkOutDate, guests, roomId } = bookingData

    const checkIn = new Date(checkInDate)
    const checkOut = new Date(checkOutDate)
    const nights = this.calculateNights(checkIn, checkOut)

    switch (listingType) {
      case "hotel":
        const room = await this.prisma.room.findUnique({
          where: { id: roomId },
        })
        return room ? room.basePrice * nights : 0

      case "guide":
        const guide = await this.prisma.guide.findUnique({
          where: { id: listingId },
        })
        return guide ? guide.pricePerDay * nights : 0

      case "package":
        const packageData = await this.prisma.package.findUnique({
          where: { id: listingId },
        })
        return packageData ? packageData.price * (guests || 1) : 0

      default:
        return 0
    }
  }

  private calculateNights(checkIn: Date, checkOut: Date): number {
    const timeDiff = checkOut.getTime() - checkIn.getTime()
    return Math.ceil(timeDiff / (1000 * 3600 * 24))
  }

  private generateBookingReference(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 8)
    return `MNT${timestamp}${random}`.toUpperCase()
  }

  private async updateInventoryAfterBooking(booking: any) {
    if (booking.roomId) {
      // Decrease available rooms
      await this.prisma.room.update({
        where: { id: booking.roomId },
        data: {
          availableRooms: {
            decrement: 1,
          },
        },
      })
    }
  }

  private async restoreInventoryAfterCancellation(booking: any) {
    if (booking.roomId) {
      // Increase available rooms
      await this.prisma.room.update({
        where: { id: booking.roomId },
        data: {
          availableRooms: {
            increment: 1,
          },
        },
      })
    }
  }
}
