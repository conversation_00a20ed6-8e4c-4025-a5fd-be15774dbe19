import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { CreateReviewDto, UpdateReviewDto, ReviewQueryDto, ReviewModerationDto } from "./dto/review.dto";
export declare class ReviewService {
    private prisma;
    private redis;
    constructor(prisma: PrismaService, redis: RedisService);
    createReview(userId: string, createReviewDto: CreateReviewDto): Promise<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>;
    getListingReviews(listingId: string, query: ReviewQueryDto): Promise<{
        reviews: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
        summary: {
            averageRating: any;
            totalReviews: any;
            ratingDistribution: any;
        };
    }>;
    getUserReviews(userId: string, query: ReviewQueryDto): Promise<{
        reviews: {
            title: string | null;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            images: string[];
            rating: number;
            bookingId: string;
            comment: string;
            isApproved: boolean;
            moderatedAt: Date | null;
            moderationNotes: string | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getReviewById(reviewId: string): Promise<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>;
    updateReview(userId: string, reviewId: string, updateReviewDto: UpdateReviewDto): Promise<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>;
    deleteReview(userId: string, reviewId: string, userRole: string): Promise<void>;
    markReviewHelpful(userId: string, reviewId: string): Promise<{
        helpful: boolean;
    }>;
    reportReview(userId: string, reviewId: string, reason: string): Promise<any>;
    getPendingReviews(query: ReviewQueryDto): Promise<{
        reviews: {
            title: string | null;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            images: string[];
            rating: number;
            bookingId: string;
            comment: string;
            isApproved: boolean;
            moderatedAt: Date | null;
            moderationNotes: string | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    moderateReview(reviewId: string, moderationDto: ReviewModerationDto): Promise<{
        title: string | null;
        id: string;
        userId: string;
        createdAt: Date;
        updatedAt: Date;
        listingId: string;
        images: string[];
        rating: number;
        bookingId: string;
        comment: string;
        isApproved: boolean;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>;
    getReportedReviews(query: ReviewQueryDto): Promise<{
        reports: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    private updateListingRating;
    private getReviewOrderBy;
}
