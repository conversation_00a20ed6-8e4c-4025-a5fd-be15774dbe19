import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { CreateReviewDto, UpdateReviewDto, ReviewQueryDto, ReviewModerationDto } from "./dto/review.dto";
export declare class ReviewService {
    private prisma;
    private redis;
    constructor(prisma: PrismaService, redis: RedisService);
    createReview(userId: string, createReviewDto: CreateReviewDto): Promise<any>;
    getListingReviews(listingId: string, query: ReviewQueryDto): Promise<{
        reviews: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
        summary: {
            averageRating: any;
            totalReviews: any;
            ratingDistribution: any;
        };
    }>;
    getUserReviews(userId: string, query: ReviewQueryDto): Promise<{
        reviews: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getReviewById(reviewId: string): Promise<any>;
    updateReview(userId: string, reviewId: string, updateReviewDto: UpdateReviewDto): Promise<any>;
    deleteReview(userId: string, reviewId: string, userRole: string): Promise<void>;
    markReviewHelpful(userId: string, reviewId: string): Promise<{
        helpful: boolean;
    }>;
    reportReview(userId: string, reviewId: string, reason: string): Promise<any>;
    getPendingReviews(query: ReviewQueryDto): Promise<{
        reviews: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    moderateReview(reviewId: string, moderationDto: ReviewModerationDto): Promise<any>;
    getReportedReviews(query: ReviewQueryDto): Promise<{
        reports: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    private updateListingRating;
    private getReviewOrderBy;
}
