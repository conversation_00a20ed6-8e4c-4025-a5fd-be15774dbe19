"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotelService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let HotelService = class HotelService {
    constructor(prisma, typesense) {
        this.prisma = prisma;
        this.typesense = typesense;
    }
    async createHotel(userId, createHotelDto) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const hotel = await this.prisma.hotel.create({
            data: {
                vendorId: vendor.id,
                name: createHotelDto.name,
                description: createHotelDto.description,
                address: createHotelDto.address,
                city: createHotelDto.city,
                state: createHotelDto.state,
                pincode: createHotelDto.pincode,
                latitude: createHotelDto.latitude,
                longitude: createHotelDto.longitude,
                starRating: createHotelDto.starRating,
                amenities: createHotelDto.amenities,
                checkInTime: createHotelDto.checkInTime,
                checkOutTime: createHotelDto.checkOutTime,
                cancellationPolicy: createHotelDto.cancellationPolicy,
                status: client_1.HotelStatus.DRAFT,
                rooms: {
                    create: createHotelDto.rooms?.map((room) => ({
                        type: room.type,
                        description: room.description,
                        maxOccupancy: room.maxOccupancy,
                        basePrice: room.basePrice,
                        amenities: room.amenities,
                        totalRooms: room.totalRooms,
                        availableRooms: room.totalRooms,
                    })) || [],
                },
            },
            include: {
                rooms: true,
                images: true,
                vendor: {
                    select: {
                        businessName: true,
                        contactPhone: true,
                        contactEmail: true,
                    },
                },
            },
        });
        await this.indexHotelInTypesense(hotel);
        return hotel;
    }
    async getVendorHotels(userId, query) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const { page = 1, limit = 10, status, city, search } = query;
        const skip = (page - 1) * limit;
        const where = {
            vendorId: vendor.id,
        };
        if (status) {
            where.status = status;
        }
        if (city) {
            where.city = {
                contains: city,
                mode: "insensitive",
            };
        }
        if (search) {
            where.OR = [
                {
                    name: {
                        contains: search,
                        mode: "insensitive",
                    },
                },
                {
                    description: {
                        contains: search,
                        mode: "insensitive",
                    },
                },
            ];
        }
        const [hotels, total] = await Promise.all([
            this.prisma.hotel.findMany({
                where,
                include: {
                    images: {
                        take: 1,
                        orderBy: {
                            isPrimary: "desc",
                        },
                    },
                    rooms: {
                        select: {
                            id: true,
                            type: true,
                            basePrice: true,
                            maxOccupancy: true,
                        },
                    },
                    _count: {
                        select: {
                            bookings: true,
                            reviews: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
                skip,
                take: limit,
            }),
            this.prisma.hotel.count({ where }),
        ]);
        return {
            hotels,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getHotelById(userId, hotelId) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const hotel = await this.prisma.hotel.findFirst({
            where: {
                id: hotelId,
                vendorId: vendor.id,
            },
            include: {
                images: {
                    orderBy: {
                        isPrimary: "desc",
                    },
                },
                rooms: true,
                reviews: {
                    include: {
                        user: {
                            select: {
                                firstName: true,
                                lastName: true,
                                profilePicture: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 10,
                },
                _count: {
                    select: {
                        bookings: true,
                        reviews: true,
                    },
                },
            },
        });
        if (!hotel) {
            throw new common_1.NotFoundException("Hotel not found");
        }
        return hotel;
    }
    async updateHotel(userId, hotelId, updateHotelDto) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const hotel = await this.prisma.hotel.findFirst({
            where: {
                id: hotelId,
                vendorId: vendor.id,
            },
        });
        if (!hotel) {
            throw new common_1.NotFoundException("Hotel not found");
        }
        const updatedHotel = await this.prisma.hotel.update({
            where: { id: hotelId },
            data: updateHotelDto,
            include: {
                rooms: true,
                images: true,
                vendor: {
                    select: {
                        businessName: true,
                        contactPhone: true,
                        contactEmail: true,
                    },
                },
            },
        });
        await this.indexHotelInTypesense(updatedHotel);
        return updatedHotel;
    }
    async deleteHotel(userId, hotelId) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const hotel = await this.prisma.hotel.findFirst({
            where: {
                id: hotelId,
                vendorId: vendor.id,
            },
        });
        if (!hotel) {
            throw new common_1.NotFoundException("Hotel not found");
        }
        const activeBookings = await this.prisma.booking.count({
            where: {
                hotelId,
                status: {
                    in: ["PENDING", "CONFIRMED"],
                },
            },
        });
        if (activeBookings > 0) {
            throw new common_1.BadRequestException("Cannot delete hotel with active bookings");
        }
        await this.prisma.hotel.delete({
            where: { id: hotelId },
        });
        try {
            await this.typesense.client.collections("hotels").documents(hotelId).delete();
        }
        catch (error) {
            console.error("Error removing hotel from Typesense:", error);
        }
    }
    async uploadHotelImages(userId, hotelId, files) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const hotel = await this.prisma.hotel.findFirst({
            where: {
                id: hotelId,
                vendorId: vendor.id,
            },
        });
        if (!hotel) {
            throw new common_1.NotFoundException("Hotel not found");
        }
        const images = await Promise.all(files.map(async (file, index) => {
            return this.prisma.hotelImage.create({
                data: {
                    hotelId,
                    url: `/uploads/hotels/${hotelId}/${file.filename}`,
                    altText: `${hotel.name} - Image ${index + 1}`,
                    isPrimary: index === 0,
                },
            });
        }));
        return images;
    }
    async deleteHotelImage(userId, hotelId, imageId) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const hotel = await this.prisma.hotel.findFirst({
            where: {
                id: hotelId,
                vendorId: vendor.id,
            },
        });
        if (!hotel) {
            throw new common_1.NotFoundException("Hotel not found");
        }
        const image = await this.prisma.hotelImage.findFirst({
            where: {
                id: imageId,
                hotelId,
            },
        });
        if (!image) {
            throw new common_1.NotFoundException("Image not found");
        }
        await this.prisma.hotelImage.delete({
            where: { id: imageId },
        });
    }
    async updateHotelStatus(userId, hotelId, status) {
        const vendor = await this.prisma.vendor.findUnique({
            where: { userId },
        });
        if (!vendor) {
            throw new common_1.NotFoundException("Vendor profile not found");
        }
        const hotel = await this.prisma.hotel.findFirst({
            where: {
                id: hotelId,
                vendorId: vendor.id,
            },
        });
        if (!hotel) {
            throw new common_1.NotFoundException("Hotel not found");
        }
        const updatedHotel = await this.prisma.hotel.update({
            where: { id: hotelId },
            data: { status: status },
            include: {
                rooms: true,
                images: true,
            },
        });
        await this.indexHotelInTypesense(updatedHotel);
        return updatedHotel;
    }
    async indexHotelInTypesense(hotel) {
        try {
            const document = {
                id: hotel.id,
                name: hotel.name,
                description: hotel.description,
                city: hotel.city,
                state: hotel.state,
                starRating: hotel.starRating,
                amenities: hotel.amenities,
                averageRating: hotel.averageRating || 0,
                totalReviews: hotel.totalReviews || 0,
                minPrice: hotel.rooms?.length > 0 ? Math.min(...hotel.rooms.map((r) => r.basePrice)) : 0,
                maxPrice: hotel.rooms?.length > 0 ? Math.max(...hotel.rooms.map((r) => r.basePrice)) : 0,
                latitude: hotel.latitude,
                longitude: hotel.longitude,
                status: hotel.status,
                type: "hotel",
            };
            await this.typesense.client.collections("listings").documents().upsert(document);
        }
        catch (error) {
            console.error("Error indexing hotel in Typesense:", error);
        }
    }
};
exports.HotelService = HotelService;
exports.HotelService = HotelService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function, Function])
], HotelService);
//# sourceMappingURL=hotel.service.js.map