"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const api_response_dto_1 = require("../common/dto/api-response.dto");
let PaymentController = class PaymentController {
    constructor(paymentService) {
        this.paymentService = paymentService;
    }
    async createPaymentIntent(req, createPaymentDto) {
        const paymentIntent = await this.paymentService.createPaymentIntent(req.user.id, createPaymentDto);
        return new api_response_dto_1.ApiResponseDto(true, "Payment intent created successfully", paymentIntent);
    }
    async processPayment(req, body) {
        const payment = await this.paymentService.processPayment(req.user.id, body.paymentIntentId, body.paymentMethodId);
        return new api_response_dto_1.ApiResponseDto(true, "Payment processed successfully", payment);
    }
    async getPayments(req, query) {
        const payments = await this.paymentService.getUserPayments(req.user.id, req.user.role, query);
        return new api_response_dto_1.ApiResponseDto(true, "Payments retrieved successfully", payments);
    }
    async getPayment(req, id) {
        const payment = await this.paymentService.getPaymentById(req.user.id, id, req.user.role);
        return new api_response_dto_1.ApiResponseDto(true, "Payment retrieved successfully", payment);
    }
    async processRefund(req, id, refundDto) {
        const refund = await this.paymentService.processRefund(req.user.id, id, refundDto, req.user.role);
        return new api_response_dto_1.ApiResponseDto(true, "Refund processed successfully", refund);
    }
    async getSavedPaymentMethods(req) {
        const methods = await this.paymentService.getSavedPaymentMethods(req.user.id);
        return new api_response_dto_1.ApiResponseDto(true, "Payment methods retrieved successfully", methods);
    }
    async savePaymentMethod(req, body) {
        const method = await this.paymentService.savePaymentMethod(req.user.id, body.paymentMethodId, body.isDefault);
        return new api_response_dto_1.ApiResponseDto(true, "Payment method saved successfully", method);
    }
    async setDefaultPaymentMethod(req, methodId) {
        await this.paymentService.setDefaultPaymentMethod(req.user.id, methodId);
        return new api_response_dto_1.ApiResponseDto(true, "Default payment method updated successfully");
    }
    async getRevenueAnalytics(req, query) {
        const analytics = await this.paymentService.getVendorRevenueAnalytics(req.user.id, query.startDate, query.endDate);
        return new api_response_dto_1.ApiResponseDto(true, "Revenue analytics retrieved successfully", analytics);
    }
};
exports.PaymentController = PaymentController;
__decorate([
    (0, common_1.Post)("create-intent"),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Create payment intent" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: "Payment intent created successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "createPaymentIntent", null);
__decorate([
    (0, common_1.Post)("process"),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Process payment" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Payment processed successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "processPayment", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)("user", "vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get user payments" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getPayments", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, roles_decorator_1.Roles)("user", "vendor", "admin"),
    (0, swagger_1.ApiOperation)({ summary: "Get payment by ID" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getPayment", null);
__decorate([
    (0, common_1.Post)(":id/refund"),
    (0, roles_decorator_1.Roles)("admin", "vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Process refund" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Function]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "processRefund", null);
__decorate([
    (0, common_1.Get)("methods/saved"),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Get saved payment methods" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getSavedPaymentMethods", null);
__decorate([
    (0, common_1.Post)("methods/save"),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Save payment method" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "savePaymentMethod", null);
__decorate([
    (0, common_1.Put)("methods/:methodId/default"),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiOperation)({ summary: "Set default payment method" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "setDefaultPaymentMethod", null);
__decorate([
    (0, common_1.Get)("analytics/revenue"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor revenue analytics" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getRevenueAnalytics", null);
exports.PaymentController = PaymentController = __decorate([
    (0, swagger_1.ApiTags)("Payments"),
    (0, common_1.Controller)("payments"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [Function])
], PaymentController);
//# sourceMappingURL=payment.controller.js.map