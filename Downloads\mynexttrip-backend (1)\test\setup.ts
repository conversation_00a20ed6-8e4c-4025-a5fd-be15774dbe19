import { beforeAll, afterAll, jest } from "@jest/globals"

// Global test setup
beforeAll(async () => {
  // Setup test database
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || "postgresql://test:test@localhost:5433/mynexttrip_test"
  process.env.REDIS_URL = process.env.TEST_REDIS_URL || "redis://localhost:6380"
  process.env.NODE_ENV = "test"
})

afterAll(async () => {
  // Cleanup
})

// Mock external services for testing
jest.mock("stripe", () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    paymentIntents: {
      create: jest.fn(),
      retrieve: jest.fn(),
      confirm: jest.fn(),
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
  })),
}))

jest.mock("razorpay", () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    orders: {
      create: jest.fn(),
      fetch: jest.fn(),
    },
    payments: {
      fetch: jest.fn(),
    },
  })),
}))
