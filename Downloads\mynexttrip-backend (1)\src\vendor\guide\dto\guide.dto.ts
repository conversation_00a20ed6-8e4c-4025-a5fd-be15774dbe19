import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsNumber, IsArray, IsEnum, Length, Min, Max, IsInt } from "class-validator"
import { Type } from "class-transformer"
import { GuideStatus } from "@prisma/client"

export class CreateGuideDto {
  @ApiProperty({ description: "Guide name" })
  @IsString()
  @Length(2, 100)
  name: string

  @ApiPropertyOptional({ description: "Guide description" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  description?: string

  @ApiProperty({ description: "Years of experience" })
  @IsInt()
  @Min(0)
  @Max(50)
  experience: number

  @ApiProperty({ description: "Languages spoken", type: [String] })
  @IsArray()
  @IsString({ each: true })
  languages: string[]

  @ApiProperty({ description: "Specializations", type: [String] })
  @IsArray()
  @IsString({ each: true })
  specializations: string[]

  @ApiProperty({ description: "City" })
  @IsString()
  @Length(2, 50)
  city: string

  @ApiProperty({ description: "State" })
  @IsString()
  @Length(2, 50)
  state: string

  @ApiProperty({ description: "Price per day" })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  pricePerDay: number

  @ApiPropertyOptional({ description: "Price per hour" })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  pricePerHour?: number

  @ApiProperty({ description: "Maximum group size" })
  @IsInt()
  @Min(1)
  @Max(50)
  maxGroupSize: number

  @ApiPropertyOptional({ description: "Cancellation policy" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  cancellationPolicy?: string
}

export class UpdateGuideDto {
  @ApiPropertyOptional({ description: "Guide name" })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  name?: string

  @ApiPropertyOptional({ description: "Guide description" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  description?: string

  @ApiPropertyOptional({ description: "Years of experience" })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(50)
  experience?: number

  @ApiPropertyOptional({ description: "Languages spoken", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  languages?: string[]

  @ApiPropertyOptional({ description: "Specializations", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specializations?: string[]

  @ApiPropertyOptional({ description: "City" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  city?: string

  @ApiPropertyOptional({ description: "State" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  state?: string

  @ApiPropertyOptional({ description: "Price per day" })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  pricePerDay?: number

  @ApiPropertyOptional({ description: "Price per hour" })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  pricePerHour?: number

  @ApiPropertyOptional({ description: "Maximum group size" })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  maxGroupSize?: number

  @ApiPropertyOptional({ description: "Cancellation policy" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  cancellationPolicy?: string
}

export class GuideQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10

  @ApiPropertyOptional({ enum: GuideStatus, description: "Filter by status" })
  @IsOptional()
  @IsEnum(GuideStatus)
  status?: GuideStatus

  @ApiPropertyOptional({ description: "Filter by city" })
  @IsOptional()
  @IsString()
  city?: string

  @ApiPropertyOptional({ description: "Search query" })
  @IsOptional()
  @IsString()
  search?: string
}
