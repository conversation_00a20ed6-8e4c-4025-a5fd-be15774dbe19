import { Injectable } from "@nestjs/common"
import type { ConfigService } from "@nestjs/config"
import { Client as TypesenseClient } from "typesense"

@Injectable()
export class TypesenseService {
  private client: TypesenseClient

  constructor(private configService: ConfigService) {
    this.client = new TypesenseClient({
      nodes: [
        {
          host: this.configService.get("TYPESENSE_HOST") || "localhost",
          port: Number.parseInt(this.configService.get("TYPESENSE_PORT")) || 8108,
          protocol: this.configService.get("TYPESENSE_PROTOCOL") || "http",
        },
      ],
      apiKey: this.configService.get("TYPESENSE_API_KEY") || "xyz",
      connectionTimeoutSeconds: 2,
    })
  }

  getClient(): TypesenseClient {
    return this.client
  }

  async createCollection(schema: any) {
    try {
      await this.client.collections().create(schema)
    } catch (error) {
      if (error.httpStatus !== 409) {
        throw error
      }
    }
  }

  async indexDocument(collectionName: string, document: any) {
    return this.client.collections(collectionName).documents().create(document)
  }

  async updateDocument(collectionName: string, documentId: string, document: any) {
    return this.client.collections(collectionName).documents(documentId).update(document)
  }

  async deleteDocument(collectionName: string, documentId: string) {
    return this.client.collections(collectionName).documents(documentId).delete()
  }

  async search(collectionName: string, searchParameters: any) {
    return this.client.collections(collectionName).documents().search(searchParameters)
  }
}
