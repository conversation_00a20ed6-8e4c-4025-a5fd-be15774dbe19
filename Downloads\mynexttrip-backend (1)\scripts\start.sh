#!/bin/bash

# MyNextTrip Backend Startup Script

echo "🚀 Starting MyNextTrip Backend..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure your environment variables."
    exit 1
fi

# Load environment variables
source .env

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start infrastructure services
echo "🐳 Starting infrastructure services..."
docker-compose up -d postgres redis typesense

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run database migrations
echo "🗄️ Running database migrations..."
npx prisma migrate deploy

# Seed the database
echo "🌱 Seeding database with sample data..."
npx prisma db seed

# Start the application
echo "🎯 Starting the application..."
if [ "$NODE_ENV" = "production" ]; then
    npm run build
    npm run start:prod
else
    npm run start:dev
fi
