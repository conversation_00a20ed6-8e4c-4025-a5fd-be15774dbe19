"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VendorController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const api_response_dto_1 = require("../common/dto/api-response.dto");
let VendorController = class VendorController {
    constructor(vendorService) {
        this.vendorService = vendorService;
    }
    async registerVendor(createVendorDto, request) {
        const vendor = await this.vendorService.registerVendor(request.user.id, createVendorDto);
        return new api_response_dto_1.ApiResponseDto(true, "Vendor registered successfully", vendor);
    }
    async getProfile(request) {
        const vendor = await this.vendorService.getVendorProfile(request.user.id);
        return new api_response_dto_1.ApiResponseDto(true, "Vendor profile retrieved", vendor);
    }
    async updateProfile(request, updateVendorDto) {
        const vendor = await this.vendorService.updateVendorProfile(request.user.id, updateVendorDto);
        return new api_response_dto_1.ApiResponseDto(true, "Vendor profile updated", vendor);
    }
    async getDashboardStats(request) {
        const stats = await this.vendorService.getDashboardStats(request.user.id);
        return new api_response_dto_1.ApiResponseDto(true, "Dashboard stats retrieved", stats);
    }
    async getBookings(request, query) {
        const bookings = await this.vendorService.getVendorBookings(request.user.id, query);
        return new api_response_dto_1.ApiResponseDto(true, "Bookings retrieved", bookings);
    }
    async getEarnings(request, query) {
        const earnings = await this.vendorService.getVendorEarnings(request.user.id, query);
        return new api_response_dto_1.ApiResponseDto(true, "Earnings retrieved", earnings);
    }
};
exports.VendorController = VendorController;
__decorate([
    (0, common_1.Post)("register"),
    (0, swagger_1.ApiOperation)({ summary: "Register as vendor" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: "Vendor registered successfully" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function, Object]),
    __metadata("design:returntype", Promise)
], VendorController.prototype, "registerVendor", null);
__decorate([
    (0, common_1.Get)("profile"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor profile" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VendorController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Put)("profile"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Update vendor profile" }),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], VendorController.prototype, "updateProfile", null);
__decorate([
    (0, common_1.Get)("dashboard/stats"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor dashboard statistics" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VendorController.prototype, "getDashboardStats", null);
__decorate([
    (0, common_1.Get)("bookings"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor bookings" }),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], VendorController.prototype, "getBookings", null);
__decorate([
    (0, common_1.Get)("earnings"),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor earnings" }),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], VendorController.prototype, "getEarnings", null);
exports.VendorController = VendorController = __decorate([
    (0, swagger_1.ApiTags)("Vendor Management"),
    (0, common_1.Controller)("vendor"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [Function])
], VendorController);
//# sourceMappingURL=vendor.controller.js.map