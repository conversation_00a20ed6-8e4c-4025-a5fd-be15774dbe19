"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { PaymentMethodSelector } from "@/components/payment/payment-method-selector"
import { StripeCheckout } from "@/components/payment/stripe-checkout"
import { RazorpayCheckout } from "@/components/payment/razorpay-checkout"
import { Calendar, MapPin, Users, Star, Shield } from "lucide-react"
import { api } from "@/lib/api"
import { MainLayout } from "@/components/layout/main-layout"
import Image from "next/image"
import { format } from "date-fns"

interface BookingDetails {
  id: string
  listingId: string
  listingName: string
  listingType: "hotel" | "guide" | "package"
  listingImage: string
  location: string
  rating: number
  reviewCount: number
  checkIn: string
  checkOut: string
  guests: number
  basePrice: number
  taxes: number
  serviceFee: number
  totalAmount: number
  currency: "INR" | "USD"
  cancellationPolicy: string
  amenities: string[]
  specialRequests?: string
}

export default function BookingPage() {
  const params = useParams()
  const router = useRouter()
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<any>(null)
  const [showPayment, setShowPayment] = useState(false)

  const { data: booking, isLoading } = useQuery({
    queryKey: ["booking-details", params.id],
    queryFn: async () => {
      const response = await api.get(`/bookings/${params.id}`)
      return response.data as BookingDetails
    },
  })

  const handlePaymentSuccess = (paymentData: any) => {
    router.push(`/booking/${params.id}/confirmation?payment_id=${paymentData.id}`)
  }

  const handlePaymentError = (error: string) => {
    console.error("Payment error:", error)
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!booking) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Booking Not Found</h1>
          <p className="text-muted-foreground">The booking you're looking for doesn't exist.</p>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Complete Your Booking</h1>
          <p className="text-muted-foreground">Review your details and proceed with payment</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Booking Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Booking Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Listing Info */}
                <div className="space-y-3">
                  <div className="relative">
                    <Image
                      src={booking.listingImage || "/placeholder.svg?height=200&width=300"}
                      alt={booking.listingName}
                      width={300}
                      height={200}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <Badge className="absolute top-2 left-2 bg-primary text-primary-foreground capitalize">
                      {booking.listingType}
                    </Badge>
                  </div>
                  <div>
                    <h3 className="font-semibold">{booking.listingName}</h3>
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {booking.location}
                    </p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{booking.rating}</span>
                      <span className="text-xs text-muted-foreground">({booking.reviewCount} reviews)</span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Booking Details */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">
                        {format(new Date(booking.checkIn), "MMM dd")} -{" "}
                        {format(new Date(booking.checkOut), "MMM dd, yyyy")}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {Math.ceil(
                          (new Date(booking.checkOut).getTime() - new Date(booking.checkIn).getTime()) /
                            (1000 * 60 * 60 * 24),
                        )}{" "}
                        nights
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{booking.guests} guests</span>
                  </div>
                </div>

                <Separator />

                {/* Price Breakdown */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Base price</span>
                    <span>
                      {booking.currency === "INR" ? "₹" : "$"}
                      {booking.basePrice.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Service fee</span>
                    <span>
                      {booking.currency === "INR" ? "₹" : "$"}
                      {booking.serviceFee.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Taxes</span>
                    <span>
                      {booking.currency === "INR" ? "₹" : "$"}
                      {booking.taxes.toLocaleString()}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span className="text-primary">
                      {booking.currency === "INR" ? "₹" : "$"}
                      {booking.totalAmount.toLocaleString()}
                    </span>
                  </div>
                </div>

                <Separator />

                {/* Policies */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Free cancellation</span>
                  </div>
                  <p className="text-xs text-muted-foreground">{booking.cancellationPolicy}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Section */}
          <div className="lg:col-span-2">
            {!showPayment ? (
              <div className="space-y-6">
                <PaymentMethodSelector
                  onSelect={(method) => {
                    setSelectedPaymentMethod(method)
                    setShowPayment(true)
                  }}
                  selectedMethod={selectedPaymentMethod}
                  amount={booking.totalAmount}
                  currency={booking.currency}
                />
              </div>
            ) : (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold">Payment Details</h2>
                  <Button variant="outline" onClick={() => setShowPayment(false)} className="bg-transparent">
                    Change Method
                  </Button>
                </div>

                {selectedPaymentMethod?.type === "stripe" ? (
                  <StripeCheckout
                    amount={booking.totalAmount}
                    currency={booking.currency}
                    bookingData={booking}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                  />
                ) : (
                  <RazorpayCheckout
                    amount={booking.totalAmount}
                    currency={booking.currency}
                    bookingData={booking}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
