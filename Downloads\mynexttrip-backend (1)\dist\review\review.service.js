"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let ReviewService = class ReviewService {
    constructor(prisma, redis) {
        this.prisma = prisma;
        this.redis = redis;
    }
    async createReview(userId, createReviewDto) {
        const { listingType, listingId, bookingId, rating, title, content, images } = createReviewDto;
        const booking = await this.prisma.booking.findFirst({
            where: {
                id: bookingId,
                userId,
                status: "COMPLETED",
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException("Completed booking not found");
        }
        const existingReview = await this.prisma.review.findFirst({
            where: {
                bookingId,
                userId,
            },
        });
        if (existingReview) {
            throw new common_1.BadRequestException("Review already exists for this booking");
        }
        const listingMatches = (listingType === "hotel" && booking.hotelId === listingId) ||
            (listingType === "guide" && booking.guideId === listingId) ||
            (listingType === "package" && booking.packageId === listingId);
        if (!listingMatches) {
            throw new common_1.BadRequestException("Listing does not match booking");
        }
        const review = await this.prisma.review.create({
            data: {
                userId,
                bookingId,
                ...(listingType === "hotel" && { hotelId: listingId }),
                ...(listingType === "guide" && { guideId: listingId }),
                ...(listingType === "package" && { packageId: listingId }),
                rating,
                title,
                content,
                images: images || [],
                status: client_1.ReviewStatus.PENDING,
            },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                        profilePicture: true,
                    },
                },
                hotel: {
                    select: {
                        name: true,
                    },
                },
                guide: {
                    select: {
                        name: true,
                    },
                },
                package: {
                    select: {
                        name: true,
                    },
                },
            },
        });
        await this.updateListingRating(listingType, listingId);
        return review;
    }
    async getListingReviews(listingId, query) {
        const { page = 1, limit = 10, rating, sortBy = "newest" } = query;
        const skip = (page - 1) * limit;
        const [hotel, guide, packageData] = await Promise.all([
            this.prisma.hotel.findUnique({ where: { id: listingId } }),
            this.prisma.guide.findUnique({ where: { id: listingId } }),
            this.prisma.package.findUnique({ where: { id: listingId } }),
        ]);
        if (!hotel && !guide && !packageData) {
            throw new common_1.NotFoundException("Listing not found");
        }
        const where = {
            status: client_1.ReviewStatus.APPROVED,
            ...(hotel && { hotelId: listingId }),
            ...(guide && { guideId: listingId }),
            ...(packageData && { packageId: listingId }),
        };
        if (rating) {
            where.rating = rating;
        }
        const orderBy = this.getReviewOrderBy(sortBy);
        const [reviews, total, ratingDistribution] = await Promise.all([
            this.prisma.review.findMany({
                where,
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            profilePicture: true,
                        },
                    },
                    _count: {
                        select: {
                            helpfulVotes: true,
                        },
                    },
                },
                orderBy,
                skip,
                take: limit,
            }),
            this.prisma.review.count({ where }),
            this.prisma.review.groupBy({
                by: ["rating"],
                where: {
                    status: client_1.ReviewStatus.APPROVED,
                    ...(hotel && { hotelId: listingId }),
                    ...(guide && { guideId: listingId }),
                    ...(packageData && { packageId: listingId }),
                },
                _count: true,
            }),
        ]);
        const averageRating = hotel?.averageRating || guide?.averageRating || packageData?.averageRating || 0;
        return {
            reviews,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
            summary: {
                averageRating,
                totalReviews: total,
                ratingDistribution,
            },
        };
    }
    async getUserReviews(userId, query) {
        const { page = 1, limit = 10, status } = query;
        const skip = (page - 1) * limit;
        const where = {
            userId,
        };
        if (status) {
            where.status = status;
        }
        const [reviews, total] = await Promise.all([
            this.prisma.review.findMany({
                where,
                include: {
                    hotel: {
                        select: {
                            name: true,
                            city: true,
                            state: true,
                        },
                    },
                    guide: {
                        select: {
                            name: true,
                            city: true,
                            state: true,
                        },
                    },
                    package: {
                        select: {
                            name: true,
                            city: true,
                            state: true,
                        },
                    },
                    _count: {
                        select: {
                            helpfulVotes: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
                skip,
                take: limit,
            }),
            this.prisma.review.count({ where }),
        ]);
        return {
            reviews,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getReviewById(reviewId) {
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                        profilePicture: true,
                    },
                },
                hotel: {
                    select: {
                        name: true,
                        city: true,
                        state: true,
                    },
                },
                guide: {
                    select: {
                        name: true,
                        city: true,
                        state: true,
                    },
                },
                package: {
                    select: {
                        name: true,
                        city: true,
                        state: true,
                    },
                },
                _count: {
                    select: {
                        helpfulVotes: true,
                    },
                },
            },
        });
        if (!review) {
            throw new common_1.NotFoundException("Review not found");
        }
        return review;
    }
    async updateReview(userId, reviewId, updateReviewDto) {
        const review = await this.prisma.review.findFirst({
            where: {
                id: reviewId,
                userId,
            },
        });
        if (!review) {
            throw new common_1.NotFoundException("Review not found");
        }
        if (review.status === client_1.ReviewStatus.APPROVED) {
            throw new common_1.BadRequestException("Cannot edit approved reviews");
        }
        const updatedReview = await this.prisma.review.update({
            where: { id: reviewId },
            data: {
                ...updateReviewDto,
                status: client_1.ReviewStatus.PENDING,
            },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                        profilePicture: true,
                    },
                },
            },
        });
        if (updateReviewDto.rating && updateReviewDto.rating !== review.rating) {
            const listingType = review.hotelId ? "hotel" : review.guideId ? "guide" : "package";
            const listingId = review.hotelId || review.guideId || review.packageId;
            await this.updateListingRating(listingType, listingId);
        }
        return updatedReview;
    }
    async deleteReview(userId, reviewId, userRole) {
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
        });
        if (!review) {
            throw new common_1.NotFoundException("Review not found");
        }
        if (userRole !== "admin" && review.userId !== userId) {
            throw new common_1.ForbiddenException("Access denied");
        }
        await this.prisma.review.delete({
            where: { id: reviewId },
        });
        const listingType = review.hotelId ? "hotel" : review.guideId ? "guide" : "package";
        const listingId = review.hotelId || review.guideId || review.packageId;
        await this.updateListingRating(listingType, listingId);
    }
    async markReviewHelpful(userId, reviewId) {
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
        });
        if (!review) {
            throw new common_1.NotFoundException("Review not found");
        }
        const existingVote = await this.prisma.reviewHelpfulVote.findFirst({
            where: {
                reviewId,
                userId,
            },
        });
        if (existingVote) {
            await this.prisma.reviewHelpfulVote.delete({
                where: { id: existingVote.id },
            });
            return { helpful: false };
        }
        else {
            await this.prisma.reviewHelpfulVote.create({
                data: {
                    reviewId,
                    userId,
                },
            });
            return { helpful: true };
        }
    }
    async reportReview(userId, reviewId, reason) {
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
        });
        if (!review) {
            throw new common_1.NotFoundException("Review not found");
        }
        const existingReport = await this.prisma.reviewReport.findFirst({
            where: {
                reviewId,
                reportedBy: userId,
            },
        });
        if (existingReport) {
            throw new common_1.BadRequestException("Review already reported by you");
        }
        const report = await this.prisma.reviewReport.create({
            data: {
                reviewId,
                reportedBy: userId,
                reason,
                status: "PENDING",
            },
        });
        return report;
    }
    async getPendingReviews(query) {
        const { page = 1, limit = 20 } = query;
        const skip = (page - 1) * limit;
        const [reviews, total] = await Promise.all([
            this.prisma.review.findMany({
                where: {
                    status: client_1.ReviewStatus.PENDING,
                },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                    hotel: {
                        select: {
                            name: true,
                        },
                    },
                    guide: {
                        select: {
                            name: true,
                        },
                    },
                    package: {
                        select: {
                            name: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "asc",
                },
                skip,
                take: limit,
            }),
            this.prisma.review.count({
                where: {
                    status: client_1.ReviewStatus.PENDING,
                },
            }),
        ]);
        return {
            reviews,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async moderateReview(reviewId, moderationDto) {
        const { action, reason } = moderationDto;
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
        });
        if (!review) {
            throw new common_1.NotFoundException("Review not found");
        }
        const updatedReview = await this.prisma.review.update({
            where: { id: reviewId },
            data: {
                status: action === "approve" ? client_1.ReviewStatus.APPROVED : client_1.ReviewStatus.REJECTED,
                moderationReason: reason,
                moderatedAt: new Date(),
            },
        });
        if (action === "approve") {
            const listingType = review.hotelId ? "hotel" : review.guideId ? "guide" : "package";
            const listingId = review.hotelId || review.guideId || review.packageId;
            await this.updateListingRating(listingType, listingId);
        }
        return updatedReview;
    }
    async getReportedReviews(query) {
        const { page = 1, limit = 20 } = query;
        const skip = (page - 1) * limit;
        const [reports, total] = await Promise.all([
            this.prisma.reviewReport.findMany({
                where: {
                    status: "PENDING",
                },
                include: {
                    review: {
                        include: {
                            user: {
                                select: {
                                    firstName: true,
                                    lastName: true,
                                    email: true,
                                },
                            },
                            hotel: {
                                select: {
                                    name: true,
                                },
                            },
                            guide: {
                                select: {
                                    name: true,
                                },
                            },
                            package: {
                                select: {
                                    name: true,
                                },
                            },
                        },
                    },
                    reporter: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "asc",
                },
                skip,
                take: limit,
            }),
            this.prisma.reviewReport.count({
                where: {
                    status: "PENDING",
                },
            }),
        ]);
        return {
            reports,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async updateListingRating(listingType, listingId) {
        const where = {
            status: client_1.ReviewStatus.APPROVED,
            ...(listingType === "hotel" && { hotelId: listingId }),
            ...(listingType === "guide" && { guideId: listingId }),
            ...(listingType === "package" && { packageId: listingId }),
        };
        const result = await this.prisma.review.aggregate({
            where,
            _avg: {
                rating: true,
            },
            _count: true,
        });
        const averageRating = result._avg.rating || 0;
        const totalReviews = result._count;
        if (listingType === "hotel") {
            await this.prisma.hotel.update({
                where: { id: listingId },
                data: {
                    averageRating,
                    totalReviews,
                },
            });
        }
        else if (listingType === "guide") {
            await this.prisma.guide.update({
                where: { id: listingId },
                data: {
                    averageRating,
                    totalReviews,
                },
            });
        }
        else if (listingType === "package") {
            await this.prisma.package.update({
                where: { id: listingId },
                data: {
                    averageRating,
                    totalReviews,
                },
            });
        }
    }
    getReviewOrderBy(sortBy) {
        switch (sortBy) {
            case "oldest":
                return { createdAt: "asc" };
            case "rating_high":
                return { rating: "desc" };
            case "rating_low":
                return { rating: "asc" };
            case "helpful":
                return { helpfulVotes: { _count: "desc" } };
            default:
                return { createdAt: "desc" };
        }
    }
};
exports.ReviewService = ReviewService;
exports.ReviewService = ReviewService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function, Function])
], ReviewService);
//# sourceMappingURL=review.service.js.map