"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Smartphone, Lock, CreditCard, Wallet } from "lucide-react"
import { api } from "@/lib/api"
import { toast } from "sonner"

interface RazorpayCheckoutProps {
  amount: number
  currency: string
  bookingData: any
  onSuccess: (paymentData: any) => void
  onError: (error: string) => void
}

declare global {
  interface Window {
    Razorpay: any
  }
}

export function RazorpayCheckout({ amount, currency, bookingData, onSuccess, onError }: RazorpayCheckoutProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [customerDetails, setCustomerDetails] = useState({
    name: "",
    email: "",
    phone: "",
  })

  const loadRazorpayScript = () => {
    return new Promise((resolve) => {
      const script = document.createElement("script")
      script.src = "https://checkout.razorpay.com/v1/checkout.js"
      script.onload = () => resolve(true)
      script.onerror = () => resolve(false)
      document.body.appendChild(script)
    })
  }

  const handlePayment = async () => {
    if (!customerDetails.name || !customerDetails.email || !customerDetails.phone) {
      toast.error("Please fill in all customer details")
      return
    }

    setIsProcessing(true)

    try {
      // Load Razorpay script
      const scriptLoaded = await loadRazorpayScript()
      if (!scriptLoaded) {
        throw new Error("Failed to load Razorpay SDK")
      }

      // Create order
      const { data } = await api.post("/payments/razorpay/create-order", {
        amount: amount * 100, // Convert to paise
        currency: "INR",
        bookingData,
        customerDetails,
      })

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: data.amount,
        currency: data.currency,
        name: "MyNextTrip",
        description: `Booking Payment - ${bookingData.listingName}`,
        order_id: data.id,
        prefill: {
          name: customerDetails.name,
          email: customerDetails.email,
          contact: customerDetails.phone,
        },
        theme: {
          color: "#8b5cf6",
        },
        handler: async (response: any) => {
          try {
            // Verify payment
            const verificationResponse = await api.post("/payments/razorpay/verify", {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              bookingData,
            })

            onSuccess(verificationResponse.data)
            toast.success("Payment successful!")
          } catch (error: any) {
            onError(error.response?.data?.message || "Payment verification failed")
            toast.error("Payment verification failed")
          }
        },
        modal: {
          ondismiss: () => {
            setIsProcessing(false)
            toast.error("Payment cancelled")
          },
        },
      }

      const razorpay = new window.Razorpay(options)
      razorpay.open()
    } catch (error: any) {
      onError(error.response?.data?.message || "Payment failed")
      toast.error(error.response?.data?.message || "Payment failed")
      setIsProcessing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wallet className="h-5 w-5" />
          Pay with Razorpay
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Customer Details */}
        <div className="space-y-4">
          <h3 className="font-semibold">Customer Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={customerDetails.name}
                onChange={(e) => setCustomerDetails({ ...customerDetails, name: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={customerDetails.email}
                onChange={(e) => setCustomerDetails({ ...customerDetails, email: e.target.value })}
                required
              />
            </div>
          </div>
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              value={customerDetails.phone}
              onChange={(e) => setCustomerDetails({ ...customerDetails, phone: e.target.value })}
              placeholder="+91 98765 43210"
              required
            />
          </div>
        </div>

        <Separator />

        {/* Payment Methods */}
        <div className="space-y-4">
          <h3 className="font-semibold">Available Payment Methods</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex flex-col items-center p-3 border rounded-lg">
              <Smartphone className="h-6 w-6 text-blue-600 mb-2" />
              <span className="text-xs font-medium">UPI</span>
            </div>
            <div className="flex flex-col items-center p-3 border rounded-lg">
              <CreditCard className="h-6 w-6 text-green-600 mb-2" />
              <span className="text-xs font-medium">Cards</span>
            </div>
            <div className="flex flex-col items-center p-3 border rounded-lg">
              <Wallet className="h-6 w-6 text-purple-600 mb-2" />
              <span className="text-xs font-medium">Wallets</span>
            </div>
            <div className="flex flex-col items-center p-3 border rounded-lg">
              <CreditCard className="h-6 w-6 text-orange-600 mb-2" />
              <span className="text-xs font-medium">Net Banking</span>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
          <Lock className="h-4 w-4 text-green-600" />
          <span className="text-sm text-muted-foreground">Secured by Razorpay with bank-level security</span>
        </div>

        {/* Pay Button */}
        <Button onClick={handlePayment} className="w-full" size="lg" disabled={isProcessing}>
          {isProcessing ? "Processing..." : `Pay ₹${amount.toLocaleString()}`}
        </Button>
      </CardContent>
    </Card>
  )
}
