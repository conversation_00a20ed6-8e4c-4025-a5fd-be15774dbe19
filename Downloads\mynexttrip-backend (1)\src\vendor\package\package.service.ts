import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import type { PrismaService } from "../../common/services/prisma.service"
import type { TypesenseService } from "../../common/services/typesense.service"
import type { CreatePackageDto, UpdatePackageDto, PackageQueryDto } from "./dto/package.dto"
import { PackageStatus } from "@prisma/client"
import type { Express } from "express"

@Injectable()
export class PackageService {
  constructor(
    private prisma: PrismaService,
    private typesense: TypesenseService,
  ) {}

  async createPackage(userId: string, createPackageDto: CreatePackageDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const packageData = await this.prisma.package.create({
      data: {
        vendorId: vendor.id,
        name: createPackageDto.name,
        description: createPackageDto.description,
        duration: createPackageDto.duration,
        maxGroupSize: createPackageDto.maxGroupSize,
        price: createPackageDto.price,
        inclusions: createPackageDto.inclusions,
        exclusions: createPackageDto.exclusions,
        itinerary: createPackageDto.itinerary,
        city: createPackageDto.city,
        state: createPackageDto.state,
        difficulty: createPackageDto.difficulty,
        category: createPackageDto.category,
        cancellationPolicy: createPackageDto.cancellationPolicy,
        status: PackageStatus.DRAFT,
      },
      include: {
        images: true,
        vendor: {
          select: {
            businessName: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
      },
    })

    await this.indexPackageInTypesense(packageData)
    return packageData
  }

  async getVendorPackages(userId: string, query: PackageQueryDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const { page = 1, limit = 10, status, city, category, search } = query
    const skip = (page - 1) * limit

    const where: any = {
      vendorId: vendor.id,
    }

    if (status) {
      where.status = status
    }

    if (city) {
      where.city = {
        contains: city,
        mode: "insensitive",
      }
    }

    if (category) {
      where.category = category
    }

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          description: {
            contains: search,
            mode: "insensitive",
          },
        },
      ]
    }

    const [packages, total] = await Promise.all([
      this.prisma.package.findMany({
        where,
        include: {
          images: {
            take: 1,
            orderBy: {
              isPrimary: "desc",
            },
          },
          _count: {
            select: {
              bookings: true,
              reviews: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      this.prisma.package.count({ where }),
    ])

    return {
      packages,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getPackageById(userId: string, packageId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const packageData = await this.prisma.package.findFirst({
      where: {
        id: packageId,
        vendorId: vendor.id,
      },
      include: {
        images: {
          orderBy: {
            isPrimary: "desc",
          },
        },
        reviews: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                profilePicture: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
        _count: {
          select: {
            bookings: true,
            reviews: true,
          },
        },
      },
    })

    if (!packageData) {
      throw new NotFoundException("Package not found")
    }

    return packageData
  }

  async updatePackage(userId: string, packageId: string, updatePackageDto: UpdatePackageDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const packageData = await this.prisma.package.findFirst({
      where: {
        id: packageId,
        vendorId: vendor.id,
      },
    })

    if (!packageData) {
      throw new NotFoundException("Package not found")
    }

    const updatedPackage = await this.prisma.package.update({
      where: { id: packageId },
      data: updatePackageDto,
      include: {
        images: true,
        vendor: {
          select: {
            businessName: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
      },
    })

    await this.indexPackageInTypesense(updatedPackage)
    return updatedPackage
  }

  async deletePackage(userId: string, packageId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const packageData = await this.prisma.package.findFirst({
      where: {
        id: packageId,
        vendorId: vendor.id,
      },
    })

    if (!packageData) {
      throw new NotFoundException("Package not found")
    }

    const activeBookings = await this.prisma.booking.count({
      where: {
        packageId,
        status: {
          in: ["PENDING", "CONFIRMED"],
        },
      },
    })

    if (activeBookings > 0) {
      throw new BadRequestException("Cannot delete package with active bookings")
    }

    await this.prisma.package.delete({
      where: { id: packageId },
    })

    try {
      await this.typesense.client.collections("listings").documents(packageId).delete()
    } catch (error) {
      console.error("Error removing package from Typesense:", error)
    }
  }

  async uploadPackageImages(userId: string, packageId: string, files: Express.Multer.File[]) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const packageData = await this.prisma.package.findFirst({
      where: {
        id: packageId,
        vendorId: vendor.id,
      },
    })

    if (!packageData) {
      throw new NotFoundException("Package not found")
    }

    const images = await Promise.all(
      files.map(async (file, index) => {
        return this.prisma.packageImage.create({
          data: {
            packageId,
            url: `/uploads/packages/${packageId}/${file.filename}`,
            altText: `${packageData.name} - Image ${index + 1}`,
            isPrimary: index === 0,
          },
        })
      }),
    )

    return images
  }

  async updatePackageStatus(userId: string, packageId: string, status: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const packageData = await this.prisma.package.findFirst({
      where: {
        id: packageId,
        vendorId: vendor.id,
      },
    })

    if (!packageData) {
      throw new NotFoundException("Package not found")
    }

    const updatedPackage = await this.prisma.package.update({
      where: { id: packageId },
      data: { status: status as PackageStatus },
      include: {
        images: true,
      },
    })

    await this.indexPackageInTypesense(updatedPackage)
    return updatedPackage
  }

  private async indexPackageInTypesense(packageData: any) {
    try {
      const document = {
        id: packageData.id,
        name: packageData.name,
        description: packageData.description,
        city: packageData.city,
        state: packageData.state,
        duration: packageData.duration,
        maxGroupSize: packageData.maxGroupSize,
        price: packageData.price,
        difficulty: packageData.difficulty,
        category: packageData.category,
        averageRating: packageData.averageRating || 0,
        totalReviews: packageData.totalReviews || 0,
        status: packageData.status,
        type: "package",
      }

      await this.typesense.client.collections("listings").documents().upsert(document)
    } catch (error) {
      console.error("Error indexing package in Typesense:", error)
    }
  }
}
