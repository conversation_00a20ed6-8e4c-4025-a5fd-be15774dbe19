import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { StripeService } from "./services/stripe.service";
import type { RazorpayService } from "./services/razorpay.service";
import type { CreatePaymentDto, RefundPaymentDto, PaymentQueryDto } from "./dto/payment.dto";
export declare class PaymentService {
    private prisma;
    private redis;
    private stripeService;
    private razorpayService;
    constructor(prisma: PrismaService, redis: RedisService, stripeService: StripeService, razorpayService: RazorpayService);
    createPaymentIntent(userId: string, createPaymentDto: CreatePaymentDto): Promise<{
        paymentId: any;
        paymentReference: any;
        clientSecret: any;
        amount: any;
        currency: string;
        gateway: PaymentGateway;
        booking: {
            id: any;
            reference: any;
            checkIn: any;
            checkOut: any;
            guests: any;
            listing: {
                name: any;
                type: string;
                location: string;
            };
        };
    }>;
    processPayment(userId: string, paymentIntentId: string, paymentMethodId?: string): Promise<any>;
    getUserPayments(userId: string, userRole: string, query: PaymentQueryDto): Promise<{
        payments: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getPaymentById(userId: string, paymentId: string, userRole: string): Promise<any>;
    processRefund(userId: string, paymentId: string, refundDto: RefundPaymentDto, userRole: string): Promise<any>;
    getSavedPaymentMethods(userId: string): Promise<any[]>;
    savePaymentMethod(userId: string, paymentMethodId: string, isDefault?: boolean): Promise<{
        success: boolean;
        paymentMethodId: string;
        isDefault: boolean;
    }>;
    setDefaultPaymentMethod(userId: string, paymentMethodId: string): Promise<{
        success: boolean;
    }>;
    getVendorRevenueAnalytics(userId: string, startDate?: string, endDate?: string): Promise<{
        totalRevenue: any;
        vendorRevenue: number;
        platformFees: any;
        totalPayments: any;
        monthlyRevenue: any;
        recentPayments: any;
    }>;
    private createVendorPayout;
    private generatePaymentReference;
}
