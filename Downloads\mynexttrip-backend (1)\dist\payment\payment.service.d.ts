import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { StripeService } from "./services/stripe.service";
import type { RazorpayService } from "./services/razorpay.service";
import type { CreatePaymentDto, RefundPaymentDto, PaymentQueryDto } from "./dto/payment.dto";
export declare class PaymentService {
    private prisma;
    private redis;
    private stripeService;
    private razorpayService;
    constructor(prisma: PrismaService, redis: RedisService, stripeService: StripeService, razorpayService: RazorpayService);
    createPaymentIntent(userId: string, createPaymentDto: CreatePaymentDto): Promise<{
        paymentId: string;
        paymentReference: any;
        clientSecret: any;
        amount: number;
        currency: string;
        gateway: PaymentGateway;
        booking: {
            id: string;
            reference: any;
            checkIn: any;
            checkOut: any;
            guests: number;
            listing: {
                name: any;
                type: string;
                location: string;
            };
        };
    }>;
    processPayment(userId: string, paymentIntentId: string, paymentMethodId?: string): Promise<{
        id: string;
        amount: number;
        currency: string;
        method: import(".prisma/client").$Enums.PaymentMethod;
        status: import(".prisma/client").$Enums.PaymentStatus;
        gatewayId: string | null;
        gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
        refundAmount: number | null;
        refundReason: string | null;
        refundedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        bookingId: string;
    }>;
    getUserPayments(userId: string, userRole: string, query: PaymentQueryDto): Promise<{
        payments: {
            id: string;
            amount: number;
            currency: string;
            method: import(".prisma/client").$Enums.PaymentMethod;
            status: import(".prisma/client").$Enums.PaymentStatus;
            gatewayId: string | null;
            gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
            refundAmount: number | null;
            refundReason: string | null;
            refundedAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
            bookingId: string;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getPaymentById(userId: string, paymentId: string, userRole: string): Promise<{
        id: string;
        amount: number;
        currency: string;
        method: import(".prisma/client").$Enums.PaymentMethod;
        status: import(".prisma/client").$Enums.PaymentStatus;
        gatewayId: string | null;
        gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
        refundAmount: number | null;
        refundReason: string | null;
        refundedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        bookingId: string;
    }>;
    processRefund(userId: string, paymentId: string, refundDto: RefundPaymentDto, userRole: string): Promise<any>;
    getSavedPaymentMethods(userId: string): Promise<any[]>;
    savePaymentMethod(userId: string, paymentMethodId: string, isDefault?: boolean): Promise<{
        success: boolean;
        paymentMethodId: string;
        isDefault: boolean;
    }>;
    setDefaultPaymentMethod(userId: string, paymentMethodId: string): Promise<{
        success: boolean;
    }>;
    getVendorRevenueAnalytics(userId: string, startDate?: string, endDate?: string): Promise<{
        totalRevenue: number;
        vendorRevenue: number;
        platformFees: any;
        totalPayments: number;
        monthlyRevenue: unknown;
        recentPayments: {
            id: string;
            amount: number;
            currency: string;
            method: import(".prisma/client").$Enums.PaymentMethod;
            status: import(".prisma/client").$Enums.PaymentStatus;
            gatewayId: string | null;
            gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
            refundAmount: number | null;
            refundReason: string | null;
            refundedAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
            bookingId: string;
        }[];
    }>;
    private createVendorPayout;
    private generatePaymentReference;
}
