import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
  UseInterceptors,
  UploadedFiles,
} from "@nestjs/common"
import { FilesInterceptor } from "@nestjs/platform-express"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from "@nestjs/swagger"
import { JwtAuthGuard } from "../../common/guards/jwt-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles } from "../../common/decorators/roles.decorator"
import type { HotelService } from "./hotel.service"
import type { CreateHotelDto, UpdateHotelDto, HotelQueryDto } from "./dto/hotel.dto"
import { ApiResponseDto } from "../../common/dto/api-response.dto"
import type { Express } from "express"

@ApiTags("Hotel Management")
@Controller("vendor/hotels")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles("vendor")
@ApiBearerAuth()
export class HotelController {
  constructor(private readonly hotelService: HotelService) {}

  @Post()
  @ApiOperation({ summary: "Create new hotel listing" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Hotel created successfully" })
  async createHotel(req, @Body() createHotelDto: CreateHotelDto) {
    const hotel = await this.hotelService.createHotel(req.user.id, createHotelDto)
    return new ApiResponseDto(true, "Hotel created successfully", hotel)
  }

  @Get()
  @ApiOperation({ summary: "Get vendor hotels" })
  async getHotels(req, @Query() query: HotelQueryDto) {
    const hotels = await this.hotelService.getVendorHotels(req.user.id, query)
    return new ApiResponseDto(true, "Hotels retrieved successfully", hotels)
  }

  @Get(":id")
  @ApiOperation({ summary: "Get hotel by ID" })
  async getHotel(req, @Param('id', ParseUUIDPipe) id: string) {
    const hotel = await this.hotelService.getHotelById(req.user.id, id)
    return new ApiResponseDto(true, "Hotel retrieved successfully", hotel)
  }

  @Put(":id")
  @ApiOperation({ summary: "Update hotel" })
  async updateHotel(req, @Param('id', ParseUUIDPipe) id: string, @Body() updateHotelDto: UpdateHotelDto) {
    const hotel = await this.hotelService.updateHotel(req.user.id, id, updateHotelDto)
    return new ApiResponseDto(true, "Hotel updated successfully", hotel)
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete hotel" })
  async deleteHotel(req, @Param('id', ParseUUIDPipe) id: string) {
    await this.hotelService.deleteHotel(req.user.id, id)
    return new ApiResponseDto(true, "Hotel deleted successfully")
  }

  @Post(":id/images")
  @UseInterceptors(FilesInterceptor("images", 10))
  @ApiConsumes("multipart/form-data")
  @ApiOperation({ summary: "Upload hotel images" })
  async uploadImages(req, @Param('id', ParseUUIDPipe) id: string, @UploadedFiles() files: Express.Multer.File[]) {
    const images = await this.hotelService.uploadHotelImages(req.user.id, id, files)
    return new ApiResponseDto(true, "Images uploaded successfully", images)
  }

  @Delete(":id/images/:imageId")
  @ApiOperation({ summary: "Delete hotel image" })
  async deleteImage(req, @Param('id', ParseUUIDPipe) id: string, @Param('imageId', ParseUUIDPipe) imageId: string) {
    await this.hotelService.deleteHotelImage(req.user.id, id, imageId)
    return new ApiResponseDto(true, "Image deleted successfully")
  }

  @Put(":id/status")
  @ApiOperation({ summary: "Update hotel status" })
  async updateStatus(req, @Param('id', ParseUUIDPipe) id: string, @Body() body: { status: string }) {
    const hotel = await this.hotelService.updateHotelStatus(req.user.id, id, body.status)
    return new ApiResponseDto(true, "Hotel status updated successfully", hotel)
  }
}
