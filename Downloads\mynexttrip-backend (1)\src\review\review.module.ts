import { <PERSON>du<PERSON> } from "@nestjs/common"
import { ReviewController } from "./review.controller"
import { ReviewService } from "./review.service"
import { PrismaModule } from "../common/modules/prisma.module"
import { RedisModule } from "../common/modules/redis.module"

@Module({
  imports: [PrismaModule, RedisModule],
  controllers: [ReviewController],
  providers: [ReviewService],
  exports: [ReviewService],
})
export class ReviewModule {}
