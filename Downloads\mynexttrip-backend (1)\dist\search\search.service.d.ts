import type { PrismaService } from "../common/services/prisma.service";
import type { TypesenseService } from "../common/services/typesense.service";
import type { RedisService } from "../common/services/redis.service";
import type { SearchQueryDto } from "./dto/search.dto";
export declare class SearchService {
    private prisma;
    private typesense;
    private redis;
    constructor(prisma: PrismaService, typesense: TypesenseService, redis: RedisService);
    search(query: SearchQueryDto): Promise<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>;
    searchHotels(query: SearchQueryDto): Promise<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>;
    searchGuides(query: SearchQueryDto): Promise<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>;
    searchPackages(query: SearchQueryDto): Promise<{
        results: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        facets: any[];
        searchTime: number;
    }>;
    getSuggestions(searchQuery: string): Promise<any>;
    searchLocations(searchQuery: string): Promise<any>;
    getAvailableFilters(type?: string, city?: string): Promise<any>;
    getPopularListings(): Promise<any>;
    private buildFilterString;
    private buildSortString;
    private getDetailedListings;
    private filterByAvailability;
    private fallbackDatabaseSearch;
}
