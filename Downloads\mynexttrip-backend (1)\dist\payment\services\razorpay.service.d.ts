export declare class RazorpayService {
    private razorpay;
    constructor();
    createOrder(params: {
        amount: number;
        currency: string;
        receipt: string;
        notes?: Record<string, string>;
    }): Promise<import("razorpay/dist/types/orders").Orders.RazorpayOrder>;
    getOrder(orderId: string): Promise<import("razorpay/dist/types/orders").Orders.RazorpayOrder>;
    createRefund(params: {
        paymentId: string;
        amount: number;
        notes?: Record<string, string>;
    }): Promise<import("razorpay/dist/types/refunds").Refunds.RazorpayRefund>;
    verifyWebhookSignature(payload: string, signature: string): boolean;
    getPayment(paymentId: string): Promise<import("razorpay/dist/types/payments").Payments.RazorpayPayment>;
}
