import Stripe from "stripe";
export declare class StripeService {
    private stripe;
    constructor();
    createPaymentIntent(params: Stripe.PaymentIntentCreateParams): Promise<Stripe.Response<Stripe.PaymentIntent>>;
    confirmPaymentIntent(paymentIntentId: string, paymentMethodId?: string): Promise<Stripe.Response<Stripe.PaymentIntent>>;
    createCustomer(params: Stripe.CustomerCreateParams): Promise<Stripe.Response<Stripe.Customer>>;
    getCustomerPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]>;
    attachPaymentMethodToCustomer(paymentMethodId: string, customerId: string): Promise<Stripe.Response<Stripe.PaymentMethod>>;
    setDefaultPaymentMethod(customerId: string, paymentMethodId: string): Promise<Stripe.Response<Stripe.Customer>>;
    createRefund(params: Stripe.RefundCreateParams): Promise<Stripe.Response<Stripe.Refund>>;
    constructWebhookEvent(payload: string | Buffer, signature: string): Promise<Stripe.Event>;
    retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.Response<Stripe.PaymentIntent>>;
}
