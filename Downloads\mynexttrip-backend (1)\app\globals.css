@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* Pure white */
  --foreground: oklch(0.205 0 0); /* Dark gray #1f2937 */
  --card: oklch(0.97 0.005 264); /* Light gray #f1f5f9 */
  --card-foreground: oklch(0.205 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.205 0 0);
  --primary: oklch(0.627 0.265 303.9); /* Purple #8b5cf6 */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.696 0.17 162.48); /* Orange #f97316 */
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.556 0 0); /* Gray #6b7280 */
  --muted-foreground: oklch(1 0 0);
  --accent: oklch(0.627 0.265 303.9); /* Purple accent */
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.577 0.245 27.325); /* Red #be123c */
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.922 0 0); /* Light border #e5e7eb */
  --input: oklch(0.97 0.005 264); /* Input background */
  --ring: oklch(0.627 0.265 303.9 / 0.5); /* Purple focus ring */
  --chart-1: oklch(0.627 0.265 303.9);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.696 0.17 162.48);
  --chart-4: oklch(0.577 0.245 27.325);
  --chart-5: oklch(0.696 0.17 162.48);
  --radius: 0.5rem;
  --sidebar: oklch(0.97 0.005 264);
  --sidebar-foreground: oklch(0.205 0 0);
  --sidebar-primary: oklch(0.627 0.265 303.9);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.696 0.17 162.48);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.627 0.265 303.9 / 0.5);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.627 0.265 303.9);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.627 0.265 303.9);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.627 0.265 303.9 / 0.5);
  --chart-1: oklch(0.627 0.265 303.9);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.627 0.265 303.9);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.627 0.265 303.9 / 0.5);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  /* Added custom styles for MyNextTrip luxury aesthetic */
  .hero-gradient {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(249, 115, 22, 0.1) 100%);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .luxury-shadow {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}
