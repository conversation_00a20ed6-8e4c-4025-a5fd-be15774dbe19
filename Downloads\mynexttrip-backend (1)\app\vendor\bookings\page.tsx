"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Calendar, User, Phone, Mail, MapPin, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { api } from "@/lib/api"
import { MainLayout } from "@/components/layout/main-layout"
import { format } from "date-fns"

interface Booking {
  id: string
  bookingNumber: string
  guestName: string
  guestEmail: string
  guestPhone: string
  listingName: string
  listingType: "hotel" | "guide" | "package"
  checkIn: string
  checkOut: string
  guests: number
  amount: number
  commission: number
  netAmount: number
  status: "pending" | "confirmed" | "cancelled" | "completed" | "in-progress"
  paymentStatus: "pending" | "paid" | "refunded"
  createdAt: string
  specialRequests?: string
}

export default function VendorBookings() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateRange, setDateRange] = useState("all")

  const { data: bookings, isLoading } = useQuery({
    queryKey: ["vendor-bookings", searchQuery, statusFilter, dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        search: searchQuery,
        ...(statusFilter !== "all" && { status: statusFilter }),
        ...(dateRange !== "all" && { dateRange }),
      })
      const response = await api.get(`/vendor/bookings?${params}`)
      return response.data as Booking[]
    },
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-100 text-green-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      case "completed":
        return "bg-blue-100 text-blue-800"
      case "in-progress":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "confirmed":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "cancelled":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "pending":
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case "in-progress":
        return <Clock className="h-4 w-4 text-purple-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const handleStatusUpdate = async (bookingId: string, newStatus: string) => {
    try {
      await api.patch(`/vendor/bookings/${bookingId}/status`, { status: newStatus })
      // Refetch bookings
    } catch (error) {
      console.error("Failed to update booking status:", error)
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Booking Management</h1>
          <p className="text-muted-foreground">View and manage all your bookings</p>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by guest name or booking number..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Bookings</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Bookings List */}
        <div className="space-y-4">
          {bookings?.map((booking) => (
            <Card key={booking.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-lg">{booking.bookingNumber}</h3>
                      <Badge className={getStatusColor(booking.status)}>
                        {getStatusIcon(booking.status)}
                        <span className="ml-1 capitalize">{booking.status}</span>
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Booked on {format(new Date(booking.createdAt), "MMM dd, yyyy")}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-primary">₹{booking.amount.toLocaleString()}</p>
                    <p className="text-sm text-muted-foreground">Net: ₹{booking.netAmount.toLocaleString()}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Guest Information */}
                  <div className="space-y-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Guest Information
                    </h4>
                    <div className="space-y-2 text-sm">
                      <p className="font-medium">{booking.guestName}</p>
                      <p className="flex items-center gap-2 text-muted-foreground">
                        <Mail className="h-3 w-3" />
                        {booking.guestEmail}
                      </p>
                      <p className="flex items-center gap-2 text-muted-foreground">
                        <Phone className="h-3 w-3" />
                        {booking.guestPhone}
                      </p>
                    </div>
                  </div>

                  {/* Booking Details */}
                  <div className="space-y-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Booking Details
                    </h4>
                    <div className="space-y-2 text-sm">
                      <p className="font-medium">{booking.listingName}</p>
                      <p className="text-muted-foreground capitalize">{booking.listingType}</p>
                      <p className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {format(new Date(booking.checkIn), "MMM dd")} -{" "}
                        {format(new Date(booking.checkOut), "MMM dd, yyyy")}
                      </p>
                      <p className="text-muted-foreground">{booking.guests} guests</p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Actions</h4>
                    <div className="space-y-2">
                      {booking.status === "pending" && (
                        <>
                          <Button
                            size="sm"
                            className="w-full"
                            onClick={() => handleStatusUpdate(booking.id, "confirmed")}
                          >
                            Confirm Booking
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full bg-transparent"
                            onClick={() => handleStatusUpdate(booking.id, "cancelled")}
                          >
                            Cancel Booking
                          </Button>
                        </>
                      )}
                      {booking.status === "confirmed" && (
                        <Button
                          size="sm"
                          className="w-full"
                          onClick={() => handleStatusUpdate(booking.id, "in-progress")}
                        >
                          Mark In Progress
                        </Button>
                      )}
                      {booking.status === "in-progress" && (
                        <Button
                          size="sm"
                          className="w-full"
                          onClick={() => handleStatusUpdate(booking.id, "completed")}
                        >
                          Mark Completed
                        </Button>
                      )}
                      <Button size="sm" variant="outline" className="w-full bg-transparent">
                        Contact Guest
                      </Button>
                    </div>
                  </div>
                </div>

                {booking.specialRequests && (
                  <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                    <h5 className="font-medium text-sm mb-1">Special Requests</h5>
                    <p className="text-sm text-muted-foreground">{booking.specialRequests}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {bookings?.length === 0 && !isLoading && (
          <Card>
            <CardContent className="py-12 text-center">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-semibold text-lg mb-2">No bookings found</h3>
              <p className="text-muted-foreground">
                {searchQuery || statusFilter !== "all" || dateRange !== "all"
                  ? "Try adjusting your filters to see more results."
                  : "Your bookings will appear here once guests start making reservations."}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
