import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import type { PrismaService } from "../common/services/prisma.service"
import type { CreateVendorDto, UpdateVendorDto, VendorQueryDto } from "./dto/vendor.dto"
import { VendorStatus } from "@prisma/client"

@Injectable()
export class VendorService {
  constructor(private prisma: PrismaService) {}

  async registerVendor(userId: string, createVendorDto: CreateVendorDto) {
    // Check if user is already a vendor
    const existingVendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (existingVendor) {
      throw new BadRequestException("User is already registered as a vendor")
    }

    // Create vendor profile
    const vendor = await this.prisma.vendor.create({
      data: {
        userId,
        businessName: createVendorDto.businessName,
        businessType: createVendorDto.businessType,
        description: createVendorDto.description,
        contactPhone: createVendorDto.contactPhone,
        contactEmail: createVendorDto.contactEmail,
        address: createVendorDto.address,
        city: createVendorDto.city,
        state: createVendorDto.state,
        pincode: createVendorDto.pincode,
        gstNumber: createVendorDto.gstNumber,
        panNumber: createVendorDto.panNumber,
        bankAccountNumber: createVendorDto.bankAccountNumber,
        bankIfscCode: createVendorDto.bankIfscCode,
        bankAccountHolderName: createVendorDto.bankAccountHolderName,
        status: VendorStatus.PENDING,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    })

    // Update user role to vendor
    await this.prisma.user.update({
      where: { id: userId },
      data: { role: "vendor" },
    })

    return vendor
  }

  async getVendorProfile(userId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            profilePicture: true,
          },
        },
        _count: {
          select: {
            hotels: true,
            guides: true,
            packages: true,
          },
        },
      },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    return vendor
  }

  async updateVendorProfile(userId: string, updateVendorDto: UpdateVendorDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    return this.prisma.vendor.update({
      where: { userId },
      data: updateVendorDto,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    })
  }

  async getDashboardStats(userId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const [totalListings, totalBookings, totalEarnings, pendingBookings, recentBookings] = await Promise.all([
      // Total listings count
      this.prisma
        .$transaction([
          this.prisma.hotel.count({ where: { vendorId: vendor.id } }),
          this.prisma.guide.count({ where: { vendorId: vendor.id } }),
          this.prisma.package.count({ where: { vendorId: vendor.id } }),
        ])
        .then(([hotels, guides, packages]) => hotels + guides + packages),

      // Total bookings
      this.prisma.booking.count({
        where: {
          OR: [
            { hotel: { vendorId: vendor.id } },
            { guide: { vendorId: vendor.id } },
            { package: { vendorId: vendor.id } },
          ],
        },
      }),

      // Total earnings
      this.prisma.booking.aggregate({
        where: {
          OR: [
            { hotel: { vendorId: vendor.id } },
            { guide: { vendorId: vendor.id } },
            { package: { vendorId: vendor.id } },
          ],
          status: "CONFIRMED",
        },
        _sum: {
          totalAmount: true,
        },
      }),

      // Pending bookings
      this.prisma.booking.count({
        where: {
          OR: [
            { hotel: { vendorId: vendor.id } },
            { guide: { vendorId: vendor.id } },
            { package: { vendorId: vendor.id } },
          ],
          status: "PENDING",
        },
      }),

      // Recent bookings
      this.prisma.booking.findMany({
        where: {
          OR: [
            { hotel: { vendorId: vendor.id } },
            { guide: { vendorId: vendor.id } },
            { package: { vendorId: vendor.id } },
          ],
        },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          hotel: {
            select: {
              name: true,
            },
          },
          guide: {
            select: {
              name: true,
            },
          },
          package: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 10,
      }),
    ])

    return {
      totalListings,
      totalBookings,
      totalEarnings: totalEarnings._sum.totalAmount || 0,
      pendingBookings,
      recentBookings,
    }
  }

  async getVendorBookings(userId: string, query: VendorQueryDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const { page = 1, limit = 10, status, startDate, endDate } = query
    const skip = (page - 1) * limit

    const where: any = {
      OR: [
        { hotel: { vendorId: vendor.id } },
        { guide: { vendorId: vendor.id } },
        { package: { vendorId: vendor.id } },
      ],
    }

    if (status) {
      where.status = status
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const [bookings, total] = await Promise.all([
      this.prisma.booking.findMany({
        where,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
            },
          },
          hotel: {
            select: {
              name: true,
              city: true,
            },
          },
          guide: {
            select: {
              name: true,
              city: true,
            },
          },
          package: {
            select: {
              name: true,
              city: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      this.prisma.booking.count({ where }),
    ])

    return {
      bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getVendorEarnings(userId: string, query: VendorQueryDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const { startDate, endDate } = query

    const where: any = {
      OR: [
        { hotel: { vendorId: vendor.id } },
        { guide: { vendorId: vendor.id } },
        { package: { vendorId: vendor.id } },
      ],
      status: "CONFIRMED",
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const earnings = await this.prisma.booking.aggregate({
      where,
      _sum: {
        totalAmount: true,
      },
      _count: {
        id: true,
      },
    })

    // Get monthly earnings for chart
    const monthlyEarnings = await this.prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', "createdAt") as month,
        SUM("totalAmount") as earnings,
        COUNT(*) as bookings
      FROM "Booking" b
      WHERE (
        EXISTS (SELECT 1 FROM "Hotel" h WHERE h.id = b."hotelId" AND h."vendorId" = ${vendor.id}) OR
        EXISTS (SELECT 1 FROM "Guide" g WHERE g.id = b."guideId" AND g."vendorId" = ${vendor.id}) OR
        EXISTS (SELECT 1 FROM "Package" p WHERE p.id = b."packageId" AND p."vendorId" = ${vendor.id})
      )
      AND b.status = 'CONFIRMED'
      ${startDate ? `AND b."createdAt" >= ${startDate}` : ""}
      ${endDate ? `AND b."createdAt" <= ${endDate}` : ""}
      GROUP BY DATE_TRUNC('month', "createdAt")
      ORDER BY month DESC
      LIMIT 12
    `

    return {
      totalEarnings: earnings._sum.totalAmount || 0,
      totalBookings: earnings._count.id,
      monthlyEarnings,
    }
  }
}
