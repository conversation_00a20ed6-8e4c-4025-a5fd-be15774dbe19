import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsNumber, IsArray, IsEnum, Length, Min, Max, IsInt, IsJSON } from "class-validator"
import { Type } from "class-transformer"
import { PackageStatus, PackageCategory, DifficultyLevel } from "@prisma/client"

export class CreatePackageDto {
  @ApiProperty({ description: "Package name" })
  @IsString()
  @Length(2, 100)
  name: string

  @ApiPropertyOptional({ description: "Package description" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  description?: string

  @ApiProperty({ description: "Duration in days" })
  @IsInt()
  @Min(1)
  @Max(30)
  duration: number

  @ApiProperty({ description: "Maximum group size" })
  @IsInt()
  @Min(1)
  @Max(50)
  maxGroupSize: number

  @ApiProperty({ description: "Package price" })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price: number

  @ApiProperty({ description: "What's included", type: [String] })
  @IsArray()
  @IsString({ each: true })
  inclusions: string[]

  @ApiPropertyOptional({ description: "What's excluded", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  exclusions?: string[]

  @ApiPropertyOptional({ description: "Day-wise itinerary (JSON)" })
  @IsOptional()
  @IsJSON()
  itinerary?: any

  @ApiProperty({ description: "City" })
  @IsString()
  @Length(2, 50)
  city: string

  @ApiProperty({ description: "State" })
  @IsString()
  @Length(2, 50)
  state: string

  @ApiProperty({ enum: DifficultyLevel, description: "Difficulty level" })
  @IsEnum(DifficultyLevel)
  difficulty: DifficultyLevel

  @ApiProperty({ enum: PackageCategory, description: "Package category" })
  @IsEnum(PackageCategory)
  category: PackageCategory

  @ApiPropertyOptional({ description: "Cancellation policy" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  cancellationPolicy?: string
}

export class UpdatePackageDto {
  @ApiPropertyOptional({ description: "Package name" })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  name?: string

  @ApiPropertyOptional({ description: "Package description" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  description?: string

  @ApiPropertyOptional({ description: "Duration in days" })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(30)
  duration?: number

  @ApiPropertyOptional({ description: "Maximum group size" })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  maxGroupSize?: number

  @ApiPropertyOptional({ description: "Package price" })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price?: number

  @ApiPropertyOptional({ description: "What's included", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  inclusions?: string[]

  @ApiPropertyOptional({ description: "What's excluded", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  exclusions?: string[]

  @ApiPropertyOptional({ description: "Day-wise itinerary (JSON)" })
  @IsOptional()
  @IsJSON()
  itinerary?: any

  @ApiPropertyOptional({ description: "City" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  city?: string

  @ApiPropertyOptional({ description: "State" })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  state?: string

  @ApiPropertyOptional({ enum: DifficultyLevel, description: "Difficulty level" })
  @IsOptional()
  @IsEnum(DifficultyLevel)
  difficulty?: DifficultyLevel

  @ApiPropertyOptional({ enum: PackageCategory, description: "Package category" })
  @IsOptional()
  @IsEnum(PackageCategory)
  category?: PackageCategory

  @ApiPropertyOptional({ description: "Cancellation policy" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  cancellationPolicy?: string
}

export class PackageQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10

  @ApiPropertyOptional({ enum: PackageStatus, description: "Filter by status" })
  @IsOptional()
  @IsEnum(PackageStatus)
  status?: PackageStatus

  @ApiPropertyOptional({ description: "Filter by city" })
  @IsOptional()
  @IsString()
  city?: string

  @ApiPropertyOptional({ enum: PackageCategory, description: "Filter by category" })
  @IsOptional()
  @IsEnum(PackageCategory)
  category?: PackageCategory

  @ApiPropertyOptional({ description: "Search query" })
  @IsOptional()
  @IsString()
  search?: string
}
