import { BusinessType } from "@prisma/client";
export declare class CreateVendorDto {
    businessName: string;
    businessType: BusinessType;
    description?: string;
    contactPhone: string;
    contactEmail: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
    gstNumber?: string;
    panNumber?: string;
    bankAccountNumber: string;
    bankIfscCode: string;
    bankAccountHolderName: string;
}
export declare class UpdateVendorDto {
    businessName?: string;
    description?: string;
    contactPhone?: string;
    contactEmail?: string;
    address?: string;
    city?: string;
    state?: string;
    pincode?: string;
    gstNumber?: string;
    panNumber?: string;
    bankAccountNumber?: string;
    bankIfscCode?: string;
    bankAccountHolderName?: string;
}
export declare class VendorQueryDto {
    page?: number;
    limit?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
}
