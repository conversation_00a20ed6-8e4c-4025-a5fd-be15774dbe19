"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageQueryDto = exports.UpdatePackageDto = exports.CreatePackageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
class CreatePackageDto {
}
exports.CreatePackageDto = CreatePackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Package name" }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 100),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Package description" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 2000),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Duration in days" }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(30),
    __metadata("design:type", Number)
], CreatePackageDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Maximum group size" }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], CreatePackageDto.prototype, "maxGroupSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Package price" }),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreatePackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "What's included", type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePackageDto.prototype, "inclusions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "What's excluded", type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePackageDto.prototype, "exclusions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Day-wise itinerary (JSON)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], CreatePackageDto.prototype, "itinerary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "City" }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 50),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "State" }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 50),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.DifficultyLevel, description: "Difficulty level" }),
    (0, class_validator_1.IsEnum)(client_1.DifficultyLevel),
    __metadata("design:type", typeof (_a = typeof client_1.DifficultyLevel !== "undefined" && client_1.DifficultyLevel) === "function" ? _a : Object)
], CreatePackageDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.PackageCategory, description: "Package category" }),
    (0, class_validator_1.IsEnum)(client_1.PackageCategory),
    __metadata("design:type", typeof (_b = typeof client_1.PackageCategory !== "undefined" && client_1.PackageCategory) === "function" ? _b : Object)
], CreatePackageDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cancellation policy" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 1000),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "cancellationPolicy", void 0);
class UpdatePackageDto {
}
exports.UpdatePackageDto = UpdatePackageDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Package name" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 100),
    __metadata("design:type", String)
], UpdatePackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Package description" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 2000),
    __metadata("design:type", String)
], UpdatePackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Duration in days" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(30),
    __metadata("design:type", Number)
], UpdatePackageDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Maximum group size" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], UpdatePackageDto.prototype, "maxGroupSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Package price" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdatePackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "What's included", type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UpdatePackageDto.prototype, "inclusions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "What's excluded", type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UpdatePackageDto.prototype, "exclusions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Day-wise itinerary (JSON)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], UpdatePackageDto.prototype, "itinerary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "City" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 50),
    __metadata("design:type", String)
], UpdatePackageDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "State" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 50),
    __metadata("design:type", String)
], UpdatePackageDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: client_1.DifficultyLevel, description: "Difficulty level" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.DifficultyLevel),
    __metadata("design:type", typeof (_c = typeof client_1.DifficultyLevel !== "undefined" && client_1.DifficultyLevel) === "function" ? _c : Object)
], UpdatePackageDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: client_1.PackageCategory, description: "Package category" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.PackageCategory),
    __metadata("design:type", typeof (_d = typeof client_1.PackageCategory !== "undefined" && client_1.PackageCategory) === "function" ? _d : Object)
], UpdatePackageDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cancellation policy" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 1000),
    __metadata("design:type", String)
], UpdatePackageDto.prototype, "cancellationPolicy", void 0);
class PackageQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.PackageQueryDto = PackageQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Page number", default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PackageQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Items per page", default: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], PackageQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: client_1.PackageStatus, description: "Filter by status" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.PackageStatus),
    __metadata("design:type", typeof (_e = typeof client_1.PackageStatus !== "undefined" && client_1.PackageStatus) === "function" ? _e : Object)
], PackageQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Filter by city" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PackageQueryDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: client_1.PackageCategory, description: "Filter by category" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.PackageCategory),
    __metadata("design:type", typeof (_f = typeof client_1.PackageCategory !== "undefined" && client_1.PackageCategory) === "function" ? _f : Object)
], PackageQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Search query" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PackageQueryDto.prototype, "search", void 0);
//# sourceMappingURL=package.dto.js.map