"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../common/dto/api-response.dto");
let SearchController = class SearchController {
    constructor(searchService) {
        this.searchService = searchService;
    }
    async search(query) {
        const results = await this.searchService.search(query);
        return new api_response_dto_1.ApiResponseDto(true, "Search results retrieved successfully", results);
    }
    async searchHotels(query) {
        const results = await this.searchService.searchHotels(query);
        return new api_response_dto_1.ApiResponseDto(true, "Hotel search results retrieved", results);
    }
    async searchGuides(query) {
        const results = await this.searchService.searchGuides(query);
        return new api_response_dto_1.ApiResponseDto(true, "Guide search results retrieved", results);
    }
    async searchPackages(query) {
        const results = await this.searchService.searchPackages(query);
        return new api_response_dto_1.ApiResponseDto(true, "Package search results retrieved", results);
    }
    async getSuggestions(query) {
        const suggestions = await this.searchService.getSuggestions(query.q);
        return new api_response_dto_1.ApiResponseDto(true, "Suggestions retrieved successfully", suggestions);
    }
    async searchLocations(query) {
        const locations = await this.searchService.searchLocations(query.q);
        return new api_response_dto_1.ApiResponseDto(true, "Locations retrieved successfully", locations);
    }
    async getFilters(query) {
        const filters = await this.searchService.getAvailableFilters(query.type, query.city);
        return new api_response_dto_1.ApiResponseDto(true, "Filters retrieved successfully", filters);
    }
    async getPopular() {
        const popular = await this.searchService.getPopularListings();
        return new api_response_dto_1.ApiResponseDto(true, "Popular listings retrieved", popular);
    }
};
exports.SearchController = SearchController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Search hotels, guides, and packages" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Search results retrieved successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "search", null);
__decorate([
    (0, common_1.Get)("hotels"),
    (0, swagger_1.ApiOperation)({ summary: "Search hotels only" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "searchHotels", null);
__decorate([
    (0, common_1.Get)("guides"),
    (0, swagger_1.ApiOperation)({ summary: "Search guides only" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "searchGuides", null);
__decorate([
    (0, common_1.Get)("packages"),
    (0, swagger_1.ApiOperation)({ summary: "Search packages only" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "searchPackages", null);
__decorate([
    (0, common_1.Get)("suggestions"),
    (0, swagger_1.ApiOperation)({ summary: "Get search suggestions" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "getSuggestions", null);
__decorate([
    (0, common_1.Get)("locations"),
    (0, swagger_1.ApiOperation)({ summary: "Search locations" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "searchLocations", null);
__decorate([
    (0, common_1.Get)("filters"),
    (0, swagger_1.ApiOperation)({ summary: "Get available filters" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "getFilters", null);
__decorate([
    (0, common_1.Get)("popular"),
    (0, swagger_1.ApiOperation)({ summary: "Get popular destinations and listings" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SearchController.prototype, "getPopular", null);
exports.SearchController = SearchController = __decorate([
    (0, swagger_1.ApiTags)("Search"),
    (0, common_1.Controller)("search"),
    __metadata("design:paramtypes", [Function])
], SearchController);
//# sourceMappingURL=search.controller.js.map