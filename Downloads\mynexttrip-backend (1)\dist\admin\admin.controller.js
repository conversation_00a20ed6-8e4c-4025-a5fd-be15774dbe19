"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const api_response_dto_1 = require("../common/dto/api-response.dto");
let AdminController = class AdminController {
    constructor(adminService) {
        this.adminService = adminService;
    }
    async getDashboard() {
        const dashboard = await this.adminService.getDashboardOverview();
        return new api_response_dto_1.ApiResponseDto(true, "Dashboard data retrieved successfully", dashboard);
    }
    async getUsers(query) {
        const users = await this.adminService.getUsers(query);
        return new api_response_dto_1.ApiResponseDto(true, "Users retrieved successfully", users);
    }
    async getUserById(id) {
        const user = await this.adminService.getUserById(id);
        return new api_response_dto_1.ApiResponseDto(true, "User details retrieved successfully", user);
    }
    async updateUser(id, updateDto) {
        const user = await this.adminService.updateUser(id, updateDto);
        return new api_response_dto_1.ApiResponseDto(true, "User updated successfully", user);
    }
    async suspendUser(id, body) {
        const result = await this.adminService.suspendUser(id, body.reason, body.duration);
        return new api_response_dto_1.ApiResponseDto(true, "User suspended successfully", result);
    }
    async activateUser(id) {
        const result = await this.adminService.activateUser(id);
        return new api_response_dto_1.ApiResponseDto(true, "User activated successfully", result);
    }
    async getBookings(query) {
        const bookings = await this.adminService.getBookings(query);
        return new api_response_dto_1.ApiResponseDto(true, "Bookings retrieved successfully", bookings);
    }
    async getPayments(query) {
        const payments = await this.adminService.getPayments(query);
        return new api_response_dto_1.ApiResponseDto(true, "Payments retrieved successfully", payments);
    }
    async getDisputes(query) {
        const disputes = await this.adminService.getDisputes(query);
        return new api_response_dto_1.ApiResponseDto(true, "Disputes retrieved successfully", disputes);
    }
    async resolveDispute(id, body) {
        const result = await this.adminService.resolveDispute(id, body.resolution, body.refundAmount);
        return new api_response_dto_1.ApiResponseDto(true, "Dispute resolved successfully", result);
    }
    async getConfig() {
        const config = await this.adminService.getPlatformConfig();
        return new api_response_dto_1.ApiResponseDto(true, "Configuration retrieved successfully", config);
    }
    async updateConfig(configDto) {
        const config = await this.adminService.updatePlatformConfig(configDto);
        return new api_response_dto_1.ApiResponseDto(true, "Configuration updated successfully", config);
    }
    async getRevenueReport(query) {
        const report = await this.adminService.getRevenueReport(query.startDate, query.endDate, query.groupBy);
        return new api_response_dto_1.ApiResponseDto(true, "Revenue report generated successfully", report);
    }
    async getActivityReport(query) {
        const report = await this.adminService.getActivityReport(query.startDate, query.endDate);
        return new api_response_dto_1.ApiResponseDto(true, "Activity report generated successfully", report);
    }
};
exports.AdminController = AdminController;
__decorate([
    (0, common_1.Get)("dashboard"),
    (0, swagger_1.ApiOperation)({ summary: "Get admin dashboard overview" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Dashboard data retrieved successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)("users"),
    (0, swagger_1.ApiOperation)({ summary: "Get all users with filters" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getUsers", null);
__decorate([
    (0, common_1.Get)("users/:id"),
    (0, swagger_1.ApiOperation)({ summary: "Get user details by ID" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getUserById", null);
__decorate([
    (0, common_1.Put)("users/:id"),
    (0, swagger_1.ApiOperation)({ summary: "Update user details" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Function]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Put)("users/:id/suspend"),
    (0, swagger_1.ApiOperation)({ summary: "Suspend user account" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "suspendUser", null);
__decorate([
    (0, common_1.Put)("users/:id/activate"),
    (0, swagger_1.ApiOperation)({ summary: "Activate suspended user account" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "activateUser", null);
__decorate([
    (0, common_1.Get)("bookings"),
    (0, swagger_1.ApiOperation)({ summary: "Get all bookings with filters" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getBookings", null);
__decorate([
    (0, common_1.Get)("payments"),
    (0, swagger_1.ApiOperation)({ summary: "Get all payments with filters" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getPayments", null);
__decorate([
    (0, common_1.Get)("disputes"),
    (0, swagger_1.ApiOperation)({ summary: "Get all disputes" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getDisputes", null);
__decorate([
    (0, common_1.Put)("disputes/:id/resolve"),
    (0, swagger_1.ApiOperation)({ summary: "Resolve dispute" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "resolveDispute", null);
__decorate([
    (0, common_1.Get)("config"),
    (0, swagger_1.ApiOperation)({ summary: "Get platform configuration" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getConfig", null);
__decorate([
    (0, common_1.Put)("config"),
    (0, swagger_1.ApiOperation)({ summary: "Update platform configuration" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateConfig", null);
__decorate([
    (0, common_1.Get)("reports/revenue"),
    (0, swagger_1.ApiOperation)({ summary: "Get revenue reports" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getRevenueReport", null);
__decorate([
    (0, common_1.Get)("reports/activity"),
    (0, swagger_1.ApiOperation)({ summary: "Get platform activity reports" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getActivityReport", null);
exports.AdminController = AdminController = __decorate([
    (0, swagger_1.ApiTags)("Admin Management"),
    (0, common_1.Controller)("admin"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("admin"),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [Function])
], AdminController);
//# sourceMappingURL=admin.controller.js.map