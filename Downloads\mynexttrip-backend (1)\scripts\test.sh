#!/bin/bash

# MyNextTrip Backend Testing Script

echo "🧪 Running MyNextTrip Backend Tests..."

# Setup test environment
export NODE_ENV=test
export DATABASE_URL="postgresql://test:test@localhost:5433/mynexttrip_test"
export REDIS_URL="redis://localhost:6380"

# Start test infrastructure
echo "🐳 Starting test infrastructure..."
docker-compose -f docker-compose.test.yml up -d

# Wait for services
echo "⏳ Waiting for test services..."
sleep 5

# Run database migrations for test
echo "🗄️ Setting up test database..."
npx prisma migrate deploy

# Run tests
echo "🏃 Running unit tests..."
npm run test

echo "🏃 Running integration tests..."
npm run test:e2e

echo "📊 Generating coverage report..."
npm run test:cov

# Cleanup
echo "🧹 Cleaning up test infrastructure..."
docker-compose -f docker-compose.test.yml down

echo "✅ Testing complete!"
