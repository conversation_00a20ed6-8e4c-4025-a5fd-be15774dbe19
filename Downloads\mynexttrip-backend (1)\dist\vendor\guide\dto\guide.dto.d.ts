import { GuideStatus } from "@prisma/client";
export declare class CreateGuideDto {
    name: string;
    description?: string;
    experience: number;
    languages: string[];
    specializations: string[];
    city: string;
    state: string;
    pricePerDay: number;
    pricePerHour?: number;
    maxGroupSize: number;
    cancellationPolicy?: string;
}
export declare class UpdateGuideDto {
    name?: string;
    description?: string;
    experience?: number;
    languages?: string[];
    specializations?: string[];
    city?: string;
    state?: string;
    pricePerDay?: number;
    pricePerHour?: number;
    maxGroupSize?: number;
    cancellationPolicy?: string;
}
export declare class GuideQueryDto {
    page?: number;
    limit?: number;
    status?: GuideStatus;
    city?: string;
    search?: string;
}
