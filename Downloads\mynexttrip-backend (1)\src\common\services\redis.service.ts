import { Injectable, type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/common"
import type { ConfigService } from "@nestjs/config"
import { createClient, type RedisClientType } from "redis"

@Injectable()
export class RedisService implements OnModuleDestroy {
  private client: RedisClientType

  constructor(private configService: ConfigService) {
    this.client = createClient({
      url: this.configService.get("REDIS_URL") || "redis://localhost:6379",
    })

    this.client.on("error", (err) => {
      console.error("Redis Client Error:", err)
    })

    this.client.connect()
  }

  async onModuleDestroy() {
    await this.client.quit()
  }

  async get(key: string): Promise<string | null> {
    return this.client.get(key)
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.client.setEx(key, ttl, value)
    } else {
      await this.client.set(key, value)
    }
  }

  async del(key: string): Promise<void> {
    await this.client.del(key)
  }

  async exists(key: string): Promise<boolean> {
    return (await this.client.exists(key)) === 1
  }

  async incr(key: string): Promise<number> {
    return this.client.incr(key)
  }

  async expire(key: string, seconds: number): Promise<void> {
    await this.client.expire(key, seconds)
  }

  async hSet(key: string, field: string, value: string): Promise<void> {
    await this.client.hSet(key, field, value)
  }

  async hGet(key: string, field: string): Promise<string | undefined> {
    return this.client.hGet(key, field)
  }

  async hGetAll(key: string): Promise<Record<string, string>> {
    return this.client.hGetAll(key)
  }
}
