import { Modu<PERSON> } from "@nestjs/common"
import { AdminController } from "./admin.controller"
import { AdminService } from "./admin.service"
import { VendorApprovalController } from "./vendor-approval.controller"
import { VendorApprovalService } from "./vendor-approval.service"
import { ListingModerationController } from "./listing-moderation.controller"
import { ListingModerationService } from "./listing-moderation.service"
import { AnalyticsController } from "./analytics.controller"
import { AnalyticsService } from "./analytics.service"
import { PrismaModule } from "../common/modules/prisma.module"
import { RedisModule } from "../common/modules/redis.module"
import { TypesenseModule } from "../common/modules/typesense.module"

@Module({
  imports: [PrismaModule, RedisModule, TypesenseModule],
  controllers: [AdminController, VendorApprovalController, ListingModerationController, AnalyticsController],
  providers: [AdminService, VendorApprovalService, ListingModerationService, AnalyticsService],
  exports: [AdminService],
})
export class AdminModule {}
