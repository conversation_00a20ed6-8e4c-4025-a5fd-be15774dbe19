"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VendorModule = void 0;
const common_1 = require("@nestjs/common");
const vendor_controller_1 = require("./vendor.controller");
const vendor_service_1 = require("./vendor.service");
const hotel_controller_1 = require("./hotel/hotel.controller");
const hotel_service_1 = require("./hotel/hotel.service");
const guide_controller_1 = require("./guide/guide.controller");
const guide_service_1 = require("./guide/guide.service");
const package_controller_1 = require("./package/package.controller");
const package_service_1 = require("./package/package.service");
const prisma_module_1 = require("../common/modules/prisma.module");
const typesense_module_1 = require("../common/modules/typesense.module");
let VendorModule = class VendorModule {
};
exports.VendorModule = VendorModule;
exports.VendorModule = VendorModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, typesense_module_1.TypesenseModule],
        controllers: [vendor_controller_1.VendorController, hotel_controller_1.HotelController, guide_controller_1.GuideController, package_controller_1.PackageController],
        providers: [vendor_service_1.VendorService, hotel_service_1.HotelService, guide_service_1.GuideService, package_service_1.PackageService],
        exports: [vendor_service_1.VendorService, hotel_service_1.HotelService, guide_service_1.GuideService, package_service_1.PackageService],
    })
], VendorModule);
//# sourceMappingURL=vendor.module.js.map