"use client"

import type React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CreditCard, Wallet } from "lucide-react"

interface PaymentMethod {
  id: string
  type: "stripe" | "razorpay"
  name: string
  description: string
  icon: React.ReactNode
  supportedMethods: string[]
  recommended?: boolean
}

interface PaymentMethodSelectorProps {
  onSelect: (method: PaymentMethod) => void
  selectedMethod?: PaymentMethod
  amount: number
  currency: "INR" | "USD"
}

export function PaymentMethodSelector({ onSelect, selectedMethod, amount, currency }: PaymentMethodSelectorProps) {
  const paymentMethods: PaymentMethod[] = [
    {
      id: "razorpay",
      type: "razorpay",
      name: "Razorpay",
      description: "UPI, Cards, Net Banking, Wallets",
      icon: <Wallet className="h-6 w-6" />,
      supportedMethods: ["UPI", "Cards", "Net Banking", "Wallets"],
      recommended: currency === "INR",
    },
    {
      id: "stripe",
      type: "stripe",
      name: "Stripe",
      description: "Credit/Debit Cards, Digital Wallets",
      icon: <CreditCard className="h-6 w-6" />,
      supportedMethods: ["Credit Cards", "Debit Cards", "Apple Pay", "Google Pay"],
      recommended: currency === "USD",
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Choose Payment Method</h3>
        <div className="text-right">
          <p className="text-2xl font-bold text-primary">
            {currency === "INR" ? "₹" : "$"}
            {amount.toLocaleString()}
          </p>
          <p className="text-sm text-muted-foreground">Total Amount</p>
        </div>
      </div>

      <div className="grid gap-4">
        {paymentMethods.map((method) => (
          <Card
            key={method.id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedMethod?.id === method.id ? "ring-2 ring-primary bg-primary/5" : ""
            }`}
            onClick={() => onSelect(method)}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-muted rounded-lg">{method.icon}</div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold">{method.name}</h4>
                    {method.recommended && <Badge className="bg-green-100 text-green-800">Recommended</Badge>}
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{method.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {method.supportedMethods.map((supportedMethod) => (
                      <Badge key={supportedMethod} variant="outline" className="text-xs">
                        {supportedMethod}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex items-center">
                  {selectedMethod?.id === method.id && (
                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full" />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-6 p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <CreditCard className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Secure Payment</span>
        </div>
        <p className="text-xs text-muted-foreground">
          Your payment information is encrypted and secure. We never store your card details.
        </p>
      </div>
    </div>
  )
}
