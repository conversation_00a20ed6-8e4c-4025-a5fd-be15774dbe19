import type { PrismaService } from "../common/services/prisma.service";
import type { CreateVendorDto, UpdateVendorDto, VendorQueryDto } from "./dto/vendor.dto";
export declare class VendorService {
    private prisma;
    constructor(prisma: PrismaService);
    registerVendor(userId: string, createVendorDto: CreateVendorDto): Promise<any>;
    getVendorProfile(userId: string): Promise<any>;
    updateVendorProfile(userId: string, updateVendorDto: UpdateVendorDto): Promise<any>;
    getDashboardStats(userId: string): Promise<{
        totalListings: any;
        totalBookings: any;
        totalEarnings: any;
        pendingBookings: any;
        recentBookings: any;
    }>;
    getVendorBookings(userId: string, query: VendorQueryDto): Promise<{
        bookings: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getVendorEarnings(userId: string, query: VendorQueryDto): Promise<{
        totalEarnings: any;
        totalBookings: any;
        monthlyEarnings: any;
    }>;
}
