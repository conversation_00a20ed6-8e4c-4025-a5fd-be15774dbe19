import type { PrismaService } from "../common/services/prisma.service";
import type { CreateVendorDto, UpdateVendorDto, VendorQueryDto } from "./dto/vendor.dto";
export declare class VendorService {
    private prisma;
    constructor(prisma: PrismaService);
    registerVendor(userId: string, createVendorDto: CreateVendorDto): Promise<{
        description: string | null;
        businessName: string;
        businessType: import(".prisma/client").$Enums.VendorType;
        gstNumber: string | null;
        panNumber: string | null;
        status: import(".prisma/client").$Enums.VendorStatus;
        id: string;
        userId: string;
        logo: string | null;
        businessAddress: import("@prisma/client/runtime/library").JsonValue;
        bankDetails: import("@prisma/client/runtime/library").JsonValue;
        documentsUploaded: boolean;
        verificationNotes: string | null;
        verifiedAt: Date | null;
        commissionRate: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getVendorProfile(userId: string): Promise<{
        description: string | null;
        businessName: string;
        businessType: import(".prisma/client").$Enums.VendorType;
        gstNumber: string | null;
        panNumber: string | null;
        status: import(".prisma/client").$Enums.VendorStatus;
        id: string;
        userId: string;
        logo: string | null;
        businessAddress: import("@prisma/client/runtime/library").JsonValue;
        bankDetails: import("@prisma/client/runtime/library").JsonValue;
        documentsUploaded: boolean;
        verificationNotes: string | null;
        verifiedAt: Date | null;
        commissionRate: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    updateVendorProfile(userId: string, updateVendorDto: UpdateVendorDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
    } & {
        description: string | null;
        businessName: string;
        businessType: import(".prisma/client").$Enums.VendorType;
        gstNumber: string | null;
        panNumber: string | null;
        status: import(".prisma/client").$Enums.VendorStatus;
        id: string;
        userId: string;
        logo: string | null;
        businessAddress: import("@prisma/client/runtime/library").JsonValue;
        bankDetails: import("@prisma/client/runtime/library").JsonValue;
        documentsUploaded: boolean;
        verificationNotes: string | null;
        verifiedAt: Date | null;
        commissionRate: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getDashboardStats(userId: string): Promise<{
        totalListings: any;
        totalBookings: any;
        totalEarnings: any;
        pendingBookings: any;
        recentBookings: any;
    }>;
    getVendorBookings(userId: string, query: VendorQueryDto): Promise<{
        bookings: {
            status: import(".prisma/client").$Enums.BookingStatus;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            availabilitySlotId: string | null;
            checkIn: Date;
            checkOut: Date | null;
            guests: number;
            totalAmount: number;
            currency: string;
            guestDetails: import("@prisma/client/runtime/library").JsonValue;
            specialRequests: string | null;
            paymentIntentId: string | null;
            paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
            confirmationCode: string;
            confirmedAt: Date | null;
            cancelledAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getVendorEarnings(userId: string, query: VendorQueryDto): Promise<{
        totalEarnings: number;
        totalBookings: number;
        monthlyEarnings: unknown;
    }>;
}
