import { ReviewStatus } from "@prisma/client";
export declare class CreateReviewDto {
    listingType: string;
    listingId: string;
    bookingId: string;
    rating: number;
    title: string;
    content: string;
    images?: string[];
}
export declare class UpdateReviewDto {
    rating?: number;
    title?: string;
    content?: string;
    images?: string[];
}
export declare class ReviewQueryDto {
    page?: number;
    limit?: number;
    rating?: number;
    status?: ReviewStatus;
    sortBy?: string;
}
export declare class ReviewModerationDto {
    action: string;
    reason?: string;
}
