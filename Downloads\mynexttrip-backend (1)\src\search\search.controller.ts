import { Controller, Get } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger"
import type { SearchService } from "./search.service"
import type { SearchQueryDto, LocationSearchDto, SuggestionsQueryDto } from "./dto/search.dto"
import { ApiResponseDto } from "../common/dto/api-response.dto"

@ApiTags("Search")
@Controller("search")
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  @Get()
  @ApiOperation({ summary: "Search hotels, guides, and packages" })
  @ApiResponse({ status: 200, description: "Search results retrieved successfully" })
  async search(query: SearchQueryDto) {
    const results = await this.searchService.search(query)
    return new ApiResponseDto(true, "Search results retrieved successfully", results)
  }

  @Get("hotels")
  @ApiOperation({ summary: "Search hotels only" })
  async searchHotels(query: SearchQueryDto) {
    const results = await this.searchService.searchHotels(query)
    return new ApiResponseDto(true, "Hotel search results retrieved", results)
  }

  @Get("guides")
  @ApiOperation({ summary: "Search guides only" })
  async searchGuides(query: SearchQueryDto) {
    const results = await this.searchService.searchGuides(query)
    return new ApiResponseDto(true, "Guide search results retrieved", results)
  }

  @Get("packages")
  @ApiOperation({ summary: "Search packages only" })
  async searchPackages(query: SearchQueryDto) {
    const results = await this.searchService.searchPackages(query)
    return new ApiResponseDto(true, "Package search results retrieved", results)
  }

  @Get("suggestions")
  @ApiOperation({ summary: "Get search suggestions" })
  async getSuggestions(query: SuggestionsQueryDto) {
    const suggestions = await this.searchService.getSuggestions(query.q)
    return new ApiResponseDto(true, "Suggestions retrieved successfully", suggestions)
  }

  @Get("locations")
  @ApiOperation({ summary: "Search locations" })
  async searchLocations(query: LocationSearchDto) {
    const locations = await this.searchService.searchLocations(query.q)
    return new ApiResponseDto(true, "Locations retrieved successfully", locations)
  }

  @Get("filters")
  @ApiOperation({ summary: "Get available filters" })
  async getFilters(query: { type?: string; city?: string }) {
    const filters = await this.searchService.getAvailableFilters(query.type, query.city)
    return new ApiResponseDto(true, "Filters retrieved successfully", filters)
  }

  @Get("popular")
  @ApiOperation({ summary: "Get popular destinations and listings" })
  async getPopular() {
    const popular = await this.searchService.getPopularListings()
    return new ApiResponseDto(true, "Popular listings retrieved", popular)
  }
}
