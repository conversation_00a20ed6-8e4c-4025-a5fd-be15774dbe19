"use client"

import { Star, MapPin, Wifi, Car, Coffee, Waves } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Image from "next/image"

interface Hotel {
  id: string
  name: string
  location: string
  rating: number
  reviewCount: number
  price: number
  originalPrice?: number
  images: string[]
  amenities: string[]
  description: string
}

interface HotelCardProps {
  hotel: Hotel
  onSelect?: (hotel: Hotel) => void
}

const amenityIcons = {
  wifi: Wifi,
  parking: Car,
  restaurant: Coffee,
  pool: Waves,
}

export function HotelCard({ hotel, onSelect }: HotelCardProps) {
  const discount = hotel.originalPrice
    ? Math.round(((hotel.originalPrice - hotel.price) / hotel.originalPrice) * 100)
    : 0

  return (
    <Card
      className="overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer"
      onClick={() => onSelect?.(hotel)}
    >
      <div className="relative">
        <Image
          src={hotel.images[0] || "/placeholder.svg?height=200&width=300"}
          alt={hotel.name}
          width={300}
          height={200}
          className="w-full h-48 object-cover"
        />
        {discount > 0 && (
          <Badge className="absolute top-2 left-2 bg-destructive text-destructive-foreground">{discount}% OFF</Badge>
        )}
      </div>

      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-lg line-clamp-1">{hotel.name}</h3>
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{hotel.rating}</span>
            <span className="text-xs text-muted-foreground">({hotel.reviewCount})</span>
          </div>
        </div>

        <div className="flex items-center gap-1 mb-3">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">{hotel.location}</span>
        </div>

        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{hotel.description}</p>

        <div className="flex items-center gap-2 mb-4">
          {hotel.amenities.slice(0, 4).map((amenity) => {
            const Icon = amenityIcons[amenity.toLowerCase() as keyof typeof amenityIcons]
            return Icon ? <Icon key={amenity} className="h-4 w-4 text-muted-foreground" /> : null
          })}
          {hotel.amenities.length > 4 && (
            <span className="text-xs text-muted-foreground">+{hotel.amenities.length - 4} more</span>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold text-primary">₹{hotel.price.toLocaleString()}</span>
            {hotel.originalPrice && (
              <span className="text-sm text-muted-foreground line-through">
                ₹{hotel.originalPrice.toLocaleString()}
              </span>
            )}
            <span className="text-sm text-muted-foreground">/ night</span>
          </div>
          <Button size="sm">View Details</Button>
        </div>
      </CardContent>
    </Card>
  )
}
