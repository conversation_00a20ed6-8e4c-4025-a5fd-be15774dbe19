{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NEXT_PUBLIC_API_URL": "@next_public_api_url", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url", "GOOGLE_CLIENT_ID": "@google_client_id", "GOOGLE_CLIENT_SECRET": "@google_client_secret", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "@next_public_stripe_publishable_key", "NEXT_PUBLIC_RAZORPAY_KEY_ID": "@next_public_razorpay_key_id"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}