export declare class ApiResponseDto<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: any;
    timestamp: string;
    path: string;
    constructor(success: boolean, message: string, data?: T, error?: any, path?: string);
    static success<T>(message: string, data?: T, path?: string): ApiResponseDto<T>;
    static error(message: string, error?: any, path?: string): ApiResponseDto;
}
