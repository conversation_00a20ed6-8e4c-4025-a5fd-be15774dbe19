"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuideController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
let GuideController = class GuideController {
    constructor(guideService) {
        this.guideService = guideService;
    }
    async createGuide(req, createGuideDto) {
        const guide = await this.guideService.createGuide(req.user.id, createGuideDto);
        return new api_response_dto_1.ApiResponseDto(true, "Guide created successfully", guide);
    }
    async getGuides(req, query) {
        const guides = await this.guideService.getVendorGuides(req.user.id, query);
        return new api_response_dto_1.ApiResponseDto(true, "Guides retrieved successfully", guides);
    }
    async getGuide(req, id) {
        const guide = await this.guideService.getGuideById(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Guide retrieved successfully", guide);
    }
    async updateGuide(req, id, updateGuideDto) {
        const guide = await this.guideService.updateGuide(req.user.id, id, updateGuideDto);
        return new api_response_dto_1.ApiResponseDto(true, "Guide updated successfully", guide);
    }
    async deleteGuide(req, id) {
        await this.guideService.deleteGuide(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Guide deleted successfully");
    }
    async uploadImages(req, id, files) {
        const images = await this.guideService.uploadGuideImages(req.user.id, id, files);
        return new api_response_dto_1.ApiResponseDto(true, "Images uploaded successfully", images);
    }
    async updateStatus(req, id, body) {
        const guide = await this.guideService.updateGuideStatus(req.user.id, id, body.status);
        return new api_response_dto_1.ApiResponseDto(true, "Guide status updated successfully", guide);
    }
    async getAvailability(req, id, query) {
        const availability = await this.guideService.getGuideAvailability(req.user.id, id, query.startDate, query.endDate);
        return new api_response_dto_1.ApiResponseDto(true, "Availability retrieved successfully", availability);
    }
    async updateAvailability(req, id, body) {
        const availability = await this.guideService.updateGuideAvailability(req.user.id, id, body.dates, body.available);
        return new api_response_dto_1.ApiResponseDto(true, "Availability updated successfully", availability);
    }
};
exports.GuideController = GuideController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: "Create new guide listing" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: "Guide created successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "createGuide", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get vendor guides" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "getGuides", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Get guide by ID" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "getGuide", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Update guide" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Function]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "updateGuide", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Delete guide" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "deleteGuide", null);
__decorate([
    (0, common_1.Post)(":id/images"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", 10)),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, swagger_1.ApiOperation)({ summary: "Upload guide images" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Array]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "uploadImages", null);
__decorate([
    (0, common_1.Put)(":id/status"),
    (0, swagger_1.ApiOperation)({ summary: "Update guide status" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Get)(":id/availability"),
    (0, swagger_1.ApiOperation)({ summary: "Get guide availability" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "getAvailability", null);
__decorate([
    (0, common_1.Post)(":id/availability"),
    (0, swagger_1.ApiOperation)({ summary: "Update guide availability" }),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], GuideController.prototype, "updateAvailability", null);
exports.GuideController = GuideController = __decorate([
    (0, swagger_1.ApiTags)("Guide Management"),
    (0, common_1.Controller)("vendor/guides"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("vendor"),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [Function])
], GuideController);
//# sourceMappingURL=guide.controller.js.map