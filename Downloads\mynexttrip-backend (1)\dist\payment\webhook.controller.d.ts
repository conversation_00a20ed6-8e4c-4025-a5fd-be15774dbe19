import type { PaymentService } from "./payment.service";
import type { StripeService } from "./services/stripe.service";
import type { RazorpayService } from "./services/razorpay.service";
import type { PrismaService } from "../common/services/prisma.service";
export declare class WebhookController {
    private paymentService;
    private stripeService;
    private razorpayService;
    private prisma;
    constructor(paymentService: PaymentService, stripeService: StripeService, razorpayService: RazorpayService, prisma: PrismaService);
    handleStripeWebhook(req: any, signature: string): Promise<{
        received: boolean;
    }>;
    handleRazorpayWebhook(req: any, signature: string): Promise<{
        received: boolean;
    }>;
    private handleStripePaymentSuccess;
    private handleStripePaymentFailed;
    private handleStripeRefundCreated;
    private handleStripeRefundUpdated;
    private handleRazorpayPaymentCaptured;
    private handleRazorpayPaymentFailed;
    private handleRazorpayRefundCreated;
    private handleRazorpayRefundProcessed;
    private createVendorPayout;
}
