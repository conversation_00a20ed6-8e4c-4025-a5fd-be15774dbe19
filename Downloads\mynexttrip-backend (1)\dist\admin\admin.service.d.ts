import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { AdminQueryDto, UserManagementDto, PlatformConfigDto } from "./dto/admin.dto";
export declare class AdminService {
    private prisma;
    private redis;
    constructor(prisma: PrismaService, redis: RedisService);
    getDashboardOverview(): Promise<any>;
    getUsers(query: AdminQueryDto): Promise<{
        users: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getUserById(userId: string): Promise<any>;
    updateUser(userId: string, updateDto: UserManagementDto): Promise<any>;
    suspendUser(userId: string, reason: string, duration?: number): Promise<any>;
    activateUser(userId: string): Promise<any>;
    getBookings(query: AdminQueryDto): Promise<{
        bookings: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getPayments(query: AdminQueryDto): Promise<{
        payments: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    getDisputes(query: AdminQueryDto): Promise<{
        disputes: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    resolveDispute(disputeId: string, resolution: string, refundAmount?: number): Promise<any>;
    getPlatformConfig(): Promise<any>;
    updatePlatformConfig(configDto: PlatformConfigDto): Promise<any>;
    getRevenueReport(startDate?: string, endDate?: string, groupBy?: string): Promise<{
        summary: {
            totalRevenue: any;
            platformRevenue: any;
            taxRevenue: any;
            totalTransactions: any;
        };
        revenueByPeriod: any;
        revenueByGateway: any;
        topVendors: any;
    }>;
    getActivityReport(startDate?: string, endDate?: string): Promise<{
        userActivity: any;
        bookingActivity: any;
        listingActivity: any;
    }>;
    private getRecentActivity;
    private getDefaultConfig;
}
