import type { PrismaService } from "../common/services/prisma.service";
import type { RedisService } from "../common/services/redis.service";
import type { AdminQueryDto, UserManagementDto, PlatformConfigDto } from "./dto/admin.dto";
export declare class AdminService {
    private prisma;
    private redis;
    constructor(prisma: PrismaService, redis: RedisService);
    getDashboardOverview(): Promise<any>;
    getUsers(query: AdminQueryDto): Promise<{
        users: {
            status: import(".prisma/client").$Enums.UserStatus;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            email: string;
            phone: string | null;
            password: string | null;
            firstName: string;
            lastName: string;
            avatar: string | null;
            role: import(".prisma/client").$Enums.UserRole;
            emailVerified: boolean;
            phoneVerified: boolean;
            googleId: string | null;
            preferredLanguage: string;
            preferredCurrency: string;
            lastLoginAt: Date | null;
            paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getUserById(userId: string): Promise<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>;
    updateUser(userId: string, updateDto: UserManagementDto): Promise<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>;
    suspendUser(userId: string, reason: string, duration?: number): Promise<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>;
    activateUser(userId: string): Promise<{
        status: import(".prisma/client").$Enums.UserStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        phone: string | null;
        password: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        phoneVerified: boolean;
        googleId: string | null;
        preferredLanguage: string;
        preferredCurrency: string;
        lastLoginAt: Date | null;
        paymentMethods: import(".prisma/client").$Enums.PaymentMethod[];
    }>;
    getBookings(query: AdminQueryDto): Promise<{
        bookings: {
            status: import(".prisma/client").$Enums.BookingStatus;
            id: string;
            userId: string;
            createdAt: Date;
            updatedAt: Date;
            listingId: string;
            availabilitySlotId: string | null;
            checkIn: Date;
            checkOut: Date | null;
            guests: number;
            totalAmount: number;
            currency: string;
            guestDetails: import("@prisma/client/runtime/library").JsonValue;
            specialRequests: string | null;
            paymentIntentId: string | null;
            paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
            confirmationCode: string;
            confirmedAt: Date | null;
            cancelledAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getPayments(query: AdminQueryDto): Promise<{
        payments: {
            status: import(".prisma/client").$Enums.PaymentStatus;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            currency: string;
            bookingId: string;
            amount: number;
            method: import(".prisma/client").$Enums.PaymentMethod;
            gatewayId: string | null;
            gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
            refundAmount: number | null;
            refundReason: string | null;
            refundedAt: Date | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getDisputes(query: AdminQueryDto): Promise<{
        disputes: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    resolveDispute(disputeId: string, resolution: string, refundAmount?: number): Promise<any>;
    getPlatformConfig(): Promise<any>;
    updatePlatformConfig(configDto: PlatformConfigDto): Promise<any>;
    getRevenueReport(startDate?: string, endDate?: string, groupBy?: string): Promise<{
        summary: {
            totalRevenue: any;
            platformRevenue: any;
            taxRevenue: any;
            totalTransactions: any;
        };
        revenueByPeriod: any;
        revenueByGateway: any;
        topVendors: any;
    }>;
    getActivityReport(startDate?: string, endDate?: string): Promise<{
        userActivity: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
            _count: number;
        })[];
        bookingActivity: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.BookingGroupByOutputType, "status"[]> & {
            _count: number;
        })[];
        listingActivity: unknown;
    }>;
    private getRecentActivity;
    private getDefaultConfig;
}
