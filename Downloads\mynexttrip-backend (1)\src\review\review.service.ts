import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from "@nestjs/common"
import type { PrismaService } from "../common/services/prisma.service"
import type { RedisService } from "../common/services/redis.service"
import type { CreateReviewDto, UpdateReviewDto, ReviewQueryDto, ReviewModerationDto } from "./dto/review.dto"
import { ReviewStatus } from "@prisma/client"

@Injectable()
export class ReviewService {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  async createReview(userId: string, createReviewDto: CreateReviewDto) {
    const { listingType, listingId, bookingId, rating, title, content, images } = createReviewDto

    // Verify booking exists and belongs to user
    const booking = await this.prisma.booking.findFirst({
      where: {
        id: bookingId,
        userId,
        status: "COMPLETED",
      },
    })

    if (!booking) {
      throw new NotFoundException("Completed booking not found")
    }

    // Check if review already exists for this booking
    const existingReview = await this.prisma.review.findFirst({
      where: {
        bookingId,
        userId,
      },
    })

    if (existingReview) {
      throw new BadRequestException("Review already exists for this booking")
    }

    // Verify listing matches booking
    const listingMatches =
      (listingType === "hotel" && booking.hotelId === listingId) ||
      (listingType === "guide" && booking.guideId === listingId) ||
      (listingType === "package" && booking.packageId === listingId)

    if (!listingMatches) {
      throw new BadRequestException("Listing does not match booking")
    }

    // Create review
    const review = await this.prisma.review.create({
      data: {
        userId,
        bookingId,
        ...(listingType === "hotel" && { hotelId: listingId }),
        ...(listingType === "guide" && { guideId: listingId }),
        ...(listingType === "package" && { packageId: listingId }),
        rating,
        title,
        content,
        images: images || [],
        status: ReviewStatus.PENDING, // Reviews need moderation
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
          },
        },
        hotel: {
          select: {
            name: true,
          },
        },
        guide: {
          select: {
            name: true,
          },
        },
        package: {
          select: {
            name: true,
          },
        },
      },
    })

    // Update listing average rating
    await this.updateListingRating(listingType, listingId)

    return review
  }

  async getListingReviews(listingId: string, query: ReviewQueryDto) {
    const { page = 1, limit = 10, rating, sortBy = "newest" } = query
    const skip = (page - 1) * limit

    // Determine listing type by checking which table contains the ID
    const [hotel, guide, packageData] = await Promise.all([
      this.prisma.hotel.findUnique({ where: { id: listingId } }),
      this.prisma.guide.findUnique({ where: { id: listingId } }),
      this.prisma.package.findUnique({ where: { id: listingId } }),
    ])

    if (!hotel && !guide && !packageData) {
      throw new NotFoundException("Listing not found")
    }

    const where: any = {
      status: ReviewStatus.APPROVED,
      ...(hotel && { hotelId: listingId }),
      ...(guide && { guideId: listingId }),
      ...(packageData && { packageId: listingId }),
    }

    if (rating) {
      where.rating = rating
    }

    const orderBy = this.getReviewOrderBy(sortBy)

    const [reviews, total, ratingDistribution] = await Promise.all([
      this.prisma.review.findMany({
        where,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              profilePicture: true,
            },
          },
          _count: {
            select: {
              helpfulVotes: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      this.prisma.review.count({ where }),
      this.prisma.review.groupBy({
        by: ["rating"],
        where: {
          status: ReviewStatus.APPROVED,
          ...(hotel && { hotelId: listingId }),
          ...(guide && { guideId: listingId }),
          ...(packageData && { packageId: listingId }),
        },
        _count: true,
      }),
    ])

    const averageRating = hotel?.averageRating || guide?.averageRating || packageData?.averageRating || 0

    return {
      reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
      summary: {
        averageRating,
        totalReviews: total,
        ratingDistribution,
      },
    }
  }

  async getUserReviews(userId: string, query: ReviewQueryDto) {
    const { page = 1, limit = 10, status } = query
    const skip = (page - 1) * limit

    const where: any = {
      userId,
    }

    if (status) {
      where.status = status
    }

    const [reviews, total] = await Promise.all([
      this.prisma.review.findMany({
        where,
        include: {
          hotel: {
            select: {
              name: true,
              city: true,
              state: true,
            },
          },
          guide: {
            select: {
              name: true,
              city: true,
              state: true,
            },
          },
          package: {
            select: {
              name: true,
              city: true,
              state: true,
            },
          },
          _count: {
            select: {
              helpfulVotes: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      this.prisma.review.count({ where }),
    ])

    return {
      reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getReviewById(reviewId: string) {
    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
          },
        },
        hotel: {
          select: {
            name: true,
            city: true,
            state: true,
          },
        },
        guide: {
          select: {
            name: true,
            city: true,
            state: true,
          },
        },
        package: {
          select: {
            name: true,
            city: true,
            state: true,
          },
        },
        _count: {
          select: {
            helpfulVotes: true,
          },
        },
      },
    })

    if (!review) {
      throw new NotFoundException("Review not found")
    }

    return review
  }

  async updateReview(userId: string, reviewId: string, updateReviewDto: UpdateReviewDto) {
    const review = await this.prisma.review.findFirst({
      where: {
        id: reviewId,
        userId,
      },
    })

    if (!review) {
      throw new NotFoundException("Review not found")
    }

    if (review.status === ReviewStatus.APPROVED) {
      throw new BadRequestException("Cannot edit approved reviews")
    }

    const updatedReview = await this.prisma.review.update({
      where: { id: reviewId },
      data: {
        ...updateReviewDto,
        status: ReviewStatus.PENDING, // Reset to pending after edit
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
          },
        },
      },
    })

    // Update listing rating if rating changed
    if (updateReviewDto.rating && updateReviewDto.rating !== review.rating) {
      const listingType = review.hotelId ? "hotel" : review.guideId ? "guide" : "package"
      const listingId = review.hotelId || review.guideId || review.packageId
      await this.updateListingRating(listingType, listingId)
    }

    return updatedReview
  }

  async deleteReview(userId: string, reviewId: string, userRole: string) {
    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    })

    if (!review) {
      throw new NotFoundException("Review not found")
    }

    // Check permissions
    if (userRole !== "admin" && review.userId !== userId) {
      throw new ForbiddenException("Access denied")
    }

    await this.prisma.review.delete({
      where: { id: reviewId },
    })

    // Update listing rating
    const listingType = review.hotelId ? "hotel" : review.guideId ? "guide" : "package"
    const listingId = review.hotelId || review.guideId || review.packageId
    await this.updateListingRating(listingType, listingId)
  }

  async markReviewHelpful(userId: string, reviewId: string) {
    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    })

    if (!review) {
      throw new NotFoundException("Review not found")
    }

    // Check if user already voted
    const existingVote = await this.prisma.reviewHelpfulVote.findFirst({
      where: {
        reviewId,
        userId,
      },
    })

    if (existingVote) {
      // Remove vote
      await this.prisma.reviewHelpfulVote.delete({
        where: { id: existingVote.id },
      })
      return { helpful: false }
    } else {
      // Add vote
      await this.prisma.reviewHelpfulVote.create({
        data: {
          reviewId,
          userId,
        },
      })
      return { helpful: true }
    }
  }

  async reportReview(userId: string, reviewId: string, reason: string) {
    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    })

    if (!review) {
      throw new NotFoundException("Review not found")
    }

    // Check if user already reported this review
    const existingReport = await this.prisma.reviewReport.findFirst({
      where: {
        reviewId,
        reportedBy: userId,
      },
    })

    if (existingReport) {
      throw new BadRequestException("Review already reported by you")
    }

    const report = await this.prisma.reviewReport.create({
      data: {
        reviewId,
        reportedBy: userId,
        reason,
        status: "PENDING",
      },
    })

    return report
  }

  async getPendingReviews(query: ReviewQueryDto) {
    const { page = 1, limit = 20 } = query
    const skip = (page - 1) * limit

    const [reviews, total] = await Promise.all([
      this.prisma.review.findMany({
        where: {
          status: ReviewStatus.PENDING,
        },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          hotel: {
            select: {
              name: true,
            },
          },
          guide: {
            select: {
              name: true,
            },
          },
          package: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "asc",
        },
        skip,
        take: limit,
      }),
      this.prisma.review.count({
        where: {
          status: ReviewStatus.PENDING,
        },
      }),
    ])

    return {
      reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async moderateReview(reviewId: string, moderationDto: ReviewModerationDto) {
    const { action, reason } = moderationDto

    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    })

    if (!review) {
      throw new NotFoundException("Review not found")
    }

    const updatedReview = await this.prisma.review.update({
      where: { id: reviewId },
      data: {
        status: action === "approve" ? ReviewStatus.APPROVED : ReviewStatus.REJECTED,
        moderationReason: reason,
        moderatedAt: new Date(),
      },
    })

    // Update listing rating if approved
    if (action === "approve") {
      const listingType = review.hotelId ? "hotel" : review.guideId ? "guide" : "package"
      const listingId = review.hotelId || review.guideId || review.packageId
      await this.updateListingRating(listingType, listingId)
    }

    return updatedReview
  }

  async getReportedReviews(query: ReviewQueryDto) {
    const { page = 1, limit = 20 } = query
    const skip = (page - 1) * limit

    const [reports, total] = await Promise.all([
      this.prisma.reviewReport.findMany({
        where: {
          status: "PENDING",
        },
        include: {
          review: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              hotel: {
                select: {
                  name: true,
                },
              },
              guide: {
                select: {
                  name: true,
                },
              },
              package: {
                select: {
                  name: true,
                },
              },
            },
          },
          reporter: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "asc",
        },
        skip,
        take: limit,
      }),
      this.prisma.reviewReport.count({
        where: {
          status: "PENDING",
        },
      }),
    ])

    return {
      reports,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  private async updateListingRating(listingType: string, listingId: string) {
    const where: any = {
      status: ReviewStatus.APPROVED,
      ...(listingType === "hotel" && { hotelId: listingId }),
      ...(listingType === "guide" && { guideId: listingId }),
      ...(listingType === "package" && { packageId: listingId }),
    }

    const result = await this.prisma.review.aggregate({
      where,
      _avg: {
        rating: true,
      },
      _count: true,
    })

    const averageRating = result._avg.rating || 0
    const totalReviews = result._count

    // Update the listing
    if (listingType === "hotel") {
      await this.prisma.hotel.update({
        where: { id: listingId },
        data: {
          averageRating,
          totalReviews,
        },
      })
    } else if (listingType === "guide") {
      await this.prisma.guide.update({
        where: { id: listingId },
        data: {
          averageRating,
          totalReviews,
        },
      })
    } else if (listingType === "package") {
      await this.prisma.package.update({
        where: { id: listingId },
        data: {
          averageRating,
          totalReviews,
        },
      })
    }
  }

  private getReviewOrderBy(sortBy: string) {
    switch (sortBy) {
      case "oldest":
        return { createdAt: "asc" }
      case "rating_high":
        return { rating: "desc" }
      case "rating_low":
        return { rating: "asc" }
      case "helpful":
        return { helpfulVotes: { _count: "desc" } }
      default:
        return { createdAt: "desc" }
    }
  }
}
