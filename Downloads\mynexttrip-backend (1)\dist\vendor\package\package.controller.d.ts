import type { PackageService } from "./package.service";
import type { Express } from "express";
export declare class PackageController {
    private readonly packageService;
    constructor(packageService: PackageService);
    createPackage(req: any, createPackageDto: any): Promise<any>;
    getPackages(req: any, query: any): Promise<any>;
    getPackage(req: any, id: string): Promise<any>;
    updatePackage(req: any, id: string, updatePackageDto: any): Promise<any>;
    deletePackage(req: any, id: string): Promise<any>;
    uploadImages(req: any, id: string, files: Express.Multer.File[]): Promise<any>;
    updateStatus(req: any, id: string, body: any): Promise<any>;
}
