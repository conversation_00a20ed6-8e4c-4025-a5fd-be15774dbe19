import type { PackageService } from "./package.service";
import { ApiResponseDto } from "../../common/dto/api-response.dto";
import type { Express } from "express";
export declare class PackageController {
    private readonly packageService;
    constructor(packageService: PackageService);
    createPackage(req: any, createPackageDto: any): Promise<ApiResponseDto<any>>;
    getPackages(req: any, query: any): Promise<ApiResponseDto<{
        packages: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>>;
    getPackage(req: any, id: string): Promise<ApiResponseDto<any>>;
    updatePackage(req: any, id: string, updatePackageDto: any): Promise<ApiResponseDto<any>>;
    deletePackage(req: any, id: string): Promise<ApiResponseDto<any>>;
    uploadImages(req: any, id: string, files: Express.Multer.File[]): Promise<ApiResponseDto<any>>;
    updateStatus(req: any, id: string, body: any): Promise<ApiResponseDto<any>>;
}
