export declare class SearchQueryDto {
    q?: string;
    type?: string;
    city?: string;
    state?: string;
    minPrice?: number;
    maxPrice?: number;
    rating?: number;
    starRating?: number;
    amenities?: string[];
    checkIn?: string;
    checkOut?: string;
    guests?: number;
    page?: number;
    limit?: number;
    sortBy?: string;
}
export declare class SuggestionsQueryDto {
    q: string;
}
export declare class LocationSearchDto {
    q: string;
}
