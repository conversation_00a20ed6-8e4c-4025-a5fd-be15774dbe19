{"version": 3, "file": "package.dto.js", "sourceRoot": "", "sources": ["../../../../src/vendor/package/dto/package.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,6CAAkE;AAClE,qDAAkH;AAClH,yDAAwC;AACxC,2CAAgF;AAEhF,MAAa,gBAAgB;CAoE5B;AApED,4CAoEC;AAhEC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IAC5C,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,CAAC;;8CACH;AAMZ;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,IAAI,CAAC;;qDACI;AAMpB;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAChD,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;kDACQ;AAMhB;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IAClD,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;sDACY;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IAC7C,IAAA,0BAAQ,EAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;IACjC,IAAA,qBAAG,EAAC,CAAC,CAAC;;+CACM;AAKb;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IAC/D,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;oDACL;AAMpB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACvE,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;oDACJ;AAKrB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;;mDACM;AAKf;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,CAAC;;8CACF;AAKZ;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IACrC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,CAAC;;+CACD;AAIb;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,wBAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACvE,IAAA,wBAAM,EAAC,wBAAe,CAAC;kDACZ,wBAAe,oBAAf,wBAAe;oDAAA;AAI3B;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,wBAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACvE,IAAA,wBAAM,EAAC,wBAAe,CAAC;kDACd,wBAAe,oBAAf,wBAAe;kDAAA;AAMzB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,IAAI,CAAC;;4DACW;AAG7B,MAAa,gBAAgB;CA6E5B;AA7ED,4CA6EC;AAxEC;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,CAAC;;8CACF;AAMb;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,IAAI,CAAC;;qDACI;AAOpB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACxD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;kDACS;AAOjB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IAC1D,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;sDACa;AAMrB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;IACjC,IAAA,qBAAG,EAAC,CAAC,CAAC;;+CACO;AAMd;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACvE,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;oDACJ;AAMrB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACvE,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;oDACJ;AAKrB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;;mDACM;AAMf;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,CAAC;;8CACD;AAMb;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IAC7C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,CAAC;;+CACA;AAKd;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,wBAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAC/E,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,wBAAe,CAAC;kDACX,wBAAe,oBAAf,wBAAe;oDAAA;AAK5B;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,wBAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAC/E,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,wBAAe,CAAC;kDACb,wBAAe,oBAAf,wBAAe;kDAAA;AAM1B;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,IAAI,CAAC;;4DACW;AAG7B,MAAa,eAAe;IAA5B;QAME,SAAI,GAAY,CAAC,CAAA;QAQjB,UAAK,GAAY,EAAE,CAAA;IAqBrB,CAAC;CAAA;AAnCD,0CAmCC;AA7BC;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC/D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;;6CACU;AAQjB;IANC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;8CACU;AAKnB;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,sBAAa,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAC7E,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,sBAAa,CAAC;kDACb,sBAAa,oBAAb,sBAAa;+CAAA;AAKtB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACE;AAKb;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,wBAAe,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IACjF,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,wBAAe,CAAC;kDACb,wBAAe,oBAAf,wBAAe;iDAAA;AAK1B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACI"}