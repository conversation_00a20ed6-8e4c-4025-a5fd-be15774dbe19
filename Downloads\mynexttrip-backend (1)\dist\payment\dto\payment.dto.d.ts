import { PaymentGateway, PaymentStatus } from "@prisma/client";
export declare class CreatePaymentDto {
    bookingId: string;
    gateway: PaymentGateway;
    currency?: string;
    savePaymentMethod?: boolean;
}
export declare class RefundPaymentDto {
    amount: number;
    reason: string;
}
export declare class PaymentQueryDto {
    page?: number;
    limit?: number;
    status?: PaymentStatus;
    startDate?: string;
    endDate?: string;
}
