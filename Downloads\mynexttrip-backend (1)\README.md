# MyNextTrip Frontend

Premium travel booking platform frontend for Northern India markets, built with Next.js 14 and modern React patterns.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Backend API running (see backend README)

### Local Development Setup

1. **Clone and Install**
\`\`\`bash
git clone <repository-url>
cd mynexttrip-frontend
npm install
\`\`\`

2. **Environment Setup**
\`\`\`bash
cp .env.local.example .env.local
# Edit .env.local with your configuration
\`\`\`

3. **Start Development Server**
\`\`\`bash
npm run dev
\`\`\`

The application will be available at `http://localhost:3000`

## 🏗️ Architecture

### Tech Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: Zustand
- **API Client**: React Query + Axios
- **Authentication**: NextAuth.js
- **Forms**: React Hook Form + Zod
- **Payments**: Stripe + Razorpay
- **Testing**: Jest + Cypress
- **Deployment**: Vercel

### Key Features
- 🏨 **Hotel Booking**: Search, filter, and book luxury hotels
- 🗺️ **Tour Guides**: Connect with local expert guides
- 📦 **Travel Packages**: Pre-designed travel experiences
- 💳 **Dual Payments**: Stripe (international) + Razorpay (India)
- 🔐 **Authentication**: Email/password + Google OAuth
- 📱 **Responsive**: Mobile-first design
- 🌐 **Internationalization**: English/Hindi support
- ⚡ **Performance**: Optimized loading and caching

## 📱 User Interfaces

### Customer Portal
- **Landing Page**: Hero section with search, featured content
- **Search Results**: Advanced filtering and sorting
- **Listing Details**: Image galleries, amenities, reviews
- **Booking Flow**: Guest details, payment, confirmation
- **User Dashboard**: Booking history, profile management

### Vendor Portal
- **Dashboard**: Performance metrics and quick actions
- **Listings Management**: Create/edit hotels, guides, packages
- **Booking Management**: View and manage reservations
- **Earnings**: Revenue tracking and payout history

### Admin Portal
- **Analytics Dashboard**: Platform-wide metrics
- **User Management**: Customer and vendor administration
- **Content Moderation**: Listing approval and review management
- **Dispute Resolution**: Customer support and conflict handling

## 🎨 Design System

### Colors
- **Primary**: Purple (#8b5cf6) - Trust and luxury
- **Secondary**: Orange (#f97316) - Energy and warmth
- **Neutrals**: White, grays for balance
- **Success/Error**: Standard semantic colors

### Typography
- **Headings**: Geist Sans (modern, clean)
- **Body**: Manrope (readable, friendly)
- **Monospace**: JetBrains Mono for code

### Components
Built with shadcn/ui and custom travel-specific components:
- SearchBar with location autocomplete
- HotelCard with ratings and pricing
- BookingFlow with step indicators
- PaymentSelector for gateway choice

## 🔧 Development Commands

\`\`\`bash
# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server

# Code Quality
npm run lint             # ESLint checking
npm run type-check       # TypeScript validation
npm run format           # Prettier formatting

# Testing
npm run test             # Unit tests with Jest
npm run test:watch       # Watch mode
npm run test:coverage    # Coverage report
npm run e2e              # Cypress E2E tests
npm run e2e:headless     # Headless E2E tests

# Storybook
npm run storybook        # Component documentation
npm run build-storybook  # Build Storybook
\`\`\`

## 🧪 Testing Strategy

### Unit Tests (Jest + React Testing Library)
- Component rendering and interactions
- Custom hooks and utilities
- Form validation and submission
- API integration mocking

### E2E Tests (Cypress)
- Complete booking flow
- Authentication workflows
- Payment processing
- Cross-browser compatibility

### Coverage Requirements
- Minimum 70% code coverage
- Critical paths: 90%+ coverage
- Payment flows: 100% coverage

## 🚀 Deployment

### Vercel (Recommended)
\`\`\`bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
\`\`\`

### Docker
\`\`\`bash
# Build image
docker build -t mynexttrip-frontend .

# Run container
docker run -p 3000:3000 mynexttrip-frontend
\`\`\`

### Environment Variables
\`\`\`env
NEXT_PUBLIC_API_URL=https://api.mynexttrip.com
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://mynexttrip.com
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_live_...
\`\`\`

## 📊 Performance Optimization

- **Image Optimization**: Next.js Image component with WebP
- **Code Splitting**: Automatic route-based splitting
- **Caching**: React Query for API responses
- **Bundle Analysis**: webpack-bundle-analyzer
- **Core Web Vitals**: Optimized for Google metrics

## 🔐 Security Features

- **CSP Headers**: Content Security Policy
- **HTTPS Only**: Secure connections enforced
- **Input Validation**: Zod schemas for all forms
- **XSS Protection**: Sanitized user inputs
- **CSRF Protection**: NextAuth.js built-in protection

## 🌐 Internationalization

- **Languages**: English (default), Hindi
- **Localization**: Currency, dates, numbers
- **RTL Support**: Ready for Arabic/Hebrew
- **Dynamic Loading**: Language-specific bundles

## 📈 Analytics & Monitoring

- **Performance**: Web Vitals tracking
- **User Behavior**: Event tracking
- **Error Monitoring**: Sentry integration
- **A/B Testing**: Feature flag support

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Follow coding standards (ESLint + Prettier)
4. Write tests for new features
5. Commit changes (`git commit -m 'Add amazing feature'`)
6. Push to branch (`git push origin feature/amazing-feature`)
7. Open Pull Request

### Code Standards
- TypeScript strict mode
- ESLint + Prettier configuration
- Conventional commit messages
- Component documentation with Storybook

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**MyNextTrip** - Discover Northern India's hidden gems with luxury and authenticity.
