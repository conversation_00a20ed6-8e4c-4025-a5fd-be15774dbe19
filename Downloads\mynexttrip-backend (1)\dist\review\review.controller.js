"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const api_response_dto_1 = require("../common/dto/api-response.dto");
let ReviewController = class ReviewController {
    constructor(reviewService) {
        this.reviewService = reviewService;
    }
    async createReview(req, createReviewDto) {
        const review = await this.reviewService.createReview(req.user.id, createReviewDto);
        return new api_response_dto_1.ApiResponseDto(true, "Review created successfully", review);
    }
    async getListingReviews(listingId, query) {
        const reviews = await this.reviewService.getListingReviews(listingId, query);
        return new api_response_dto_1.ApiResponseDto(true, "Reviews retrieved successfully", reviews);
    }
    async getUserReviews(req, userId, query) {
        const targetUserId = req.user.role === "admin" ? userId : req.user.id;
        const reviews = await this.reviewService.getUserReviews(targetUserId, query);
        return new api_response_dto_1.ApiResponseDto(true, "Reviews retrieved successfully", reviews);
    }
    async getReview(id) {
        const review = await this.reviewService.getReviewById(id);
        return new api_response_dto_1.ApiResponseDto(true, "Review retrieved successfully", review);
    }
    async updateReview(req, id, updateReviewDto) {
        const review = await this.reviewService.updateReview(req.user.id, id, updateReviewDto);
        return new api_response_dto_1.ApiResponseDto(true, "Review updated successfully", review);
    }
    async deleteReview(req, id) {
        await this.reviewService.deleteReview(req.user.id, id, req.user.role);
        return new api_response_dto_1.ApiResponseDto(true, "Review deleted successfully");
    }
    async markHelpful(req, id) {
        const result = await this.reviewService.markReviewHelpful(req.user.id, id);
        return new api_response_dto_1.ApiResponseDto(true, "Review marked as helpful", result);
    }
    async reportReview(req, id, body) {
        const result = await this.reviewService.reportReview(req.user.id, id, body.reason);
        return new api_response_dto_1.ApiResponseDto(true, "Review reported successfully", result);
    }
    async getPendingReviews(query) {
        const reviews = await this.reviewService.getPendingReviews(query);
        return new api_response_dto_1.ApiResponseDto(true, "Pending reviews retrieved successfully", reviews);
    }
    async moderateReview(id, moderationDto) {
        const review = await this.reviewService.moderateReview(id, moderationDto);
        return new api_response_dto_1.ApiResponseDto(true, "Review moderated successfully", review);
    }
    async getReportedReviews(query) {
        const reports = await this.reviewService.getReportedReviews(query);
        return new api_response_dto_1.ApiResponseDto(true, "Reported reviews retrieved successfully", reports);
    }
};
exports.ReviewController = ReviewController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Create review" }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: "Review created successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Function]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "createReview", null);
__decorate([
    (0, common_1.Get)("listing/:listingId"),
    (0, swagger_1.ApiOperation)({ summary: "Get reviews for a listing" }),
    __param(0, (0, common_1.Param)('listingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Function]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "getListingReviews", null);
__decorate([
    (0, common_1.Get)("user/:userId"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Get user reviews" }),
    __param(1, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Function]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "getUserReviews", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Get review by ID" }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "getReview", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Update review" }),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Function]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "updateReview", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("user", "admin"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Delete review" }),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "deleteReview", null);
__decorate([
    (0, common_1.Post)(":id/helpful"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Mark review as helpful" }),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "markHelpful", null);
__decorate([
    (0, common_1.Post)(":id/report"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("user"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Report inappropriate review" }),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "reportReview", null);
__decorate([
    (0, common_1.Get)("admin/pending"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("admin"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Get pending reviews for moderation" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "getPendingReviews", null);
__decorate([
    (0, common_1.Put)("admin/:id/moderate"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("admin"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Moderate review" }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Function]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "moderateReview", null);
__decorate([
    (0, common_1.Get)("admin/reports"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)("admin"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Get reported reviews" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "getReportedReviews", null);
exports.ReviewController = ReviewController = __decorate([
    (0, swagger_1.ApiTags)("Reviews"),
    (0, common_1.Controller)("reviews"),
    __metadata("design:paramtypes", [Function])
], ReviewController);
//# sourceMappingURL=review.controller.js.map