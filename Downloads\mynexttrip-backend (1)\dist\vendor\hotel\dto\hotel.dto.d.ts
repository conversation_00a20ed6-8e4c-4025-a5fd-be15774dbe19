import { HotelStatus, RoomType } from "@prisma/client";
export declare class CreateRoomDto {
    type: RoomType;
    description?: string;
    maxOccupancy: number;
    basePrice: number;
    amenities?: string[];
    totalRooms: number;
}
export declare class CreateHotelDto {
    name: string;
    description?: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
    latitude: number;
    longitude: number;
    starRating: number;
    amenities?: string[];
    checkInTime: string;
    checkOutTime: string;
    cancellationPolicy?: string;
    rooms?: CreateRoomDto[];
}
export declare class UpdateHotelDto {
    name?: string;
    description?: string;
    address?: string;
    city?: string;
    state?: string;
    pincode?: string;
    latitude?: number;
    longitude?: number;
    starRating?: number;
    amenities?: string[];
    checkInTime?: string;
    checkOutTime?: string;
    cancellationPolicy?: string;
}
export declare class HotelQueryDto {
    page?: number;
    limit?: number;
    status?: HotelStatus;
    city?: string;
    search?: string;
}
