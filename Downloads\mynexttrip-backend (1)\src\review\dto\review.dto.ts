import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsEnum, IsUUID, IsInt, IsArray, Min, <PERSON>, <PERSON> } from "class-validator"
import { Type } from "class-transformer"
import { ReviewStatus } from "@prisma/client"

export class CreateReviewDto {
  @ApiProperty({ description: "Listing type", enum: ["hotel", "guide", "package"] })
  @IsEnum(["hotel", "guide", "package"])
  listingType: string

  @ApiProperty({ description: "Listing ID" })
  @IsUUID()
  listingId: string

  @ApiProperty({ description: "Booking ID" })
  @IsUUID()
  bookingId: string

  @ApiProperty({ description: "Rating (1-5)", minimum: 1, maximum: 5 })
  @IsInt()
  @Min(1)
  @Max(5)
  rating: number

  @ApiProperty({ description: "Review title" })
  @IsString()
  @Length(5, 100)
  title: string

  @ApiProperty({ description: "Review content" })
  @IsString()
  @Length(10, 2000)
  content: string

  @ApiPropertyOptional({ description: "Review images", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[]
}

export class UpdateReviewDto {
  @ApiPropertyOptional({ description: "Rating (1-5)", minimum: 1, maximum: 5 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  rating?: number

  @ApiPropertyOptional({ description: "Review title" })
  @IsOptional()
  @IsString()
  @Length(5, 100)
  title?: string

  @ApiPropertyOptional({ description: "Review content" })
  @IsOptional()
  @IsString()
  @Length(10, 2000)
  content?: string

  @ApiPropertyOptional({ description: "Review images", type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[]
}

export class ReviewQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10

  @ApiPropertyOptional({ description: "Filter by rating" })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(5)
  rating?: number

  @ApiPropertyOptional({ enum: ReviewStatus, description: "Filter by status" })
  @IsOptional()
  @IsEnum(ReviewStatus)
  status?: ReviewStatus

  @ApiPropertyOptional({
    description: "Sort by",
    enum: ["newest", "oldest", "rating_high", "rating_low", "helpful"],
    default: "newest",
  })
  @IsOptional()
  @IsEnum(["newest", "oldest", "rating_high", "rating_low", "helpful"])
  sortBy?: string = "newest"
}

export class ReviewModerationDto {
  @ApiProperty({ description: "Moderation action", enum: ["approve", "reject"] })
  @IsEnum(["approve", "reject"])
  action: string

  @ApiPropertyOptional({ description: "Moderation reason" })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  reason?: string
}
