import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import type { PrismaService } from "../../common/services/prisma.service"
import type { TypesenseService } from "../../common/services/typesense.service"
import type { CreateGuideDto, UpdateGuideDto, GuideQueryDto } from "./dto/guide.dto"
import { GuideStatus } from "@prisma/client"
import type { Express } from "express"

@Injectable()
export class GuideService {
  constructor(
    private prisma: PrismaService,
    private typesense: TypesenseService,
  ) {}

  async createGuide(userId: string, createGuideDto: CreateGuideDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.create({
      data: {
        vendorId: vendor.id,
        name: createGuideDto.name,
        description: createGuideDto.description,
        experience: createGuideDto.experience,
        languages: createGuideDto.languages,
        specializations: createGuideDto.specializations,
        city: createGuideDto.city,
        state: createGuideDto.state,
        pricePerDay: createGuideDto.pricePerDay,
        pricePerHour: createGuideDto.pricePerHour,
        maxGroupSize: createGuideDto.maxGroupSize,
        cancellationPolicy: createGuideDto.cancellationPolicy,
        status: GuideStatus.DRAFT,
      },
      include: {
        images: true,
        vendor: {
          select: {
            businessName: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
      },
    })

    await this.indexGuideInTypesense(guide)
    return guide
  }

  async getVendorGuides(userId: string, query: GuideQueryDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const { page = 1, limit = 10, status, city, search } = query
    const skip = (page - 1) * limit

    const where: any = {
      vendorId: vendor.id,
    }

    if (status) {
      where.status = status
    }

    if (city) {
      where.city = {
        contains: city,
        mode: "insensitive",
      }
    }

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          description: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          specializations: {
            hasSome: [search],
          },
        },
      ]
    }

    const [guides, total] = await Promise.all([
      this.prisma.guide.findMany({
        where,
        include: {
          images: {
            take: 1,
            orderBy: {
              isPrimary: "desc",
            },
          },
          _count: {
            select: {
              bookings: true,
              reviews: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      this.prisma.guide.count({ where }),
    ])

    return {
      guides,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getGuideById(userId: string, guideId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.findFirst({
      where: {
        id: guideId,
        vendorId: vendor.id,
      },
      include: {
        images: {
          orderBy: {
            isPrimary: "desc",
          },
        },
        reviews: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                profilePicture: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
        _count: {
          select: {
            bookings: true,
            reviews: true,
          },
        },
      },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    return guide
  }

  async updateGuide(userId: string, guideId: string, updateGuideDto: UpdateGuideDto) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.findFirst({
      where: {
        id: guideId,
        vendorId: vendor.id,
      },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    const updatedGuide = await this.prisma.guide.update({
      where: { id: guideId },
      data: updateGuideDto,
      include: {
        images: true,
        vendor: {
          select: {
            businessName: true,
            contactPhone: true,
            contactEmail: true,
          },
        },
      },
    })

    await this.indexGuideInTypesense(updatedGuide)
    return updatedGuide
  }

  async deleteGuide(userId: string, guideId: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.findFirst({
      where: {
        id: guideId,
        vendorId: vendor.id,
      },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    const activeBookings = await this.prisma.booking.count({
      where: {
        guideId,
        status: {
          in: ["PENDING", "CONFIRMED"],
        },
      },
    })

    if (activeBookings > 0) {
      throw new BadRequestException("Cannot delete guide with active bookings")
    }

    await this.prisma.guide.delete({
      where: { id: guideId },
    })

    try {
      await this.typesense.client.collections("listings").documents(guideId).delete()
    } catch (error) {
      console.error("Error removing guide from Typesense:", error)
    }
  }

  async uploadGuideImages(userId: string, guideId: string, files: Express.Multer.File[]) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.findFirst({
      where: {
        id: guideId,
        vendorId: vendor.id,
      },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    const images = await Promise.all(
      files.map(async (file, index) => {
        return this.prisma.guideImage.create({
          data: {
            guideId,
            url: `/uploads/guides/${guideId}/${file.filename}`,
            altText: `${guide.name} - Image ${index + 1}`,
            isPrimary: index === 0,
          },
        })
      }),
    )

    return images
  }

  async updateGuideStatus(userId: string, guideId: string, status: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.findFirst({
      where: {
        id: guideId,
        vendorId: vendor.id,
      },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    const updatedGuide = await this.prisma.guide.update({
      where: { id: guideId },
      data: { status: status as GuideStatus },
      include: {
        images: true,
      },
    })

    await this.indexGuideInTypesense(updatedGuide)
    return updatedGuide
  }

  async getGuideAvailability(userId: string, guideId: string, startDate: string, endDate: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.findFirst({
      where: {
        id: guideId,
        vendorId: vendor.id,
      },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    const availability = await this.prisma.guideAvailability.findMany({
      where: {
        guideId,
        date: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      },
      orderBy: {
        date: "asc",
      },
    })

    return availability
  }

  async updateGuideAvailability(userId: string, guideId: string, dates: string[], available: boolean) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const guide = await this.prisma.guide.findFirst({
      where: {
        id: guideId,
        vendorId: vendor.id,
      },
    })

    if (!guide) {
      throw new NotFoundException("Guide not found")
    }

    const availabilityData = dates.map((date) => ({
      guideId,
      date: new Date(date),
      isAvailable: available,
    }))

    // Use upsert to handle existing records
    const results = await Promise.all(
      availabilityData.map((data) =>
        this.prisma.guideAvailability.upsert({
          where: {
            guideId_date: {
              guideId: data.guideId,
              date: data.date,
            },
          },
          update: {
            isAvailable: data.isAvailable,
          },
          create: data,
        }),
      ),
    )

    return results
  }

  private async indexGuideInTypesense(guide: any) {
    try {
      const document = {
        id: guide.id,
        name: guide.name,
        description: guide.description,
        city: guide.city,
        state: guide.state,
        experience: guide.experience,
        languages: guide.languages,
        specializations: guide.specializations,
        pricePerDay: guide.pricePerDay,
        pricePerHour: guide.pricePerHour,
        maxGroupSize: guide.maxGroupSize,
        averageRating: guide.averageRating || 0,
        totalReviews: guide.totalReviews || 0,
        status: guide.status,
        type: "guide",
      }

      await this.typesense.client.collections("listings").documents().upsert(document)
    } catch (error) {
      console.error("Error indexing guide in Typesense:", error)
    }
  }
}
