// Import commands.js using ES2015 syntax:
import "./commands"
import { Cypress, cy } from "cypress"

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Hide fetch/XHR requests from command log
Cypress.on("window:before:load", (win) => {
  cy.stub(win.console, "error").callsFake((msg) => {
    // Hide React hydration warnings in tests
    if (msg.includes("Warning: Text content did not match")) {
      return
    }
    // Show other errors
    cy.task("log", msg)
  })
})
