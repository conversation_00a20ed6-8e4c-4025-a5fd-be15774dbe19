import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
  UseInterceptors,
} from "@nestjs/common"
import { FilesInterceptor } from "@nestjs/platform-express"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from "@nestjs/swagger"
import { JwtAuthGuard } from "../../common/guards/jwt-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles } from "../../common/decorators/roles.decorator"
import type { GuideService } from "./guide.service"
import type { CreateGuideDto, UpdateGuideDto, GuideQueryDto } from "./dto/guide.dto"
import { ApiResponseDto } from "../../common/dto/api-response.dto"
import type { Express } from "express"

@ApiTags("Guide Management")
@Controller("vendor/guides")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles("vendor")
@ApiBearerAuth()
export class GuideController {
  constructor(private readonly guideService: GuideService) {}

  @Post()
  @ApiOperation({ summary: "Create new guide listing" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Guide created successfully" })
  async createGuide(req, createGuideDto: CreateGuideDto) {
    const guide = await this.guideService.createGuide(req.user.id, createGuideDto)
    return new ApiResponseDto(true, "Guide created successfully", guide)
  }

  @Get()
  @ApiOperation({ summary: "Get vendor guides" })
  async getGuides(req, query: GuideQueryDto) {
    const guides = await this.guideService.getVendorGuides(req.user.id, query)
    return new ApiResponseDto(true, "Guides retrieved successfully", guides)
  }

  @Get(":id")
  @ApiOperation({ summary: "Get guide by ID" })
  async getGuide(req, @Param('id', ParseUUIDPipe) id: string) {
    const guide = await this.guideService.getGuideById(req.user.id, id)
    return new ApiResponseDto(true, "Guide retrieved successfully", guide)
  }

  @Put(":id")
  @ApiOperation({ summary: "Update guide" })
  async updateGuide(req, @Param('id', ParseUUIDPipe) id: string, updateGuideDto: UpdateGuideDto) {
    const guide = await this.guideService.updateGuide(req.user.id, id, updateGuideDto)
    return new ApiResponseDto(true, "Guide updated successfully", guide)
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete guide" })
  async deleteGuide(req, @Param('id', ParseUUIDPipe) id: string) {
    await this.guideService.deleteGuide(req.user.id, id)
    return new ApiResponseDto(true, "Guide deleted successfully")
  }

  @Post(":id/images")
  @UseInterceptors(FilesInterceptor("images", 10))
  @ApiConsumes("multipart/form-data")
  @ApiOperation({ summary: "Upload guide images" })
  async uploadImages(req, @Param('id', ParseUUIDPipe) id: string, files: Express.Multer.File[]) {
    const images = await this.guideService.uploadGuideImages(req.user.id, id, files)
    return new ApiResponseDto(true, "Images uploaded successfully", images)
  }

  @Put(":id/status")
  @ApiOperation({ summary: "Update guide status" })
  async updateStatus(req, @Param('id', ParseUUIDPipe) id: string, body: { status: string }) {
    const guide = await this.guideService.updateGuideStatus(req.user.id, id, body.status)
    return new ApiResponseDto(true, "Guide status updated successfully", guide)
  }

  @Get(":id/availability")
  @ApiOperation({ summary: "Get guide availability" })
  async getAvailability(req, @Param('id', ParseUUIDPipe) id: string, query: { startDate: string; endDate: string }) {
    const availability = await this.guideService.getGuideAvailability(req.user.id, id, query.startDate, query.endDate)
    return new ApiResponseDto(true, "Availability retrieved successfully", availability)
  }

  @Post(":id/availability")
  @ApiOperation({ summary: "Update guide availability" })
  async updateAvailability(req, @Param('id', ParseUUIDPipe) id: string, body: { dates: string[]; available: boolean }) {
    const availability = await this.guideService.updateGuideAvailability(req.user.id, id, body.dates, body.available)
    return new ApiResponseDto(true, "Availability updated successfully", availability)
  }
}
