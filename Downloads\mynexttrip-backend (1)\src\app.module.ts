import { Module } from "@nestjs/common"
import { ConfigModule } from "@nestjs/config"
import { ThrottlerModule } from "@nestjs/throttler"
import { APP_GUARD, APP_INTERCEPTOR, APP_FILTER } from "@nestjs/core"

// Common modules
import { PrismaModule } from "./common/modules/prisma.module"
import { RedisModule } from "./common/modules/redis.module"
import { TypesenseModule } from "./common/modules/typesense.module"

// Feature modules
import { AuthModule } from "./modules/auth/auth.module"
import { UsersModule } from "./modules/users/users.module"
import { VendorModule } from "./vendor/vendor.module"
import { SearchModule } from "./search/search.module"
import { BookingModule } from "./booking/booking.module"
import { PaymentModule } from "./payment/payment.module"
import { AdminModule } from "./admin/admin.module"
import { ReviewModule } from "./review/review.module"

// Guards and interceptors
import { JwtAuthGuard } from "./common/guards/jwt-auth.guard"
import { RolesGuard } from "./common/guards/roles.guard"
import { ThrottlerGuard } from "@nestjs/throttler"
import { LoggingInterceptor } from "./common/interceptors/logging.interceptor"
import { TransformInterceptor } from "./common/interceptors/transform.interceptor"
import { AllExceptionsFilter } from "./common/filters/all-exceptions.filter"

// Controllers
import { HealthController } from "./common/controllers/health.controller"

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
    }),

    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: Number.parseInt(process.env.RATE_LIMIT_TTL) || 60000,
        limit: Number.parseInt(process.env.RATE_LIMIT_LIMIT) || 100,
      },
    ]),

    // Common modules
    PrismaModule,
    RedisModule,
    TypesenseModule,

    // Feature modules
    AuthModule,
    UsersModule,
    VendorModule,
    SearchModule,
    BookingModule,
    PaymentModule,
    AdminModule,
    ReviewModule,
  ],
  controllers: [HealthController],
  providers: [
    // Global guards
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    // Global interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    // Global filters
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class AppModule {}
