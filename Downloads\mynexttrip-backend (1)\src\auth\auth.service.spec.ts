import { Test, type TestingModule } from "@nestjs/testing"
import { JwtService } from "@nestjs/jwt"
import { AuthService } from "./auth.service"
import { PrismaService } from "../common/services/prisma.service"
import { RedisService } from "../common/services/redis.service"
import { ConflictException, UnauthorizedException } from "@nestjs/common"
import * as bcrypt from "bcrypt"
import { jest } from "@jest/globals"

describe("AuthService", () => {
  let service: AuthService
  let prismaService: PrismaService
  let jwtService: JwtService
  let redisService: RedisService

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  }

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  }

  const mockRedisService = {
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
  }

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: RedisService, useValue: mockRedisService },
      ],
    }).compile()

    service = module.get<AuthService>(AuthService)
    prismaService = module.get<PrismaService>(PrismaService)
    jwtService = module.get<JwtService>(JwtService)
    redisService = module.get<RedisService>(RedisService)
  })

  describe("register", () => {
    it("should create a new user successfully", async () => {
      const registerDto = {
        email: "<EMAIL>",
        password: "password123",
        firstName: "John",
        lastName: "Doe",
      }

      mockPrismaService.user.findUnique.mockResolvedValue(null)
      mockPrismaService.user.create.mockResolvedValue({
        id: "1",
        email: registerDto.email,
        firstName: registerDto.firstName,
        lastName: registerDto.lastName,
      })

      const result = await service.register(registerDto)

      expect(result).toHaveProperty("user")
      expect(result.user.email).toBe(registerDto.email)
    })

    it("should throw ConflictException if user already exists", async () => {
      const registerDto = {
        email: "<EMAIL>",
        password: "password123",
        firstName: "John",
        lastName: "Doe",
      }

      mockPrismaService.user.findUnique.mockResolvedValue({ id: "1" })

      await expect(service.register(registerDto)).rejects.toThrow(ConflictException)
    })
  })

  describe("login", () => {
    it("should login user successfully", async () => {
      const loginDto = {
        email: "<EMAIL>",
        password: "password123",
      }

      const hashedPassword = await bcrypt.hash(loginDto.password, 10)
      const user = {
        id: "1",
        email: loginDto.email,
        password: hashedPassword,
        role: "USER",
      }

      mockPrismaService.user.findUnique.mockResolvedValue(user)
      mockJwtService.sign.mockReturnValue("jwt-token")

      const result = await service.login(loginDto)

      expect(result).toHaveProperty("accessToken")
      expect(result).toHaveProperty("user")
    })

    it("should throw UnauthorizedException for invalid credentials", async () => {
      const loginDto = {
        email: "<EMAIL>",
        password: "wrongpassword",
      }

      mockPrismaService.user.findUnique.mockResolvedValue(null)

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException)
    })
  })
})
