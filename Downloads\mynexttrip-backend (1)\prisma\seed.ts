import { PrismaClient, UserRole, VendorType, VendorStatus, ListingType, ListingStatus } from "@prisma/client"
import * as bcrypt from "bcryptjs"

const prisma = new PrismaClient()

// Northern India cities and locations
const northernIndiaCities = [
  { name: "Delhi", state: "Delhi", coordinates: { lat: 28.6139, lng: 77.209 } },
  { name: "Gurgaon", state: "Haryana", coordinates: { lat: 28.4595, lng: 77.0266 } },
  { name: "Noida", state: "Uttar Pradesh", coordinates: { lat: 28.5355, lng: 77.391 } },
  { name: "Agra", state: "Uttar Pradesh", coordinates: { lat: 27.1767, lng: 78.0081 } },
  { name: "Jaipur", state: "Rajasthan", coordinates: { lat: 26.9124, lng: 75.7873 } },
  { name: "Udaipur", state: "Rajasthan", coordinates: { lat: 24.5854, lng: 73.7125 } },
  { name: "Jodhpur", state: "Rajasthan", coordinates: { lat: 26.2389, lng: 73.0243 } },
  { name: "<PERSON>m<PERSON>", state: "Himachal Pradesh", coordinates: { lat: 31.1048, lng: 77.1734 } },
  { name: "Manali", state: "Himachal Pradesh", coordinates: { lat: 32.2432, lng: 77.1892 } },
  { name: "Dharamshala", state: "Himachal Pradesh", coordinates: { lat: 32.219, lng: 76.3234 } },
  { name: "Rishikesh", state: "Uttarakhand", coordinates: { lat: 30.0869, lng: 78.2676 } },
  { name: "Haridwar", state: "Uttarakhand", coordinates: { lat: 29.9457, lng: 78.1642 } },
  { name: "Nainital", state: "Uttarakhand", coordinates: { lat: 29.3803, lng: 79.4636 } },
  { name: "Mussoorie", state: "Uttarakhand", coordinates: { lat: 30.4598, lng: 78.0664 } },
  { name: "Amritsar", state: "Punjab", coordinates: { lat: 31.634, lng: 74.8723 } },
  { name: "Chandigarh", state: "Chandigarh", coordinates: { lat: 30.7333, lng: 76.7794 } },
  { name: "Srinagar", state: "Jammu and Kashmir", coordinates: { lat: 34.0837, lng: 74.7973 } },
  { name: "Leh", state: "Ladakh", coordinates: { lat: 34.1526, lng: 77.5771 } },
  { name: "Varanasi", state: "Uttar Pradesh", coordinates: { lat: 25.3176, lng: 82.9739 } },
  { name: "Lucknow", state: "Uttar Pradesh", coordinates: { lat: 26.8467, lng: 80.9462 } },
]

const hotelNames = [
  "Royal Palace",
  "Heritage Haveli",
  "Mountain View Resort",
  "Golden Temple Hotel",
  "Taj Heritage",
  "Oberoi Grand",
  "ITC Maurya",
  "The Leela Palace",
  "Trident Hotel",
  "Radisson Blu",
  "Hyatt Regency",
  "Marriott Hotel",
  "Hilton Garden Inn",
  "Holiday Inn",
  "Best Western",
  "Lemon Tree Hotel",
  "Sarovar Portico",
  "The Park Hotel",
  "Fortune Hotel",
  "Clarks Inn",
  "Hotel Ashoka",
  "The Imperial",
  "Shangri-La",
  "JW Marriott",
]

const guideNames = [
  "Rajesh Kumar",
  "Priya Sharma",
  "Amit Singh",
  "Sunita Devi",
  "Vikram Gupta",
  "Meera Joshi",
  "Ravi Patel",
  "Kavita Agarwal",
  "Suresh Yadav",
  "Pooja Verma",
  "Deepak Mishra",
  "Anita Kumari",
  "Manoj Tiwari",
  "Rekha Sinha",
  "Ashok Pandey",
  "Geeta Rani",
  "Sanjay Dubey",
  "Nisha Gupta",
  "Ramesh Chandra",
  "Usha Devi",
]

const packageTypes = [
  "Golden Triangle Tour",
  "Rajasthan Heritage Tour",
  "Himalayan Adventure",
  "Spiritual Journey",
  "Wildlife Safari",
  "Cultural Experience",
  "Photography Tour",
  "Trekking Expedition",
  "Palace & Fort Tour",
  "Desert Safari",
  "River Rafting Adventure",
  "Yoga Retreat",
  "Food & Culture Tour",
  "Historical Monuments Tour",
  "Hill Station Getaway",
]

const amenities = {
  hotel: ["WiFi", "AC", "Room Service", "Restaurant", "Parking", "Gym", "Pool", "Spa", "Bar", "Laundry"],
  guide: ["Local Expertise", "Multi-language", "Transportation", "Photography", "Historical Knowledge"],
  package: ["Accommodation", "Meals", "Transportation", "Guide", "Entry Tickets", "Insurance"],
}

async function createUsers() {
  console.log("Creating users...")
  const users = []

  // Create admin user
  const adminUser = await prisma.user.create({
    data: {
      email: "<EMAIL>",
      password: await bcrypt.hash("admin123", 10),
      firstName: "Admin",
      lastName: "User",
      role: UserRole.ADMIN,
      status: "ACTIVE",
      emailVerified: true,
    },
  })
  users.push(adminUser)

  // Create regular users
  for (let i = 1; i <= 100; i++) {
    const user = await prisma.user.create({
      data: {
        email: `user${i}@example.com`,
        password: await bcrypt.hash("password123", 10),
        firstName: `User${i}`,
        lastName: "Test",
        role: UserRole.CUSTOMER,
        status: "ACTIVE",
        emailVerified: true,
      },
    })
    users.push(user)
  }

  return users
}

async function createVendorsAndListings(users: any[]) {
  console.log("Creating vendors and listings...")

  // Create hotel vendors and listings
  for (let i = 0; i < 1000; i++) {
    const city = northernIndiaCities[i % northernIndiaCities.length]
    const hotelName = hotelNames[i % hotelNames.length]

    // Create vendor user
    const vendorUser = await prisma.user.create({
      data: {
        email: `hotel${i}@mynexttrip.com`,
        password: await bcrypt.hash("vendor123", 10),
        firstName: "Hotel",
        lastName: `Owner${i}`,
        role: UserRole.VENDOR,
        status: "ACTIVE",
        emailVerified: true,
      },
    })

    // Create vendor
    const vendor = await prisma.vendor.create({
      data: {
        userId: vendorUser.id,
        businessName: `${hotelName} ${city.name}`,
        businessType: VendorType.HOTEL,
        description: `Luxury hotel in the heart of ${city.name}, ${city.state}`,
        status: VendorStatus.APPROVED,
        businessAddress: {
          street: `Hotel Street ${i + 1}`,
          city: city.name,
          state: city.state,
          country: "India",
          pincode: `${110000 + i}`,
        },
        bankDetails: {
          accountNumber: `**********${i}`,
          ifscCode: "HDFC0000001",
          bankName: "HDFC Bank",
          accountHolderName: `${hotelName} ${city.name}`,
        },
        documentsUploaded: true,
        verifiedAt: new Date(),
      },
    })

    // Create hotel listing
    const listing = await prisma.listing.create({
      data: {
        vendorId: vendor.id,
        type: ListingType.HOTEL,
        status: ListingStatus.ACTIVE,
        title: `${hotelName} ${city.name}`,
        description: `Experience luxury and comfort at ${hotelName} in ${city.name}. Our hotel offers world-class amenities and exceptional service.`,
        images: ["/luxury-hotel-exterior.png", "/hotel-room-interior.png", "/hotel-restaurant.png"],
        location: {
          address: `Hotel Street ${i + 1}, ${city.name}`,
          city: city.name,
          state: city.state,
          country: "India",
          coordinates: city.coordinates,
        },
        basePrice: Math.floor(Math.random() * 8000) + 2000, // 2000-10000 INR
        maxCapacity: Math.floor(Math.random() * 4) + 2, // 2-6 people
        amenities: {
          features: amenities.hotel.slice(0, Math.floor(Math.random() * 6) + 4),
        },
        policies: {
          checkIn: "14:00",
          checkOut: "12:00",
          cancellation: "24 hours before check-in",
          pets: false,
        },
        slug: `${hotelName.toLowerCase().replace(/\s+/g, "-")}-${city.name.toLowerCase()}-${i}`,
        tags: ["hotel", "luxury", city.state.toLowerCase(), city.name.toLowerCase()],
        averageRating: Math.floor(Math.random() * 2) + 3.5, // 3.5-5.0
        totalReviews: Math.floor(Math.random() * 100) + 10,
      },
    })

    // Create availability slots for next 90 days
    for (let day = 0; day < 90; day++) {
      const date = new Date()
      date.setDate(date.getDate() + day)

      await prisma.availabilitySlot.create({
        data: {
          listingId: listing.id,
          date: date,
          totalCapacity: 10,
          availableCapacity: Math.floor(Math.random() * 8) + 2,
          price: listing.basePrice + (Math.floor(Math.random() * 2000) - 1000), // ±1000 variation
        },
      })
    }
  }

  // Create tour guide vendors and listings
  for (let i = 0; i < 500; i++) {
    const city = northernIndiaCities[i % northernIndiaCities.length]
    const guideName = guideNames[i % guideNames.length]

    // Create vendor user
    const vendorUser = await prisma.user.create({
      data: {
        email: `guide${i}@mynexttrip.com`,
        password: await bcrypt.hash("vendor123", 10),
        firstName: guideName.split(" ")[0],
        lastName: guideName.split(" ")[1],
        role: UserRole.VENDOR,
        status: "ACTIVE",
        emailVerified: true,
      },
    })

    // Create vendor
    const vendor = await prisma.vendor.create({
      data: {
        userId: vendorUser.id,
        businessName: `${guideName} Tours`,
        businessType: VendorType.TOUR_GUIDE,
        description: `Professional tour guide with 10+ years experience in ${city.name}`,
        status: VendorStatus.APPROVED,
        businessAddress: {
          street: `Guide Street ${i + 1}`,
          city: city.name,
          state: city.state,
          country: "India",
          pincode: `${110000 + i}`,
        },
        bankDetails: {
          accountNumber: `**********${i}`,
          ifscCode: "ICICI0000001",
          bankName: "ICICI Bank",
          accountHolderName: guideName,
        },
        documentsUploaded: true,
        verifiedAt: new Date(),
      },
    })

    // Create guide listing
    const listing = await prisma.listing.create({
      data: {
        vendorId: vendor.id,
        type: ListingType.GUIDE_SERVICE,
        status: ListingStatus.ACTIVE,
        title: `${guideName} - ${city.name} Tour Guide`,
        description: `Explore ${city.name} with an experienced local guide. Discover hidden gems and learn about local culture and history.`,
        images: ["/tour-guide-tourists.png", "/local-attractions-tour.png"],
        location: {
          address: `${city.name} Tourist Center`,
          city: city.name,
          state: city.state,
          country: "India",
          coordinates: city.coordinates,
        },
        basePrice: Math.floor(Math.random() * 3000) + 1000, // 1000-4000 INR per day
        maxCapacity: Math.floor(Math.random() * 8) + 4, // 4-12 people
        amenities: {
          features: amenities.guide.slice(0, Math.floor(Math.random() * 3) + 2),
        },
        policies: {
          duration: "8 hours",
          cancellation: "12 hours before tour",
          languages: ["Hindi", "English"],
        },
        slug: `${guideName.toLowerCase().replace(/\s+/g, "-")}-${city.name.toLowerCase()}-guide-${i}`,
        tags: ["guide", "tour", city.state.toLowerCase(), city.name.toLowerCase()],
        averageRating: Math.floor(Math.random() * 1.5) + 3.5, // 3.5-5.0
        totalReviews: Math.floor(Math.random() * 50) + 5,
      },
    })
  }

  // Create package providers and listings
  for (let i = 0; i < 200; i++) {
    const city = northernIndiaCities[i % northernIndiaCities.length]
    const packageType = packageTypes[i % packageTypes.length]

    // Create vendor user
    const vendorUser = await prisma.user.create({
      data: {
        email: `package${i}@mynexttrip.com`,
        password: await bcrypt.hash("vendor123", 10),
        firstName: "Package",
        lastName: `Provider${i}`,
        role: UserRole.VENDOR,
        status: "ACTIVE",
        emailVerified: true,
      },
    })

    // Create vendor
    const vendor = await prisma.vendor.create({
      data: {
        userId: vendorUser.id,
        businessName: `${packageType} Company`,
        businessType: VendorType.PACKAGE_PROVIDER,
        description: `Specialized in ${packageType.toLowerCase()} packages across Northern India`,
        status: VendorStatus.APPROVED,
        businessAddress: {
          street: `Package Street ${i + 1}`,
          city: city.name,
          state: city.state,
          country: "India",
          pincode: `${110000 + i}`,
        },
        bankDetails: {
          accountNumber: `**********${i}`,
          ifscCode: "**********",
          bankName: "State Bank of India",
          accountHolderName: `${packageType} Company`,
        },
        documentsUploaded: true,
        verifiedAt: new Date(),
      },
    })

    // Create package listing
    const duration = Math.floor(Math.random() * 7) + 3 // 3-10 days
    const listing = await prisma.listing.create({
      data: {
        vendorId: vendor.id,
        type: ListingType.TOUR_PACKAGE,
        status: ListingStatus.ACTIVE,
        title: `${duration}-Day ${packageType}`,
        description: `Comprehensive ${duration}-day ${packageType.toLowerCase()} covering major attractions in Northern India. All-inclusive package with accommodation, meals, and transportation.`,
        images: ["/placeholder-m87v5.png", "/group-tour-activities.png", "/diverse-cultural-experiences.png"],
        location: {
          address: `Starting from ${city.name}`,
          city: city.name,
          state: city.state,
          country: "India",
          coordinates: city.coordinates,
        },
        basePrice: duration * (Math.floor(Math.random() * 5000) + 8000), // 8000-13000 INR per day
        maxCapacity: Math.floor(Math.random() * 15) + 10, // 10-25 people
        amenities: {
          features: amenities.package,
        },
        policies: {
          duration: `${duration} days`,
          cancellation: "7 days before departure",
          includes: ["Accommodation", "Meals", "Transportation", "Guide"],
        },
        slug: `${duration}-day-${packageType.toLowerCase().replace(/\s+/g, "-")}-${i}`,
        tags: ["package", "tour", duration + "-days", city.state.toLowerCase()],
        averageRating: Math.floor(Math.random() * 1.5) + 3.5, // 3.5-5.0
        totalReviews: Math.floor(Math.random() * 30) + 3,
      },
    })
  }
}

async function createSystemConfig() {
  console.log("Creating system configuration...")

  const configs = [
    {
      key: "commission_rates",
      value: {
        hotel: 12.0,
        guide: 8.0,
        package: 15.0,
        experience: 10.0,
      },
    },
    {
      key: "payment_settings",
      value: {
        stripe_enabled: true,
        razorpay_enabled: true,
        wallet_enabled: false,
      },
    },
    {
      key: "booking_settings",
      value: {
        advance_booking_days: 365,
        cancellation_window_hours: 24,
        auto_confirm_bookings: false,
      },
    },
  ]

  for (const config of configs) {
    await prisma.systemConfig.create({
      data: config,
    })
  }
}

async function main() {
  console.log("🌱 Starting seed process...")

  try {
    // Clear existing data
    console.log("Clearing existing data...")
    await prisma.review.deleteMany()
    await prisma.payment.deleteMany()
    await prisma.booking.deleteMany()
    await prisma.availabilitySlot.deleteMany()
    await prisma.listing.deleteMany()
    await prisma.vendor.deleteMany()
    await prisma.notification.deleteMany()
    await prisma.systemConfig.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.user.deleteMany()

    // Create seed data
    const users = await createUsers()
    await createVendorsAndListings(users)
    await createSystemConfig()

    console.log("✅ Seed completed successfully!")
    console.log("📊 Created:")
    console.log("  - 1000 Hotels")
    console.log("  - 500 Tour Guides")
    console.log("  - 200 Tour Packages")
    console.log("  - 100+ Users")
    console.log("  - System Configuration")
  } catch (error) {
    console.error("❌ Seed failed:", error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
