import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsEnum, IsUUID, IsNumber, IsBoolean, Min, Max, Length } from "class-validator"
import { Type } from "class-transformer"
import { PaymentGateway, PaymentStatus } from "@prisma/client"

export class CreatePaymentDto {
  @ApiProperty({ description: "Booking ID" })
  @IsUUID()
  bookingId: string

  @ApiProperty({ enum: PaymentGateway, description: "Payment gateway" })
  @IsEnum(PaymentGateway)
  gateway: PaymentGateway

  @ApiPropertyOptional({ description: "Currency code", default: "INR" })
  @IsOptional()
  @IsString()
  currency?: string = "INR"

  @ApiPropertyOptional({ description: "Save payment method for future use", default: false })
  @IsOptional()
  @IsBoolean()
  savePaymentMethod?: boolean = false
}

export class RefundPaymentDto {
  @ApiProperty({ description: "Refund amount" })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  amount: number

  @ApiProperty({ description: "Refund reason" })
  @IsString()
  @Length(5, 500)
  reason: string
}

export class PaymentQueryDto {
  @ApiPropertyOptional({ description: "Page number", default: 1 })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number = 1

  @ApiPropertyOptional({ description: "Items per page", default: 10 })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  limit?: number = 10

  @ApiPropertyOptional({ enum: PaymentStatus, description: "Filter by status" })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus

  @ApiPropertyOptional({ description: "Start date filter" })
  @IsOptional()
  @IsString()
  startDate?: string

  @ApiPropertyOptional({ description: "End date filter" })
  @IsOptional()
  @IsString()
  endDate?: string
}
