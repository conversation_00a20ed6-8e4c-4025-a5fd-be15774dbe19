import type { ConfigService } from "@nestjs/config";
import { Client as TypesenseClient } from "typesense";
export declare class TypesenseService {
    private configService;
    private client;
    constructor(configService: ConfigService);
    getClient(): TypesenseClient;
    createCollection(schema: any): Promise<void>;
    indexDocument(collectionName: string, document: any): Promise<object>;
    updateDocument(collectionName: string, documentId: string, document: any): Promise<object>;
    deleteDocument(collectionName: string, documentId: string): Promise<object>;
    search(collectionName: string, searchParameters: any): Promise<import("typesense/lib/Typesense/Documents").SearchResponse<object>>;
}
