import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from "@nestjs/common"
import type { PrismaService } from "../common/services/prisma.service"
import type { RedisService } from "../common/services/redis.service"
import type { StripeService } from "./services/stripe.service"
import type { RazorpayService } from "./services/razorpay.service"
import type { CreatePaymentDto, RefundPaymentDto, PaymentQueryDto } from "./dto/payment.dto"
import { PaymentStatus, PaymentGateway } from "@prisma/client"

@Injectable()
export class PaymentService {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
    private stripeService: StripeService,
    private razorpayService: RazorpayService,
  ) {}

  async createPaymentIntent(userId: string, createPaymentDto: CreatePaymentDto) {
    const { bookingId, gateway, currency = "INR", savePaymentMethod = false } = createPaymentDto

    // Get booking details
    const booking = await this.prisma.booking.findFirst({
      where: {
        id: bookingId,
        userId,
        status: "PENDING",
      },
      include: {
        hotel: {
          include: {
            vendor: true,
          },
        },
        guide: {
          include: {
            vendor: true,
          },
        },
        package: {
          include: {
            vendor: true,
          },
        },
        user: true,
      },
    })

    if (!booking) {
      throw new NotFoundException("Booking not found or not eligible for payment")
    }

    // Calculate amounts
    const subtotal = booking.totalAmount
    const taxAmount = subtotal * 0.18 // 18% GST
    const platformFee = subtotal * 0.05 // 5% platform commission
    const totalAmount = subtotal + taxAmount + platformFee

    // Create payment record
    const payment = await this.prisma.payment.create({
      data: {
        bookingId,
        userId,
        gateway,
        currency,
        amount: totalAmount,
        subtotal,
        taxAmount,
        platformFee,
        status: PaymentStatus.PENDING,
        paymentReference: this.generatePaymentReference(),
      },
    })

    // Create payment intent with selected gateway
    let paymentIntent
    if (gateway === PaymentGateway.STRIPE) {
      paymentIntent = await this.stripeService.createPaymentIntent({
        amount: Math.round(totalAmount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        metadata: {
          paymentId: payment.id,
          bookingId,
          userId,
        },
        customer: booking.user.stripeCustomerId,
        setupFutureUsage: savePaymentMethod ? "off_session" : undefined,
      })
    } else if (gateway === PaymentGateway.RAZORPAY) {
      paymentIntent = await this.razorpayService.createOrder({
        amount: Math.round(totalAmount * 100), // Convert to paise
        currency,
        receipt: payment.paymentReference,
        notes: {
          paymentId: payment.id,
          bookingId,
          userId,
        },
      })
    }

    // Update payment with gateway details
    await this.prisma.payment.update({
      where: { id: payment.id },
      data: {
        gatewayPaymentId: paymentIntent.id,
        gatewayResponse: paymentIntent,
      },
    })

    return {
      paymentId: payment.id,
      paymentReference: payment.paymentReference,
      clientSecret: paymentIntent.client_secret || paymentIntent.id,
      amount: totalAmount,
      currency,
      gateway,
      booking: {
        id: booking.id,
        reference: booking.bookingReference,
        checkIn: booking.checkInDate,
        checkOut: booking.checkOutDate,
        guests: booking.guests,
        listing: {
          name: booking.hotel?.name || booking.guide?.name || booking.package?.name,
          type: booking.hotel ? "hotel" : booking.guide ? "guide" : "package",
          location: `${booking.hotel?.city || booking.guide?.city || booking.package?.city}, ${
            booking.hotel?.state || booking.guide?.state || booking.package?.state
          }`,
        },
      },
    }
  }

  async processPayment(userId: string, paymentIntentId: string, paymentMethodId?: string) {
    const payment = await this.prisma.payment.findFirst({
      where: {
        gatewayPaymentId: paymentIntentId,
        userId,
      },
      include: {
        booking: {
          include: {
            hotel: { include: { vendor: true } },
            guide: { include: { vendor: true } },
            package: { include: { vendor: true } },
          },
        },
      },
    })

    if (!payment) {
      throw new NotFoundException("Payment not found")
    }

    if (payment.status !== PaymentStatus.PENDING) {
      throw new BadRequestException("Payment is not in pending status")
    }

    try {
      let gatewayResponse
      if (payment.gateway === PaymentGateway.STRIPE) {
        gatewayResponse = await this.stripeService.confirmPaymentIntent(paymentIntentId, paymentMethodId)
      } else if (payment.gateway === PaymentGateway.RAZORPAY) {
        // For Razorpay, payment confirmation happens via webhook
        gatewayResponse = await this.razorpayService.getOrder(paymentIntentId)
      }

      // Update payment status
      const updatedPayment = await this.prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          gatewayResponse,
        },
        include: {
          booking: true,
        },
      })

      // Update booking status
      await this.prisma.booking.update({
        where: { id: payment.bookingId },
        data: {
          status: "CONFIRMED",
          confirmedAt: new Date(),
        },
      })

      // Create vendor payout record
      await this.createVendorPayout(payment)

      return updatedPayment
    } catch (error) {
      // Update payment status to failed
      await this.prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: PaymentStatus.FAILED,
          failureReason: error.message,
        },
      })

      throw new BadRequestException(`Payment failed: ${error.message}`)
    }
  }

  async getUserPayments(userId: string, userRole: string, query: PaymentQueryDto) {
    const { page = 1, limit = 10, status, startDate, endDate } = query
    const skip = (page - 1) * limit

    let where: any = {}

    if (userRole === "user") {
      where.userId = userId
    } else if (userRole === "vendor") {
      // Get vendor's payments through bookings
      const vendor = await this.prisma.vendor.findUnique({
        where: { userId },
      })

      if (!vendor) {
        throw new NotFoundException("Vendor profile not found")
      }

      where = {
        booking: {
          OR: [
            { hotel: { vendorId: vendor.id } },
            { guide: { vendorId: vendor.id } },
            { package: { vendorId: vendor.id } },
          ],
        },
      }
    }

    if (status) {
      where.status = status
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const [payments, total] = await Promise.all([
      this.prisma.payment.findMany({
        where,
        include: {
          booking: {
            include: {
              hotel: {
                select: {
                  name: true,
                  city: true,
                  state: true,
                },
              },
              guide: {
                select: {
                  name: true,
                  city: true,
                  state: true,
                },
              },
              package: {
                select: {
                  name: true,
                  city: true,
                  state: true,
                },
              },
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          refunds: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      this.prisma.payment.count({ where }),
    ])

    return {
      payments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }
  }

  async getPaymentById(userId: string, paymentId: string, userRole: string) {
    const payment = await this.prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        booking: {
          include: {
            hotel: {
              include: {
                vendor: {
                  select: {
                    businessName: true,
                    userId: true,
                  },
                },
              },
            },
            guide: {
              include: {
                vendor: {
                  select: {
                    businessName: true,
                    userId: true,
                  },
                },
              },
            },
            package: {
              include: {
                vendor: {
                  select: {
                    businessName: true,
                    userId: true,
                  },
                },
              },
            },
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        refunds: true,
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    })

    if (!payment) {
      throw new NotFoundException("Payment not found")
    }

    // Check access permissions
    const isOwner = payment.userId === userId
    const isVendor =
      userRole === "vendor" &&
      (payment.booking.hotel?.vendor?.userId === userId ||
        payment.booking.guide?.vendor?.userId === userId ||
        payment.booking.package?.vendor?.userId === userId)
    const isAdmin = userRole === "admin"

    if (!isOwner && !isVendor && !isAdmin) {
      throw new ForbiddenException("Access denied")
    }

    return payment
  }

  async processRefund(userId: string, paymentId: string, refundDto: RefundPaymentDto, userRole: string) {
    const payment = await this.getPaymentById(userId, paymentId, userRole)

    if (payment.status !== PaymentStatus.COMPLETED) {
      throw new BadRequestException("Only completed payments can be refunded")
    }

    const { amount, reason } = refundDto
    const maxRefundAmount = payment.amount - (payment.refunds?.reduce((sum, r) => sum + r.amount, 0) || 0)

    if (amount > maxRefundAmount) {
      throw new BadRequestException("Refund amount exceeds available refund amount")
    }

    try {
      let gatewayRefund
      if (payment.gateway === PaymentGateway.STRIPE) {
        gatewayRefund = await this.stripeService.createRefund({
          paymentIntent: payment.gatewayPaymentId,
          amount: Math.round(amount * 100), // Convert to cents
          reason: "requested_by_customer",
          metadata: {
            paymentId: payment.id,
            refundReason: reason,
          },
        })
      } else if (payment.gateway === PaymentGateway.RAZORPAY) {
        gatewayRefund = await this.razorpayService.createRefund({
          paymentId: payment.gatewayPaymentId,
          amount: Math.round(amount * 100), // Convert to paise
          notes: {
            paymentId: payment.id,
            refundReason: reason,
          },
        })
      }

      // Create refund record
      const refund = await this.prisma.refund.create({
        data: {
          paymentId: payment.id,
          amount,
          reason,
          status: "PENDING",
          gatewayRefundId: gatewayRefund.id,
          gatewayResponse: gatewayRefund,
          processedBy: userId,
        },
      })

      // Update booking status if full refund
      if (amount === payment.amount) {
        await this.prisma.booking.update({
          where: { id: payment.bookingId },
          data: {
            status: "CANCELLED",
            cancelledAt: new Date(),
            cancellationReason: reason,
          },
        })
      }

      return refund
    } catch (error) {
      throw new BadRequestException(`Refund failed: ${error.message}`)
    }
  }

  async getSavedPaymentMethods(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        stripeCustomerId: true,
        razorpayCustomerId: true,
      },
    })

    if (!user) {
      throw new NotFoundException("User not found")
    }

    const methods = []

    // Get Stripe payment methods
    if (user.stripeCustomerId) {
      const stripeMethods = await this.stripeService.getCustomerPaymentMethods(user.stripeCustomerId)
      methods.push(...stripeMethods.map((method) => ({ ...method, gateway: "STRIPE" })))
    }

    // Get Razorpay payment methods (if supported)
    if (user.razorpayCustomerId) {
      // Razorpay doesn't have a direct API for saved payment methods
      // This would need to be implemented based on your specific requirements
    }

    return methods
  }

  async savePaymentMethod(userId: string, paymentMethodId: string, isDefault = false) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      throw new NotFoundException("User not found")
    }

    // Create or get Stripe customer
    let stripeCustomerId = user.stripeCustomerId
    if (!stripeCustomerId) {
      const customer = await this.stripeService.createCustomer({
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        metadata: {
          userId: user.id,
        },
      })
      stripeCustomerId = customer.id

      await this.prisma.user.update({
        where: { id: userId },
        data: { stripeCustomerId },
      })
    }

    // Attach payment method to customer
    await this.stripeService.attachPaymentMethodToCustomer(paymentMethodId, stripeCustomerId)

    // Set as default if requested
    if (isDefault) {
      await this.stripeService.setDefaultPaymentMethod(stripeCustomerId, paymentMethodId)
    }

    return { success: true, paymentMethodId, isDefault }
  }

  async setDefaultPaymentMethod(userId: string, paymentMethodId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { stripeCustomerId: true },
    })

    if (!user?.stripeCustomerId) {
      throw new NotFoundException("Customer not found")
    }

    await this.stripeService.setDefaultPaymentMethod(user.stripeCustomerId, paymentMethodId)
    return { success: true }
  }

  async getVendorRevenueAnalytics(userId: string, startDate?: string, endDate?: string) {
    const vendor = await this.prisma.vendor.findUnique({
      where: { userId },
    })

    if (!vendor) {
      throw new NotFoundException("Vendor profile not found")
    }

    const where: any = {
      booking: {
        OR: [
          { hotel: { vendorId: vendor.id } },
          { guide: { vendorId: vendor.id } },
          { package: { vendorId: vendor.id } },
        ],
      },
      status: PaymentStatus.COMPLETED,
    }

    if (startDate || endDate) {
      where.paidAt = {}
      if (startDate) where.paidAt.gte = new Date(startDate)
      if (endDate) where.paidAt.lte = new Date(endDate)
    }

    const [totalRevenue, totalPayments, monthlyRevenue, recentPayments] = await Promise.all([
      this.prisma.payment.aggregate({
        where,
        _sum: {
          amount: true,
          subtotal: true,
          platformFee: true,
        },
      }),

      this.prisma.payment.count({ where }),

      this.prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "paidAt") as month,
          SUM(amount) as total_revenue,
          SUM(subtotal) as vendor_revenue,
          SUM("platformFee") as platform_fees,
          COUNT(*) as payment_count
        FROM "Payment" p
        JOIN "Booking" b ON p."bookingId" = b.id
        WHERE p.status = 'COMPLETED'
        AND (
          EXISTS (SELECT 1 FROM "Hotel" h WHERE h.id = b."hotelId" AND h."vendorId" = ${vendor.id}) OR
          EXISTS (SELECT 1 FROM "Guide" g WHERE g.id = b."guideId" AND g."vendorId" = ${vendor.id}) OR
          EXISTS (SELECT 1 FROM "Package" pkg WHERE pkg.id = b."packageId" AND pkg."vendorId" = ${vendor.id})
        )
        ${startDate ? `AND p."paidAt" >= ${startDate}` : ""}
        ${endDate ? `AND p."paidAt" <= ${endDate}` : ""}
        GROUP BY DATE_TRUNC('month', "paidAt")
        ORDER BY month DESC
        LIMIT 12
      `,

      this.prisma.payment.findMany({
        where,
        include: {
          booking: {
            include: {
              hotel: { select: { name: true } },
              guide: { select: { name: true } },
              package: { select: { name: true } },
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: {
          paidAt: "desc",
        },
        take: 10,
      }),
    ])

    return {
      totalRevenue: totalRevenue._sum.amount || 0,
      vendorRevenue: (totalRevenue._sum.subtotal || 0) - (totalRevenue._sum.platformFee || 0),
      platformFees: totalRevenue._sum.platformFee || 0,
      totalPayments,
      monthlyRevenue,
      recentPayments,
    }
  }

  private async createVendorPayout(payment: any) {
    const vendorRevenue = payment.subtotal - payment.platformFee

    // Create payout record (this would be processed separately)
    await this.prisma.payout.create({
      data: {
        vendorId:
          payment.booking.hotel?.vendorId || payment.booking.guide?.vendorId || payment.booking.package?.vendorId,
        paymentId: payment.id,
        amount: vendorRevenue,
        status: "PENDING",
        scheduledFor: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      },
    })
  }

  private generatePaymentReference(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 8)
    return `PAY${timestamp}${random}`.toUpperCase()
  }
}
